/**
 * Stickara Web Worker Manager
 * Provides a centralized system for creating and managing web workers
 * for background processing tasks.
 */

// Ensure we don't initialize multiple times
if (typeof window.StickaraWorkerManager === 'undefined') {

    // Create the worker manager as an IIFE to encapsulate private variables
    window.StickaraWorkerManager = (function() {
        // Private variables
        const workers = {};
        const taskQueue = {};
        const callbacks = {};
        let workerCounter = 0;

        // Configuration
        const config = {
            maxWorkers: 4, // Maximum number of workers to create
            taskTimeout: 30000, // Default timeout for tasks (30 seconds)
            debug: false // Debug mode (disabled for production)
        };

        /**
         * Log messages when debug mode is enabled
         * @param {...any} args - Arguments to log
         */
        function debugLog(...args) {
            if (config.debug) {
                console.log('[StickaraWorkerManager]', ...args);
            }
        }

        /**
         * Creates a simple fallback worker that just returns empty results for CSP compliance
         * @param {string} workerId - The worker ID
         * @returns {Object} Mock worker object
         */
        function createFallbackWorker(workerId) {
            debugLog(`Creating fallback worker for ${workerId}`);

            const mockWorker = {
                onmessage: null,
                onerror: null,
                postMessage: function(data) {
                    // Simple fallback - just return success with empty data
                    setTimeout(() => {
                        try {
                            if (mockWorker.onmessage) {
                                const { taskId, action } = data;

                                // Return appropriate fallback response based on action
                                let result;
                                switch (action) {
                                    case 'processNoteData':
                                        // Match the expected structure from storage-worker.js
                                        const noteData = data.data?.noteData || data.data || {};
                                        result = {
                                            success: true,
                                            processedData: {
                                                ...noteData,
                                                lastModified: Date.now(),
                                                wordCount: noteData.text ? noteData.text.split(/\s+/).filter(Boolean).length : 0
                                            }
                                        };
                                        break;
                                    case 'processHighlightData':
                                        // Match the expected structure for highlight processing
                                        const highlightData = data.data?.highlightData || data.data || {};
                                        result = {
                                            success: true,
                                            processedData: {
                                                ...highlightData,
                                                lastModified: Date.now()
                                            }
                                        };
                                        break;
                                    case 'compressData':
                                        result = { success: true, compressed: data.data?.input || data.data || '' };
                                        break;
                                    case 'decompressData':
                                        result = { success: true, decompressed: data.data?.input || data.data || '' };
                                        break;
                                    case 'encryptData':
                                        result = { success: true, encrypted: data.data?.input || data.data || '' };
                                        break;
                                    case 'decryptData':
                                        result = { success: true, decrypted: data.data?.input || data.data || '' };
                                        break;
                                    case 'batchProcess':
                                        result = { success: true, processedItems: data.data?.items || [] };
                                        break;
                                    case 'convertFormat':
                                        // For image format conversion, just return the original image
                                        result = {
                                            success: true,
                                            convertedDataUrl: data.data?.imageDataUrl || data.data?.input || null,
                                            format: data.data?.format || 'image/png'
                                        };
                                        break;
                                    case 'processImage':
                                        // For image processing, just return the original image
                                        result = {
                                            success: true,
                                            processedDataUrl: data.data?.imageDataUrl || data.data?.input || null,
                                            width: data.data?.width || 0,
                                            height: data.data?.height || 0
                                        };
                                        break;
                                    case 'resizeImage':
                                        // For image resizing, just return the original image
                                        result = {
                                            success: true,
                                            resizedDataUrl: data.data?.imageDataUrl || data.data?.input || null,
                                            width: data.data?.width || 0,
                                            height: data.data?.height || 0
                                        };
                                        break;
                                    case 'applyFilter':
                                        // For image filtering, just return the original image
                                        result = {
                                            success: true,
                                            filteredDataUrl: data.data?.imageDataUrl || data.data?.input || null,
                                            filter: data.data?.filter || 'none'
                                        };
                                        break;
                                    case 'addOverlays':
                                        // For overlay addition, just return the original image
                                        result = {
                                            success: true,
                                            overlaidDataUrl: data.data?.imageDataUrl || data.data?.input || null
                                        };
                                        break;
                                    default:
                                        result = { success: true, data: null };
                                }

                                mockWorker.onmessage({
                                    data: {
                                        taskId: taskId,
                                        result: result,
                                        error: null
                                    }
                                });
                            }
                        } catch (error) {
                            debugLog(`Fallback worker error: ${error.message}`);
                            if (mockWorker.onerror) {
                                mockWorker.onerror({ message: error.message });
                            }
                        }
                    }, 10); // Small delay to simulate async processing
                },
                terminate: function() {
                    debugLog(`Terminating fallback worker ${workerId}`);
                    this.onmessage = null;
                    this.onerror = null;
                }
            };

            return mockWorker;
        }

        /**
         * Creates a new worker with the specified script
         * @param {string} scriptPath - Path to the worker script
         * @param {string} workerId - Optional ID for the worker
         * @returns {string} The worker ID
         */
        function createWorker(scriptPath, workerId = null) {


            // Generate a worker ID if not provided
            const id = workerId || `worker-${++workerCounter}`;

            // Check if we already have a worker with this ID
            if (workers[id]) {
                debugLog(`Worker ${id} already exists`);
                return id;
            }

            try {
                // Create a new worker with proper URL handling
                // First try to fetch the worker script and create a blob URL
                debugLog(`Attempting to create worker for script: ${scriptPath}`);

                // Try different methods to create a worker
                let worker;

                // Method 1: Try using chrome.runtime.getURL directly
                try {
                    const extensionUrl = chrome.runtime.getURL(scriptPath);
                    debugLog(`Creating worker with extension URL: ${extensionUrl}`);
                    worker = new Worker(extensionUrl);
                } catch (e) {
                    debugLog(`Failed to create worker with extension URL: ${e.message}`);

                    // Method 2: Try using inline worker scripts (avoid blob URLs for CSP compliance)
                    try {
                        // Extract the worker name from the path
                        const workerName = scriptPath.split('/').pop().replace('.js', '');

                        // Check if we have an inline worker script for this worker
                        if (window.StickaraInlineWorkers && window.StickaraInlineWorkers[workerName]) {
                            debugLog(`Using inline worker script for ${workerName}`);

                            // For CSP compliance, we'll simulate worker behavior instead of creating actual workers
                            debugLog(`CSP detected, falling back to main thread execution for ${workerName}`);

                            // Create a fallback worker that avoids CSP issues
                            worker = createFallbackWorker(id);

                            // Set up message handler
                            worker.onmessage = function(event) {
                                handleWorkerMessage(id, event);
                            };

                            // Set up error handler
                            worker.onerror = function(error) {
                                // Reduce noise for CSP-related errors
                                if (error.message && error.message.includes('Content Security Policy')) {
                                    debugLog(`CSP-related worker error in ${id}, using fallback`);
                                } else {
                                    console.error(`Error in worker ${id}:`, error);
                                }

                                // Find all pending tasks for this worker and reject them
                                const pendingTasks = taskQueue[id] || [];
                                pendingTasks.forEach(taskId => {
                                    if (callbacks[taskId]) {
                                        callbacks[taskId].reject(new Error(`Worker error: ${error.message || 'Unknown error'}`));
                                        delete callbacks[taskId];
                                    }
                                });

                                // Clean up (no blobUrl for mock workers)
                                taskQueue[id] = [];
                                terminateWorker(id);
                            };

                            // Store the worker
                            workers[id] = {
                                instance: worker,
                                scriptPath: scriptPath,
                                isInline: true,
                                isMock: true,
                                busy: false,
                                lastUsed: Date.now()
                            };

                            debugLog(`Created worker ${id} with inline script`);
                            return id;
                        } else {
                            // Method 3: Fallback - create a simple fallback worker for unsupported scripts
                            debugLog(`No inline worker script found for ${workerName}, creating fallback worker`);

                            worker = createFallbackWorker(id);

                            // Set up message handler
                            worker.onmessage = function(event) {
                                handleWorkerMessage(id, event);
                            };

                            // Set up error handler
                            worker.onerror = function(error) {
                                // Reduce noise for CSP-related errors
                                if (error.message && error.message.includes('Content Security Policy')) {
                                    debugLog(`CSP-related worker error in ${id}, using fallback`);
                                } else {
                                    console.error(`Error in worker ${id}:`, error);
                                }

                                // Find all pending tasks for this worker and reject them
                                const pendingTasks = taskQueue[id] || [];
                                pendingTasks.forEach(taskId => {
                                    if (callbacks[taskId]) {
                                        callbacks[taskId].reject(new Error(`Worker error: ${error.message || 'Unknown error'}`));
                                        delete callbacks[taskId];
                                    }
                                });

                                // Clean up
                                taskQueue[id] = [];
                                terminateWorker(id);
                            };

                            // Store the worker
                            workers[id] = {
                                instance: worker,
                                scriptPath: scriptPath,
                                isMock: true,
                                busy: false,
                                lastUsed: Date.now()
                            };

                            debugLog(`Created fallback mock worker ${id}`);
                            return id;
                        }
                    } catch (error) {
                        console.error(`Failed to create worker ${id} with fallback methods:`, error);
                        throw new Error(`Could not create worker: ${error.message}`);
                    }
                }

                // If we got here, Method 1 worked and we have a worker
                // Initialize task queue for this worker
                taskQueue[id] = [];

                // Set up message handler for Method 1
                worker.onmessage = function(event) {
                    handleWorkerMessage(id, event);
                };

                // Set up error handler for Method 1
                worker.onerror = function(error) {
                    // Reduce noise for CSP-related errors
                    if (error.message && error.message.includes('Content Security Policy')) {
                        debugLog(`CSP-related worker error in ${id}, using fallback`);
                    } else {
                        console.error(`Error in worker ${id}:`, error);
                    }

                    // Find all pending tasks for this worker and reject them
                    const pendingTasks = taskQueue[id] || [];
                    pendingTasks.forEach(taskId => {
                        if (callbacks[taskId]) {
                            callbacks[taskId].reject(new Error(`Worker error: ${error.message || 'Unknown error'}`));
                            delete callbacks[taskId];
                        }
                    });

                    // Clear the task queue
                    taskQueue[id] = [];

                    // Terminate and recreate the worker
                    terminateWorker(id);
                    createWorker(scriptPath, id);
                };

                // Store the worker for Method 1
                workers[id] = {
                    instance: worker,
                    scriptPath: scriptPath,
                    busy: false,
                    lastUsed: Date.now()
                };

                debugLog(`Created worker ${id} with script ${scriptPath}`);
                return id;
            } catch (error) {
                console.error(`Failed to create worker ${id}:`, error);
                throw error;
            }
        }

        /**
         * Handles messages from workers
         * @param {string} workerId - The ID of the worker
         * @param {MessageEvent} event - The message event
         */
        function handleWorkerMessage(workerId, event) {
            const { taskId, result, error, status } = event.data;

            // Check if this is a status update
            if (status === 'ready') {
                debugLog(`Worker ${workerId} is ready`);
                processNextTask(workerId);
                return;
            }

            // Find the callback for this task
            if (callbacks[taskId]) {
                if (error) {
                    callbacks[taskId].reject(new Error(error));
                } else {
                    callbacks[taskId].resolve(result);
                }

                // Remove the callback
                delete callbacks[taskId];

                // Remove the task from the queue if it exists
                if (taskQueue[workerId]) {
                    const index = taskQueue[workerId].indexOf(taskId);
                    if (index !== -1) {
                        taskQueue[workerId].splice(index, 1);
                    }
                }

                // Mark the worker as not busy
                if (workers[workerId]) {
                    workers[workerId].busy = false;
                    workers[workerId].lastUsed = Date.now();
                }

                // Process the next task in the queue
                processNextTask(workerId);
            }
        }

        /**
         * Processes the next task in the queue for a worker
         * @param {string} workerId - The ID of the worker
         */
        function processNextTask(workerId) {
            // Check if the worker exists and is not busy
            if (!workers[workerId] || workers[workerId].busy) {
                return;
            }

            // Check if there are tasks in the queue
            if (taskQueue[workerId] && taskQueue[workerId].length > 0) {
                const taskId = taskQueue[workerId][0];

                // Find the task data
                if (callbacks[taskId] && callbacks[taskId].taskData) {
                    // Mark the worker as busy
                    workers[workerId].busy = true;

                    // Send the task to the worker
                    workers[workerId].instance.postMessage({
                        taskId: taskId,
                        action: callbacks[taskId].taskData.action,
                        data: callbacks[taskId].taskData.data
                    });

                    debugLog(`Sent task ${taskId} to worker ${workerId}`);

                    // Set up a timeout for the task
                    const timeout = callbacks[taskId].taskData.timeout || config.taskTimeout;
                    callbacks[taskId].timeoutId = setTimeout(() => {
                        if (callbacks[taskId]) {
                            callbacks[taskId].reject(new Error(`Task ${taskId} timed out after ${timeout}ms`));
                            delete callbacks[taskId];

                            // Remove the task from the queue if it exists
                            if (taskQueue[workerId]) {
                                const index = taskQueue[workerId].indexOf(taskId);
                                if (index !== -1) {
                                    taskQueue[workerId].splice(index, 1);
                                }
                            }

                            // Mark the worker as not busy
                            if (workers[workerId]) {
                                workers[workerId].busy = false;
                                workers[workerId].lastUsed = Date.now();
                            }

                            // Process the next task in the queue
                            processNextTask(workerId);
                        }
                    }, timeout);
                }
            }
        }

        /**
         * Finds an available worker or creates a new one if needed
         * @param {string} scriptPath - Path to the worker script
         * @returns {string|null} The worker ID or null if creation failed
         */
        function getAvailableWorker(scriptPath) {
            // First, try to find an existing worker with the same script that's not busy
            for (const id in workers) {
                if (workers[id].scriptPath === scriptPath && !workers[id].busy) {
                    return id;
                }
            }

            // If we have fewer than maxWorkers, create a new one
            if (Object.keys(workers).length < config.maxWorkers) {
                try {
                    return createWorker(scriptPath);
                } catch (error) {
                    console.error(`Failed to create new worker:`, error);
                    return null;
                }
            }

            // Otherwise, find the least recently used worker
            let oldestWorkerId = null;
            let oldestTime = Infinity;

            for (const id in workers) {
                if (workers[id].lastUsed < oldestTime) {
                    oldestWorkerId = id;
                    oldestTime = workers[id].lastUsed;
                }
            }

            // Terminate the oldest worker and create a new one
            if (oldestWorkerId) {
                terminateWorker(oldestWorkerId);
                try {
                    return createWorker(scriptPath);
                } catch (error) {
                    console.error(`Failed to create replacement worker:`, error);
                    return null;
                }
            }

            // If we couldn't find a worker to replace, return null
            console.warn('Could not create or find available worker');
            return null;
        }

        /**
         * Terminates a worker
         * @param {string} workerId - The ID of the worker to terminate
         */
        function terminateWorker(workerId) {
            if (workers[workerId]) {
                debugLog(`Terminating worker ${workerId}`);

                // Terminate the worker
                workers[workerId].instance.terminate();

                // Clean up blob URL if it exists
                if (workers[workerId].blobUrl) {
                    debugLog(`Revoking blob URL for worker ${workerId}`);
                    URL.revokeObjectURL(workers[workerId].blobUrl);
                }

                // Remove the worker
                delete workers[workerId];

                // Clear the task queue
                delete taskQueue[workerId];
            }
        }

        /**
         * Runs a task in a worker
         * @param {string} scriptPath - Path to the worker script
         * @param {string} action - The action to perform
         * @param {any} data - The data to pass to the worker
         * @param {Object} options - Options for the task
         * @returns {Promise<any>} A promise that resolves with the result of the task
         */
        function runTask(scriptPath, action, data, options = {}) {
            return new Promise((resolve, reject) => {
                try {
                    // Generate a task ID
                    const taskId = `task-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

                    // Get an available worker
                    const workerId = getAvailableWorker(scriptPath);

                    // Check if worker creation failed (e.g., due to CSP restrictions)
                    if (!workerId) {
                        debugLog('Worker creation failed, falling back to main thread execution');
                        // Fallback to main thread execution
                        setTimeout(() => {
                            try {
                                // Simple fallback - just return the data as-is
                                resolve({ success: true, data: data });
                            } catch (error) {
                                reject(error);
                            }
                        }, 0);
                        return;
                    }

                    // Store the callback
                    callbacks[taskId] = {
                        resolve,
                        reject,
                        taskData: {
                            action,
                            data,
                            timeout: options.timeout || config.taskTimeout
                        }
                    };

                    // Ensure the task queue exists before pushing to it
                    if (!taskQueue[workerId]) {
                        taskQueue[workerId] = [];
                    }

                    // Add the task to the queue
                    taskQueue[workerId].push(taskId);

                    // Process the task if the worker is not busy
                    if (!workers[workerId].busy) {
                        processNextTask(workerId);
                    }
                } catch (error) {
                    reject(error);
                }
            });
        }

        /**
         * Terminates all workers
         */
        function terminateAll() {
            for (const id in workers) {
                terminateWorker(id);
            }
        }

        /**
         * Updates the configuration
         * @param {Object} newConfig - The new configuration
         */
        function updateConfig(newConfig) {
            Object.assign(config, newConfig);
        }

        // Set up cleanup on page unload
        window.addEventListener('beforeunload', terminateAll);

        // Return the public API
        return {
            createWorker,
            runTask,
            terminateWorker,
            terminateAll,
            updateConfig,
            getConfig: () => ({ ...config }),
            getWorkerCount: () => Object.keys(workers).length,
            enableDebug: () => { config.debug = true; },
            disableDebug: () => { config.debug = false; }
        };
    })();

    console.log("Stickara: Worker Manager Loaded");
}
