/**
 * Stickara Image Processing Worker
 * Handles background image processing operations to prevent UI blocking
 */

// Set up worker context
self.isWorkerContext = true;

// Send ready status to main thread
self.postMessage({ status: 'ready' });

// Set up message handler
self.onmessage = function(event) {
    const { taskId, action, data } = event.data;

    try {
        // Process the task based on the action
        switch (action) {
            case 'processImage':
                handleProcessImage(taskId, data);
                break;

            case 'resizeImage':
                handleResizeImage(taskId, data);
                break;

            case 'applyFilter':
                handleApplyFilter(taskId, data);
                break;

            case 'addOverlays':
                handleAddOverlays(taskId, data);
                break;

            case 'convertFormat':
                handleConvertFormat(taskId, data);
                break;

            default:
                throw new Error(`Unknown action: ${action}`);
        }
    } catch (error) {
        // Send error back to main thread
        self.postMessage({
            taskId,
            error: error.message || 'Unknown error in image worker'
        });
    }
};

/**
 * Creates an offscreen canvas for image processing
 * @param {number} width - The width of the canvas
 * @param {number} height - The height of the canvas
 * @returns {Object} The canvas and context
 */
function createCanvas(width, height) {
    let canvas, ctx;

    // Use OffscreenCanvas if available
    if (typeof OffscreenCanvas !== 'undefined') {
        canvas = new OffscreenCanvas(width, height);
        // Set willReadFrequently to true to optimize for multiple getImageData calls
        ctx = canvas.getContext('2d', { willReadFrequently: true });
    } else {
        // This shouldn't happen in a worker, but just in case
        throw new Error('OffscreenCanvas not supported in this worker context');
    }

    return { canvas, ctx };
}

/**
 * Loads an image from a data URL
 * @param {string} dataUrl - The data URL of the image
 * @returns {Promise<ImageBitmap>} A promise that resolves with the image
 */
function loadImage(dataUrl) {
    return new Promise((resolve, reject) => {
        // Create a blob from the data URL
        const byteString = atob(dataUrl.split(',')[1]);
        const mimeType = dataUrl.split(',')[0].split(':')[1].split(';')[0];
        const ab = new ArrayBuffer(byteString.length);
        const ia = new Uint8Array(ab);

        for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
        }

        const blob = new Blob([ab], { type: mimeType });

        // Create an image bitmap from the blob
        createImageBitmap(blob)
            .then(imageBitmap => resolve(imageBitmap))
            .catch(error => reject(error));
    });
}

/**
 * Converts a canvas to a data URL
 * @param {OffscreenCanvas} canvas - The canvas to convert
 * @param {string} format - The format to convert to (e.g., 'image/png')
 * @param {number} quality - The quality of the image (0-1)
 * @param {Object} options - Additional options for conversion
 * @returns {Promise<string>} A promise that resolves with the data URL
 */
async function canvasToDataUrl(canvas, format = 'image/png', quality = 0.92, options = {}) {
    // Apply anti-aliasing if specified
    if (options.enableAntiAliasing !== undefined) {
        // Note: We can't directly modify the canvas context's imageSmoothingEnabled here
        // since we've already drawn the image. This would need to be done before drawing.
        // This is handled in the main content script before sending to the worker.
    }

    // Convert the canvas to a blob with the specified quality
    const blob = await canvas.convertToBlob({
        type: format,
        quality: quality
    });

    // Convert the blob to a data URL
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
    });
}

/**
 * Draws text onto a canvas context with basic styling
 * @param {CanvasRenderingContext2D} ctx - The canvas context
 * @param {string} text - The text to draw
 * @param {number} x - The x coordinate
 * @param {number} y - The y coordinate
 * @param {Object} options - Options for the text
 */
function drawTextOnCanvas(ctx, text, x, y, options = {}) {
    const {
        font = '14px Arial',
        color = '#000000',
        shadowColor = 'rgba(255, 255, 255, 0.8)',
        shadowBlur = 3,
        textAlign = 'left',
        textBaseline = 'top'
    } = options;

    ctx.font = font;
    ctx.fillStyle = color;
    ctx.textAlign = textAlign;
    ctx.textBaseline = textBaseline;

    // Apply shadow for better visibility
    ctx.shadowColor = shadowColor;
    ctx.shadowBlur = shadowBlur;
    ctx.shadowOffsetX = 1;
    ctx.shadowOffsetY = 1;

    ctx.fillText(text, x, y);

    // Reset shadow for subsequent drawing
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
}

/**
 * Processes an image (generic processing)
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The data for the task
 */
async function handleProcessImage(taskId, data) {
    try {
        const { imageDataUrl, options } = data;

        // Load the image
        const image = await loadImage(imageDataUrl);

        // Create a canvas
        const { canvas, ctx } = createCanvas(image.width, image.height);

        // Draw the image onto the canvas
        ctx.drawImage(image, 0, 0);

        // Apply any processing based on options
        if (options.grayscale) {
            // Apply grayscale filter
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const pixels = imageData.data;

            for (let i = 0; i < pixels.length; i += 4) {
                const avg = (pixels[i] + pixels[i + 1] + pixels[i + 2]) / 3;
                pixels[i] = avg;     // Red
                pixels[i + 1] = avg; // Green
                pixels[i + 2] = avg; // Blue
            }

            ctx.putImageData(imageData, 0, 0);
        }

        if (options.brightness !== undefined) {
            // Apply brightness adjustment
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const pixels = imageData.data;
            const factor = options.brightness;

            for (let i = 0; i < pixels.length; i += 4) {
                pixels[i] = Math.min(255, pixels[i] * factor);       // Red
                pixels[i + 1] = Math.min(255, pixels[i + 1] * factor); // Green
                pixels[i + 2] = Math.min(255, pixels[i + 2] * factor); // Blue
            }

            ctx.putImageData(imageData, 0, 0);
        }

        // Convert the canvas back to a data URL
        const processedDataUrl = await canvasToDataUrl(
            canvas,
            options.format || 'image/png',
            options.quality || 0.92,
            { enableAntiAliasing: options.enableAntiAliasing }
        );

        // Send the result back to the main thread
        self.postMessage({
            taskId,
            result: {
                processedDataUrl,
                width: canvas.width,
                height: canvas.height,
                success: true
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Image processing error: ${error.message}`
        });
    }
}

/**
 * Resizes an image
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The data for the task
 */
async function handleResizeImage(taskId, data) {
    try {
        const { imageDataUrl, width, height, options } = data;

        // Load the image
        const image = await loadImage(imageDataUrl);

        // Calculate new dimensions if only one dimension is provided
        let newWidth = width;
        let newHeight = height;

        if (width && !height) {
            // Calculate height to maintain aspect ratio
            newHeight = Math.round(image.height * (width / image.width));
        } else if (!width && height) {
            // Calculate width to maintain aspect ratio
            newWidth = Math.round(image.width * (height / image.height));
        }

        // Create a canvas with the new dimensions
        const { canvas, ctx } = createCanvas(newWidth, newHeight);

        // Draw the image onto the canvas with the new dimensions
        ctx.drawImage(image, 0, 0, newWidth, newHeight);

        // Convert the canvas back to a data URL
        const resizedDataUrl = await canvasToDataUrl(
            canvas,
            options?.format || 'image/png',
            options?.quality || 0.92,
            { enableAntiAliasing: options?.enableAntiAliasing }
        );

        // Send the result back to the main thread
        self.postMessage({
            taskId,
            result: {
                resizedDataUrl,
                width: newWidth,
                height: newHeight,
                success: true
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Image resize error: ${error.message}`
        });
    }
}

/**
 * Applies a filter to an image
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The data for the task
 */
async function handleApplyFilter(taskId, data) {
    try {
        const { imageDataUrl, filter, options } = data;

        // Load the image
        const image = await loadImage(imageDataUrl);

        // Create a canvas
        const { canvas, ctx } = createCanvas(image.width, image.height);

        // Draw the image onto the canvas
        ctx.drawImage(image, 0, 0);

        // Apply the filter
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const pixels = imageData.data;

        switch (filter) {
            case 'grayscale':
                for (let i = 0; i < pixels.length; i += 4) {
                    const avg = (pixels[i] + pixels[i + 1] + pixels[i + 2]) / 3;
                    pixels[i] = avg;     // Red
                    pixels[i + 1] = avg; // Green
                    pixels[i + 2] = avg; // Blue
                }
                break;

            case 'sepia':
                for (let i = 0; i < pixels.length; i += 4) {
                    const r = pixels[i];
                    const g = pixels[i + 1];
                    const b = pixels[i + 2];

                    pixels[i] = Math.min(255, (r * 0.393) + (g * 0.769) + (b * 0.189));     // Red
                    pixels[i + 1] = Math.min(255, (r * 0.349) + (g * 0.686) + (b * 0.168)); // Green
                    pixels[i + 2] = Math.min(255, (r * 0.272) + (g * 0.534) + (b * 0.131)); // Blue
                }
                break;

            case 'invert':
                for (let i = 0; i < pixels.length; i += 4) {
                    pixels[i] = 255 - pixels[i];         // Red
                    pixels[i + 1] = 255 - pixels[i + 1]; // Green
                    pixels[i + 2] = 255 - pixels[i + 2]; // Blue
                }
                break;

            case 'blur':
                // Simple box blur (not efficient, but works for demonstration)
                const tempData = new Uint8ClampedArray(pixels.length);
                tempData.set(pixels);

                const blurRadius = options?.blurRadius || 5;

                for (let y = 0; y < canvas.height; y++) {
                    for (let x = 0; x < canvas.width; x++) {
                        let r = 0, g = 0, b = 0, a = 0, count = 0;

                        // Sample the surrounding pixels
                        for (let ky = -blurRadius; ky <= blurRadius; ky++) {
                            for (let kx = -blurRadius; kx <= blurRadius; kx++) {
                                const px = x + kx;
                                const py = y + ky;

                                if (px >= 0 && px < canvas.width && py >= 0 && py < canvas.height) {
                                    const i = (py * canvas.width + px) * 4;
                                    r += tempData[i];
                                    g += tempData[i + 1];
                                    b += tempData[i + 2];
                                    a += tempData[i + 3];
                                    count++;
                                }
                            }
                        }

                        // Calculate the average
                        const i = (y * canvas.width + x) * 4;
                        pixels[i] = r / count;
                        pixels[i + 1] = g / count;
                        pixels[i + 2] = b / count;
                        pixels[i + 3] = a / count;
                    }
                }
                break;

            default:
                throw new Error(`Unknown filter: ${filter}`);
        }

        ctx.putImageData(imageData, 0, 0);

        // Convert the canvas back to a data URL
        const filteredDataUrl = await canvasToDataUrl(
            canvas,
            options?.format || 'image/png',
            options?.quality || 0.92,
            { enableAntiAliasing: options?.enableAntiAliasing }
        );

        // Send the result back to the main thread
        self.postMessage({
            taskId,
            result: {
                filteredDataUrl,
                filter,
                success: true
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Filter application error: ${error.message}`
        });
    }
}

/**
 * Adds overlays to an image
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The data for the task
 */
async function handleAddOverlays(taskId, data) {
    try {
        const { imageDataUrl, overlays, options } = data;

        // Load the image
        const image = await loadImage(imageDataUrl);

        // Create a canvas
        const { canvas, ctx } = createCanvas(image.width, image.height);

        // Draw the image onto the canvas
        ctx.drawImage(image, 0, 0);

        // Add overlays
        if (Array.isArray(overlays)) {
            for (const overlay of overlays) {
                switch (overlay.type) {
                    case 'text':
                        // Add text overlay
                        drawTextOnCanvas(
                            ctx,
                            overlay.text,
                            overlay.x || 10,
                            overlay.y || 10,
                            overlay.options || {}
                        );
                        break;

                    case 'image':
                        // Add image overlay
                        if (overlay.imageDataUrl) {
                            const overlayImage = await loadImage(overlay.imageDataUrl);
                            ctx.drawImage(
                                overlayImage,
                                overlay.x || 0,
                                overlay.y || 0,
                                overlay.width || overlayImage.width,
                                overlay.height || overlayImage.height
                            );
                        }
                        break;

                    case 'rectangle':
                        // Add rectangle overlay
                        ctx.fillStyle = overlay.fillStyle || 'rgba(255, 255, 255, 0.5)';
                        ctx.strokeStyle = overlay.strokeStyle || '#000000';
                        ctx.lineWidth = overlay.lineWidth || 1;

                        if (overlay.fill !== false) {
                            ctx.fillRect(
                                overlay.x || 0,
                                overlay.y || 0,
                                overlay.width || 100,
                                overlay.height || 100
                            );
                        }

                        if (overlay.stroke !== false) {
                            ctx.strokeRect(
                                overlay.x || 0,
                                overlay.y || 0,
                                overlay.width || 100,
                                overlay.height || 100
                            );
                        }
                        break;


                }
            }
        }

        // Convert the canvas back to a data URL
        const overlaidDataUrl = await canvasToDataUrl(
            canvas,
            options?.format || 'image/png',
            options?.quality || 0.92,
            { enableAntiAliasing: options?.enableAntiAliasing }
        );

        // Send the result back to the main thread
        self.postMessage({
            taskId,
            result: {
                overlaidDataUrl,
                success: true
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Overlay application error: ${error.message}`
        });
    }
}

/**
 * Converts an image to a different format
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The data for the task
 */
async function handleConvertFormat(taskId, data) {
    try {
        const { imageDataUrl, format, options } = data;

        // Load the image
        const image = await loadImage(imageDataUrl);

        // Create a canvas
        const { canvas, ctx } = createCanvas(image.width, image.height);

        // Draw the image onto the canvas
        ctx.drawImage(image, 0, 0);

        // Convert the canvas to the specified format
        const convertedDataUrl = await canvasToDataUrl(
            canvas,
            format || 'image/png',
            options?.quality || 0.92,
            { enableAntiAliasing: options?.enableAntiAliasing }
        );

        // Send the result back to the main thread
        self.postMessage({
            taskId,
            result: {
                convertedDataUrl,
                format,
                success: true
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Format conversion error: ${error.message}`
        });
    }
}
