// --- START OF FILE content-messaging.js ---

/**
 * Handles messages received from other parts of the extension (background, popup).
 */
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log("Stickara Content Script Received Message:", message);

    // Check for extension context invalidation
    if (chrome.runtime.lastError) {
        console.warn("Stickara: Extension context invalidated in message listener:", chrome.runtime.lastError.message);
        sendResponse({ status: "error", message: "Extension context invalidated" });
        return false;
    }

    // Validate the sender is from our extension
    if (sender.id !== chrome.runtime.id) {
        console.warn("Stickara: Rejected message from unauthorized sender:", sender.id);
        sendResponse({ status: "error", message: "Unauthorized sender" });
        return false;
    }

    let syncResponseNeeded = false; // Flag if we need to return true for async response

    switch (message.action) {
        case 'notesCleared': // Message from popup to clear current page data
            handleNotesCleared(message); // Pass the entire message to access resetToolbar flag
            sendResponse({ status: "ok", message: "Notes and highlights clear initiated on page." });
            break;

        case 'highlightSelection': // Message from background (context menu)
            // --- UPDATED: Use style and color directly from message ---
            const highlightColor = message.color; // Can be null for non-color styles
            const highlightStyle = message.style;

            // Validate style received from message
            if (!highlightStyle || !['color', 'underline', 'wavy', 'border-thick', 'strikethrough'].includes(highlightStyle)) {
                 console.error(`Stickara: Invalid highlight style '${highlightStyle}' received from background.`);
                 sendResponse({ status: "error", message: "Invalid highlight style received." });
                 break;
            }

            // If style is 'color' but color is missing, use default
            const finalColor = (highlightStyle === 'color' && !highlightColor) ? DEFAULT_HIGHLIGHT_COLOR : highlightColor;

            // Ensure handleHighlightSelection exists before calling
            if (typeof handleHighlightSelection === 'function') {
                handleHighlightSelection(finalColor, highlightStyle); // <<< PASS received style and finalColor
                sendResponse({ status: "ok", action: "highlight attempted", color: finalColor, style: highlightStyle });
            } else {
                console.error("Stickara: handleHighlightSelection function not found.");
                sendResponse({ status: "error", message: "Highlight function not available." });
            }
            // --- END UPDATE ---
            break;

        case 'highlightWithNote': // Message from background (context menu)
            // Check if handleHighlightWithNote function exists
            if (typeof handleHighlightWithNote === 'function') {
                handleHighlightWithNote(message.color, message.style);
                sendResponse({ status: "ok", action: "highlight with note attempted" });
            } else {
                console.error("Stickara: handleHighlightWithNote function not found.");
                sendResponse({ status: "error", message: "Highlight with note function not available." });
            }
            break;

        case 'noteFromSelection': // Message from background (context menu)
            handleNoteFromSelection(); // Defined in highlighting.js
            sendResponse({ status: "ok", action: "add to note attempted" });
            break;



        case 'highlightReviewSnippet': // Message from background (context menu)
            // Check if handleHighlightReviewSnippet function exists
            if (typeof handleHighlightReviewSnippet === 'function') {
                handleHighlightReviewSnippet(message.snippetType, message.color, message.style);
                sendResponse({ status: "ok", action: "highlight review snippet attempted" });
            } else {
                console.error("Stickara: handleHighlightReviewSnippet function not found.");
                sendResponse({ status: "error", message: "Highlight review snippet function not available." });
            }
            break;

        case 'highlightSpecSnippet': // Message from background (context menu)
            // Check if handleHighlightSpecSnippet function exists
            if (typeof handleHighlightSpecSnippet === 'function') {
                handleHighlightSpecSnippet(message.color, message.style);
                sendResponse({ status: "ok", action: "highlight spec snippet attempted" });
            } else {
                console.error("Stickara: handleHighlightSpecSnippet function not found.");
                sendResponse({ status: "error", message: "Highlight spec snippet function not available." });
            }
            break;

        case 'highlightReviewSnippetWithNote': // Message from background (context menu)
            // Check if handleHighlightReviewSnippetWithNote function exists
            if (typeof handleHighlightReviewSnippetWithNote === 'function') {
                handleHighlightReviewSnippetWithNote(message.snippetType, message.color, message.style);
                sendResponse({ status: "ok", action: "highlight review snippet with note attempted" });
            } else {
                console.error("Stickara: handleHighlightReviewSnippetWithNote function not found.");
                sendResponse({ status: "error", message: "Highlight review snippet with note function not available." });
            }
            break;

        case 'highlightSpecSnippetWithNote': // Message from background (context menu)
            // Check if handleHighlightSpecSnippetWithNote function exists
            if (typeof handleHighlightSpecSnippetWithNote === 'function') {
                handleHighlightSpecSnippetWithNote(message.color, message.style);
                sendResponse({ status: "ok", action: "highlight spec snippet with note attempted" });
            } else {
                console.error("Stickara: handleHighlightSpecSnippetWithNote function not found.");
                sendResponse({ status: "error", message: "Highlight spec snippet with note function not available." });
            }
            break;

        case 'showReminder':
            // Basic URL validation for reminders
            let isUrlSafe = false;

            if (typeof isSafeURL === 'function') {
                // Use content-security-utils if available
                isUrlSafe = isSafeURL(message.url);
            } else {
                // Basic fallback validation - only allow http/https
                isUrlSafe = /^https?:\/\//.test(message.url);
            }

            if (!isUrlSafe) {
                console.warn("Stickara: Ignoring reminder with unsafe URL protocol");
                sendResponse({ status: "error", message: "Unsafe URL protocol" });
                break;
            }

            // Check if the reminder is for the current page URL
            if (message.url === currentUrl) {
                handleShowReminder(message.noteIndex);
                sendResponse({ status: "ok", message: "Reminder shown" });
            } else {
                console.log("Stickara: Ignoring reminder for different URL:", message.url);
                sendResponse({ status: "ignored", message: "Reminder for different URL" });
            }
            break;

        case 'screenshotCaptured': // Message FROM background with screenshot data
            if (message.success && message.imageDataUrl) {
                // Check if the modal opening function exists
                if (typeof openScreenshotAnnotationModal === 'function') {
                    openScreenshotAnnotationModal(message.imageDataUrl);
                    sendResponse({ status: "ok", message: "Screenshot modal opened." });
                } else {
                    console.error("Stickara Error: openScreenshotAnnotationModal function not found.");
                    sendResponse({ status: "error", message: "Could not open screenshot modal." });
                }
            } else {
                console.error("Stickara: Screenshot capture failed in background.", message.error);
                // Sanitize error message to prevent potential XSS in alert
                const sanitizedError = message.error ?
                    document.createTextNode(message.error).textContent :
                    'Unknown background error';
                alert(`Failed to capture screenshot: ${sanitizedError}`);
                sendResponse({ status: "error", message: sanitizedError });
            }
            break;

            case 'hideUIForCapture':
                // Validate the sender is from our extension
                if (sender.id !== chrome.runtime.id || message._sourceExtensionId !== chrome.runtime.id) {
                    console.warn("SB Content: Rejected 'hideUIForCapture' from unauthorized sender:", sender.id);
                    sendResponse({ hidden: false, error: "Unauthorized sender" });
                    break;
                }

                syncResponseNeeded = true; // We will send a response asynchronously
                console.log("SB Content: Received request to hide UI for capture.");
                try {
                    const noteUI = document.getElementById(NOTE_ID);
                    if (noteUI) {
                        noteUI.style.visibility = 'hidden'; // Use visibility to avoid layout shifts
                        console.log("SB Content: Note UI hidden.");

                        // Also hide any open dropdown contents to prevent them from appearing in screenshots
                        const openDropdowns = document.querySelectorAll('.Stickara-dropdown-content.show');
                        openDropdowns.forEach(dropdown => {
                            dropdown.style.visibility = 'hidden';
                            dropdown.setAttribute('data-hidden-for-capture', 'true');
                        });

                        console.log(`SB Content: Hidden ${openDropdowns.length} open dropdowns for capture.`);

                        // Send confirmation back quickly
                        sendResponse({ hidden: true });
                    } else {
                        console.warn("SB Content: Note UI element not found, cannot hide.");
                        sendResponse({ hidden: false, error: "UI not found" });
                    }
                } catch (e) {
                    console.error("SB Content: Error hiding UI:", e);
                    sendResponse({ hidden: false, error: e.message });
                }
                break; // Keep async flag true

            case 'showUIAfterCapture':
                // Validate the sender is from our extension
                if (sender.id !== chrome.runtime.id || message._sourceExtensionId !== chrome.runtime.id) {
                    console.warn("SB Content: Rejected 'showUIAfterCapture' from unauthorized sender:", sender.id);
                    break;
                }

                console.log("SB Content: Received request to show UI after capture.");
                try {
                    const noteUI = document.getElementById(NOTE_ID);
                    if (noteUI) {
                        noteUI.style.visibility = 'visible'; // Restore visibility
                        console.log("SB Content: Note UI visibility restored.");

                        // Restore visibility of dropdowns that were hidden for capture
                        const hiddenDropdowns = document.querySelectorAll('.Stickara-dropdown-content[data-hidden-for-capture="true"]');
                        hiddenDropdowns.forEach(dropdown => {
                            dropdown.style.visibility = 'visible';
                            dropdown.removeAttribute('data-hidden-for-capture');
                        });

                        console.log(`SB Content: Restored visibility of ${hiddenDropdowns.length} dropdowns after capture.`);
                    } else {
                        console.warn("SB Content: Note UI element not found, cannot restore visibility.");
                    }
                } catch (e) {
                    console.error("SB Content: Error restoring UI visibility:", e);
                }
                // No response needed for this message
                break;


        // Voice Settings Messages
        case 'getVoiceSettingsUI':
            console.log("Stickara: Received getVoiceSettingsUI message");
            // Check if the enhanced voice module is loaded
            if (typeof window.initEnhancedVoice === 'function') {
                console.log("Stickara: Enhanced voice module is available");
                sendResponse({ status: "ok", message: "Voice settings UI available" });
            } else {
                console.error("Stickara: Enhanced voice module not loaded - window.initEnhancedVoice is not a function");
                sendResponse({ status: "error", message: "Enhanced voice module not loaded" });
            }
            break;

        case 'createVoiceSettingsUI':
            // Create a temporary container for the voice settings UI
            const tempContainer = document.createElement('div');
            tempContainer.id = 'temp-voice-settings-container';
            tempContainer.style.display = 'none';
            document.body.appendChild(tempContainer);

            // Create the voice settings UI in the temporary container
            if (typeof window.createVoiceSettingsUI === 'function') {
                window.createVoiceSettingsUI(tempContainer);

                // Send the HTML back to the popup
                const settingsHtml = tempContainer.innerHTML;
                chrome.runtime.sendMessage({
                    action: 'voiceSettingsUICreated',
                    html: settingsHtml
                });

                // Clean up
                document.body.removeChild(tempContainer);

                sendResponse({ status: "ok", message: "Voice settings UI created" });
            } else {
                console.error("Stickara: createVoiceSettingsUI function not found");
                sendResponse({ status: "error", message: "Voice settings UI function not available" });
            }
            break;

        case 'toggleEnhancedVoiceRecording':
            if (typeof window.startEnhancedRecording === 'function' &&
                typeof window.stopEnhancedRecording === 'function') {

                if (message.start) {
                    window.startEnhancedRecording();
                    sendResponse({ status: "ok", message: "Enhanced recording started" });
                } else {
                    window.stopEnhancedRecording();
                    sendResponse({ status: "ok", message: "Enhanced recording stopped" });
                }
            } else {
                console.error("Stickara: Enhanced voice recording functions not found");
                sendResponse({ status: "error", message: "Enhanced voice recording not available" });
            }
            break;

        case 'cropImageToSelection':
            // Validate the sender is from our extension
            if (sender.id !== chrome.runtime.id || message._sourceExtensionId !== chrome.runtime.id) {
                console.warn("Stickara: Rejected 'cropImageToSelection' from unauthorized sender:", sender.id);
                sendResponse({ success: false, error: "Unauthorized sender" });
                break;
            }

            syncResponseNeeded = true; // We will send a response asynchronously
            console.log("Stickara: Received request to crop image to selection");

            // Check if the cropping function exists
            if (typeof cropImageToSelection === 'function') {
                cropImageToSelection(message.imageDataUrl, message.selectionData)
                    .then(croppedImageDataUrl => {
                        console.log("Stickara: Image cropping completed successfully");
                        sendResponse({
                            success: true,
                            croppedImageDataUrl: croppedImageDataUrl
                        });
                    })
                    .catch(error => {
                        console.error("Stickara: Error cropping image:", error);
                        sendResponse({
                            success: false,
                            error: error.message || "Image cropping failed"
                        });
                    });
            } else {
                console.error("Stickara: cropImageToSelection function not found");
                sendResponse({
                    success: false,
                    error: "Image cropping function not available"
                });
            }
            break;

        // Add more message actions here if needed

        default:
            console.log("Stickara: Received unknown message action:", message.action);
            // Send a response indicating the message wasn't handled, or do nothing
            sendResponse({ status: "ignored", message: `Unknown action: ${message.action}` });
            break;
    }

    // Return true if sendResponse might be called asynchronously later.
    // For now, all handlers seem synchronous or don't need async response.
    return syncResponseNeeded;
});

/**
 * Handles the 'notesCleared' message from the popup.
 * Clears local data, resets UI, and removes highlights.
 */
function handleNotesCleared(message) {
    console.log("Stickara: Handling notesCleared message", message);
    notes = {}; // Clear local notes cache
    currentNoteIndex = 1; // Reset to note 1



    // --- Complete UI Cleanup - Remove ALL visual elements ---
    console.log("Stickara: Starting complete UI cleanup...");

    // 1. Remove all highlights from DOM
    document.querySelectorAll(`mark.${HIGHLIGHT_CLASS}`).forEach(mark => {
        if (mark.parentNode) { mark.replaceWith(...mark.childNodes); }
    });
    console.log("Stickara: Removed all highlight marks from DOM");

    // 2. Remove timestamp markers
    document.querySelectorAll('.Stickara-timestamp-link').forEach(element => {
        element.remove();
    });
    console.log("Stickara: Removed all timestamp markers");

    // 3. Clear note content but keep UI visible
    const noteContainer = document.getElementById(NOTE_ID);
    const noteText = document.getElementById('Stickara-text');
    const tagsInput = document.getElementById('Stickara-tags');
    const reminderInput = document.getElementById('Stickara-reminder');
    const timestampSpan = document.getElementById('Stickara-timestamp');
    const noteTitleInput = document.getElementById('Stickara-note-title-input');

    if (noteText) {
        noteText.innerHTML = '';
        console.log("Stickara: Cleared note text content");
    }
    if (tagsInput) {
        tagsInput.value = '';
        console.log("Stickara: Cleared tags input");
    }
    if (reminderInput) {
        reminderInput.value = '';
        console.log("Stickara: Cleared reminder input");
    }
    if (timestampSpan) {
        timestampSpan.innerText = '';
        console.log("Stickara: Cleared timestamp display");
    }
    if (noteTitleInput) {
        noteTitleInput.value = '';
        console.log("Stickara: Cleared note title");
    }

    // Reset note container to default state but keep it visible
    if (noteContainer) {
        // Reset visual state to defaults
        noteContainer.classList.remove('pinned', 'minimized', 'globally-pinned');
        noteContainer.className = noteContainer.className.replace(/theme-\w+/g, '').trim();
        noteContainer.classList.add(`theme-${typeof DEFAULT_COLOR !== 'undefined' ? DEFAULT_COLOR : 'yellow'}`);
        noteContainer.style.opacity = String(typeof DEFAULT_OPACITY !== 'undefined' ? DEFAULT_OPACITY : 1.0);

        // Reset position to default
        const initialTopViewport = 100;
        const initialRightViewport = 24;
        noteContainer.style.top = (initialTopViewport + window.scrollY) + 'px';
        noteContainer.style.left = '';
        noteContainer.style.right = initialRightViewport + 'px';
        noteContainer.style.bottom = 'auto';
        noteContainer.style.width = '300px';
        noteContainer.style.height = '340px';

        console.log("Stickara: Reset note container to default state");
    }

    // 4. Keep toggle button visible - no removal

    // 5. Remove flashcard modal
    const flashcardModal = document.getElementById('Stickara-flashcard-overlay');
    if (flashcardModal) {
        flashcardModal.remove();
        console.log("Stickara: Removed flashcard modal");
    }

    // 6. Remove diagram editor elements
    document.querySelectorAll('.Stickara-diagram-editor').forEach(element => {
        element.remove();
    });
    console.log("Stickara: Removed diagram editor elements");

    // 7. Remove highlight palette
    const highlightPalette = document.getElementById('Stickara-highlight-palette');
    if (highlightPalette) {
        highlightPalette.remove();
        console.log("Stickara: Removed highlight palette");
    }

    // 8. Remove in-note search bar
    const searchBar = document.getElementById('Stickara-in-note-search-bar');
    if (searchBar) {
        searchBar.remove();
        console.log("Stickara: Removed in-note search bar");
    }

    // 9. Close all dropdown menus but don't remove them (they're part of note UI)
    document.querySelectorAll('.Stickara-dropdown-content.show').forEach(dropdown => {
        dropdown.classList.remove('show');
        dropdown.setAttribute('aria-hidden', 'true');
    });
    console.log("Stickara: Closed all dropdown menus");

    // 10. Remove element picker overlays and labels
    const elementPickerOverlay = document.querySelector('.Stickara-element-picker-overlay');
    if (elementPickerOverlay) {
        elementPickerOverlay.remove();
    }
    const elementPickerLabel = document.getElementById('Stickara-element-picker-label');
    if (elementPickerLabel) {
        elementPickerLabel.remove();
    }
    console.log("Stickara: Removed element picker overlays");

    // 11. Remove screenshot annotation modal
    const screenshotModal = document.getElementById('Stickara-screenshot-modal');
    if (screenshotModal) {
        screenshotModal.remove();
        console.log("Stickara: Removed screenshot annotation modal");
    }

    // 12. Remove price comparison UI
    const priceComparisonDiv = document.getElementById('sb-price-comparison');
    if (priceComparisonDiv) {
        priceComparisonDiv.remove();
        console.log("Stickara: Removed price comparison UI");
    }

    // 13. Remove voice status indicators and ARIA live regions
    document.querySelectorAll('.stickara-voice-status').forEach(element => {
        element.remove();
    });
    document.querySelectorAll('[id*="voice-aria-live"]').forEach(element => {
        element.remove();
    });
    console.log("Stickara: Removed voice UI elements");

    // 14. Remove any other Stickara UI elements (comprehensive cleanup) but preserve main note UI
    document.querySelectorAll('[id^="Stickara-"], [class*="Stickara-"], [id^="sb-"], [class*="stickara-"]').forEach(element => {
        // Skip elements that are part of highlights (already handled above)
        // Skip main note UI elements (container, toggle, and their children)
        const isMainNoteUI = element.closest('#Stickara-container, #Stickara-toggle') ||
                            element.id === 'Stickara-container' ||
                            element.id === 'Stickara-toggle';

        if (!element.classList.contains(HIGHLIGHT_CLASS) &&
            !element.closest(`mark.${HIGHLIGHT_CLASS}`) &&
            !isMainNoteUI) {
            element.remove();
        }
    });
    console.log("Stickara: Removed remaining Stickara UI elements (preserved main note UI)");

    // 15. Clean up body classes
    document.body.classList.remove('Stickara-element-picker-active');
    // Remove any other Stickara-related classes from body
    document.body.className = document.body.className.replace(/\bStickara-[^\s]*/g, '').trim();
    console.log("Stickara: Cleaned up body classes");

    // 16. Reset only non-essential global variables (keep main note UI variables)
    if (typeof window.flashcardModalOverlay !== 'undefined') window.flashcardModalOverlay = null;
    if (typeof window.flashcardContent !== 'undefined') window.flashcardContent = null;
    console.log("Stickara: Reset non-essential global UI variables (kept main note UI)");

    // 17. Reset state variables
    notes = {};
    currentNoteIndex = 1;
    highlightsData = [];

    // 18. Ensure note 1 exists with default data
    if (typeof createDefaultNoteData === 'function') {
        notes[1] = createDefaultNoteData();
    }

    // 19. Update UI components to reflect cleared state
    if (typeof updateNoteSwitcher === 'function') {
        updateNoteSwitcher();
    }
    if (typeof resetFlashcardState === 'function') {
        resetFlashcardState();
    }
    if (typeof calculateTextAreaHeight === 'function') {
        calculateTextAreaHeight();
    }

    console.log("Stickara: Complete UI cleanup finished (note UI preserved and reset)");

    // --- Clear Storage Data ---
    if (typeof highlightKey !== 'undefined') {
        chrome.storage.local.remove(highlightKey, () => {
            if (chrome.runtime.lastError) {
                console.error("Stickara: Error clearing highlights from local storage:", chrome.runtime.lastError.message);
            } else {
                console.log("Stickara: Cleared highlights from local storage for page.");
            }
        });
    } else {
        console.error("Stickara: highlightKey not defined, cannot clear highlights from storage.");
    }

    // --- Remove Note Keys 1-10 and State Key ---
    if (typeof currentUrl !== 'undefined') {
        // --- Generate keys for notes 1 to 10 ---
        const noteKeysToRemove = Array.from({ length: 10 }, (_, i) => `${STORAGE_KEY_PREFIX}${currentUrl}_note${i + 1}`);
        // --- End key generation ---
        const stateKeyToRemove = `${STATE_KEY_PREFIX}${currentUrl}`;
        const keysToRemove = [...noteKeysToRemove, stateKeyToRemove];

        chrome.storage.local.remove(keysToRemove, () => {
            if (chrome.runtime.lastError) {
                 console.error("Stickara: Error removing note/state keys:", chrome.runtime.lastError.message);
            } else {
                 console.log("Stickara: Removed note (1-10) and state keys for current page.");
            }
            // No need to save state or call hideNote since all UI elements are already removed
            console.log("Stickara: Clear page data operation completed successfully");
        });
    } else {
        console.error("Stickara: currentUrl not defined, cannot remove note/state keys.");
        console.log("Stickara: Clear page data operation completed (with URL error)");
    }
}


/**
 * Handles the 'showReminder' message from the background script.
 * Switches to the specified note, makes it visible, and shows a visual cue.
 * @param {number} noteIndex - The index of the note for the reminder.
 */
function handleShowReminder(noteIndex) {
    console.log(`Stickara: Handling reminder for Note ${noteIndex} on this page.`);

    // Ensure the main note UI exists, create if necessary
    if (!noteContainer) {
        createNoteUI(); // Defined in ui.js
        if (!noteContainer) {
            console.error("Stickara: Failed to create note UI for reminder.");
            // Alert moved to background notification
            // alert(`Reminder for Stickara Note ${noteIndex}!\n(Could not show note window automatically)`);
            return;
        }
        // If UI was just created, state needs loading before switching/showing
        // Add a small delay after creation attempt
        setTimeout(() => {
            handleShowReminderInternal(noteIndex);
        }, 300); // Wait 300ms after create attempt
        return; // Exit the initial call
    }

    // If UI already exists, proceed directly
    handleShowReminderInternal(noteIndex);
}

// Internal helper for showing reminder after UI check/creation
function handleShowReminderInternal(noteIndex) {
     // Re-check if noteContainer is valid after potential delay
     if (!noteContainer) {
         console.error("Stickara: Note container still not available after delay for reminder.");
         // alert(`Reminder for Stickara Note ${noteIndex}!\n(Could not show note window automatically)`); // Alert moved to background
         return;
     }
     // If not already on the correct note, switch to it
    if (currentNoteIndex !== noteIndex) {
        switchToNote(noteIndex); // Defined in interactions.js
        // Need to wait for loadCurrentNote to apply data before showing
        setTimeout(() => {
             ensureNoteVisibleForReminder();
         }, 150); // Wait a bit after switching
         // Don't return here, allow ensureNoteVisibleForReminder to run after delay
    } else {
        // Already on the correct note, just ensure visibility
         ensureNoteVisibleForReminder();
    }
}

// Internal helper to make the note visible and flash
function ensureNoteVisibleForReminder() {
     if (!noteContainer) return;

     // Ensure the note container is visible
     if (!noteContainer.classList.contains('visible')) {
         showNote(); // Defined in interactions.js
         // showNote already handles focus and calculations via requestAnimationFrame
     }

     // Ensure the note is visible and properly sized
     if (noteContainer) {
         // Make sure the note has proper dimensions
         if (!noteContainer.style.height || noteContainer.style.height === '0px') {
             noteContainer.style.height = '340px';
         }

         // Make sure content is visible
         const noteText = document.getElementById('Stickara-text');
         if (noteText) {
             noteText.style.display = '';
             if (typeof calculateTextAreaHeight === 'function') {
                 calculateTextAreaHeight();
             }
         }
     }

     // Add a visual cue (e.g., flash header) - Ensure header exists
     if (noteHeader) {
         noteHeader.classList.add('Stickara-reminder-flash'); // Add class for CSS animation
         console.log("Stickara: Flashing reminder header.");
         setTimeout(() => {
             if (noteHeader) { // Check if header still exists
                 noteHeader.classList.remove('Stickara-reminder-flash'); // Remove class after animation
             }
         }, 2500); // Match animation duration (should match CSS)
     } else {
          console.warn("Stickara: Note header not found for reminder flash.");
     }

     // Optionally bring the window/tab to focus? Requires background script help usually.

     // Alert the user (simple notification) - moved to background script for better reliability
     // alert(`Reminder for Stickara Note ${noteIndex}!`);
}

console.log("Stickara: Messaging Logic Loaded (v1.2 - Toolbar Reset Support)"); // Updated log
// --- END OF FILE content-messaging.js ---