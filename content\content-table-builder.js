// --- START OF FILE content-table-builder.js ---

/**
 * Table Builder for Smart Templates
 * Provides visual table creation and smart table syntax processing
 */

let tableBuilderModal = null;
let selectedTableDimensions = { rows: 3, cols: 3 };

/**
 * Open the table builder modal
 */
function openTableBuilder() {
    if (tableBuilderModal) {
        tableBuilderModal.style.display = 'flex';
        return;
    }
    
    createTableBuilderModal();
    tableBuilderModal.style.display = 'flex';
}

/**
 * Close the table builder modal
 */
function closeTableBuilder() {
    if (tableBuilderModal) {
        tableBuilderModal.style.display = 'none';
    }
}

/**
 * Create the table builder modal
 */
function createTableBuilderModal() {
    tableBuilderModal = document.createElement('div');
    tableBuilderModal.id = 'Stickara-table-builder-modal';
    tableBuilderModal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        z-index: 2147483648;
        display: none;
        justify-content: center;
        align-items: center;
        font-family: Arial, sans-serif;
    `;
    
    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: white;
        border-radius: 12px;
        width: 600px;
        max-width: 90vw;
        max-height: 80vh;
        display: flex;
        flex-direction: column;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        overflow: hidden;
    `;
    
    // Header
    const header = document.createElement('div');
    header.style.cssText = `
        background: #34a853;
        color: white;
        padding: 16px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: bold;
        font-size: 18px;
    `;
    header.innerHTML = `
        <span>📊 Table Builder</span>
        <button id="Stickara-table-builder-close" style="
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        ">×</button>
    `;
    
    // Content
    const content = document.createElement('div');
    content.style.cssText = `
        padding: 20px;
        flex: 1;
        overflow-y: auto;
    `;
    
    content.innerHTML = `
        <div style="margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0; color: #333;">Select Table Size</h3>
            <div id="table-size-selector" style="
                display: inline-block;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px;
                background: #fafafa;
            "></div>
            <div style="margin-top: 10px; font-size: 14px; color: #666;">
                Selected: <span id="table-dimensions">3 × 3</span>
            </div>
        </div>
        
        <div style="margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0; color: #333;">Table Style</h3>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <label style="display: flex; align-items: center; gap: 5px; cursor: pointer;">
                    <input type="radio" name="table-style" value="basic" checked>
                    <span>Basic</span>
                </label>
                <label style="display: flex; align-items: center; gap: 5px; cursor: pointer;">
                    <input type="radio" name="table-style" value="bordered">
                    <span>Bordered</span>
                </label>
                <label style="display: flex; align-items: center; gap: 5px; cursor: pointer;">
                    <input type="radio" name="table-style" value="striped">
                    <span>Striped</span>
                </label>
                <label style="display: flex; align-items: center; gap: 5px; cursor: pointer;">
                    <input type="radio" name="table-style" value="data">
                    <span>Data Table</span>
                </label>
            </div>
        </div>
        
        <div style="margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0; color: #333;">Table Options</h3>
            <div style="display: flex; flex-direction: column; gap: 8px;">
                <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                    <input type="checkbox" id="table-headers" checked>
                    <span>Include headers</span>
                </label>
                <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                    <input type="checkbox" id="table-responsive">
                    <span>Responsive table</span>
                </label>
                <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                    <input type="checkbox" id="table-smart-syntax">
                    <span>Use smart template syntax</span>
                </label>
            </div>
        </div>
        
        <div style="margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0; color: #333;">Preview</h3>
            <div id="table-preview" style="
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                padding: 15px;
                background: #fafafa;
                max-height: 200px;
                overflow: auto;
            "></div>
        </div>
    `;
    
    // Footer
    const footer = document.createElement('div');
    footer.style.cssText = `
        padding: 16px 20px;
        background: #f8f9fa;
        border-top: 1px solid #e0e0e0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    `;
    
    footer.innerHTML = `
        <div style="font-size: 12px; color: #666;">
            💡 Tip: Smart syntax allows dynamic table content
        </div>
        <div>
            <button id="Stickara-table-cancel" style="
                background: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                cursor: pointer;
                margin-right: 8px;
            ">Cancel</button>
            <button id="Stickara-table-insert" style="
                background: #34a853;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                cursor: pointer;
            ">Insert Table</button>
        </div>
    `;
    
    modalContent.appendChild(header);
    modalContent.appendChild(content);
    modalContent.appendChild(footer);
    tableBuilderModal.appendChild(modalContent);
    
    // Add event listeners
    document.getElementById('Stickara-table-builder-close').addEventListener('click', closeTableBuilder);
    document.getElementById('Stickara-table-cancel').addEventListener('click', closeTableBuilder);
    document.getElementById('Stickara-table-insert').addEventListener('click', insertTable);
    
    // Create table size selector
    createTableSizeSelector();
    
    // Add change listeners for options
    const styleInputs = document.querySelectorAll('input[name="table-style"]');
    styleInputs.forEach(input => {
        input.addEventListener('change', updateTablePreview);
    });
    
    const optionInputs = document.querySelectorAll('#table-headers, #table-responsive, #table-smart-syntax');
    optionInputs.forEach(input => {
        input.addEventListener('change', updateTablePreview);
    });
    
    // Close on backdrop click
    tableBuilderModal.addEventListener('click', (e) => {
        if (e.target === tableBuilderModal) {
            closeTableBuilder();
        }
    });
    
    document.body.appendChild(tableBuilderModal);
    
    // Initial preview
    updateTablePreview();
}

/**
 * Create the table size selector grid
 */
function createTableSizeSelector() {
    const selector = document.getElementById('table-size-selector');
    if (!selector) return;
    
    selector.innerHTML = '';
    
    for (let row = 1; row <= 8; row++) {
        for (let col = 1; col <= 8; col++) {
            const cell = document.createElement('div');
            cell.style.cssText = `
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 1px solid #ccc;
                margin: 1px;
                cursor: pointer;
                background: #fff;
                transition: background-color 0.1s ease;
            `;
            
            cell.addEventListener('mouseenter', () => {
                highlightTableCells(row, col);
                updateDimensionsDisplay(row, col);
            });
            
            cell.addEventListener('click', () => {
                selectedTableDimensions = { rows: row, cols: col };
                updateTablePreview();
            });
            
            cell.dataset.row = row;
            cell.dataset.col = col;
            selector.appendChild(cell);
        }
        selector.appendChild(document.createElement('br'));
    }
}

/**
 * Highlight table cells up to the specified dimensions
 */
function highlightTableCells(maxRow, maxCol) {
    const cells = document.querySelectorAll('#table-size-selector div');
    cells.forEach(cell => {
        const row = parseInt(cell.dataset.row);
        const col = parseInt(cell.dataset.col);
        
        if (row <= maxRow && col <= maxCol) {
            cell.style.backgroundColor = '#4285f4';
        } else {
            cell.style.backgroundColor = '#fff';
        }
    });
}

/**
 * Update the dimensions display
 */
function updateDimensionsDisplay(rows, cols) {
    const display = document.getElementById('table-dimensions');
    if (display) {
        display.textContent = `${rows} × ${cols}`;
    }
}

/**
 * Update the table preview
 */
function updateTablePreview() {
    const preview = document.getElementById('table-preview');
    if (!preview) return;
    
    const style = document.querySelector('input[name="table-style"]:checked')?.value || 'basic';
    const hasHeaders = document.getElementById('table-headers')?.checked || false;
    const isResponsive = document.getElementById('table-responsive')?.checked || false;
    const useSmartSyntax = document.getElementById('table-smart-syntax')?.checked || false;
    
    const tableHtml = generateTableHTML(selectedTableDimensions.rows, selectedTableDimensions.cols, style, hasHeaders, isResponsive, useSmartSyntax);
    preview.innerHTML = tableHtml;
}

/**
 * Generate table HTML based on options
 */
function generateTableHTML(rows, cols, style, hasHeaders, isResponsive, useSmartSyntax) {
    let tableClass = 'stickara-table';
    
    switch (style) {
        case 'bordered':
            tableClass += ' table-bordered';
            break;
        case 'striped':
            tableClass += ' table-striped';
            break;
        case 'data':
            tableClass += ' table-data';
            break;
    }
    
    if (isResponsive) {
        tableClass += ' table-responsive';
    }
    
    let html = `<table class="${tableClass}" style="width: 100%; border-collapse: collapse;">`;
    
    // Generate table content
    for (let row = 0; row < rows; row++) {
        html += '<tr>';
        
        for (let col = 0; col < cols; col++) {
            const isHeaderRow = hasHeaders && row === 0;
            const tag = isHeaderRow ? 'th' : 'td';
            const cellStyle = getCellStyle(style);
            
            if (useSmartSyntax) {
                if (isHeaderRow) {
                    html += `<${tag} style="${cellStyle}">{{Prompt:Header ${col + 1}|Column ${col + 1}}}</${tag}>`;
                } else {
                    html += `<${tag} style="${cellStyle}">{{Prompt:Row ${row} Col ${col + 1}|Data}}</${tag}>`;
                }
            } else {
                if (isHeaderRow) {
                    html += `<${tag} style="${cellStyle}">Column ${col + 1}</${tag}>`;
                } else {
                    html += `<${tag} style="${cellStyle}">Data</${tag}>`;
                }
            }
        }
        
        html += '</tr>';
    }
    
    html += '</table>';
    
    return html;
}

/**
 * Get cell styles based on table style
 */
function getCellStyle(style) {
    const baseStyle = 'padding: 8px; text-align: left;';
    
    switch (style) {
        case 'bordered':
            return baseStyle + ' border: 1px solid #ddd;';
        case 'striped':
            return baseStyle + ' border-bottom: 1px solid #eee;';
        case 'data':
            return baseStyle + ' border: 1px solid #ddd; background: #f9f9f9;';
        default:
            return baseStyle + ' border-bottom: 1px solid #eee;';
    }
}

/**
 * Insert the generated table into the template editor
 */
function insertTable() {
    const style = document.querySelector('input[name="table-style"]:checked')?.value || 'basic';
    const hasHeaders = document.getElementById('table-headers')?.checked || false;
    const isResponsive = document.getElementById('table-responsive')?.checked || false;
    const useSmartSyntax = document.getElementById('table-smart-syntax')?.checked || false;
    
    const tableHtml = generateTableHTML(selectedTableDimensions.rows, selectedTableDimensions.cols, style, hasHeaders, isResponsive, useSmartSyntax);
    
    // Insert into template editor if available
    if (typeof insertTemplateSnippet === 'function') {
        insertTemplateSnippet('\n' + tableHtml + '\n');
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(tableHtml).then(() => {
            alert('Table HTML copied to clipboard!');
        }).catch(() => {
            // Show in a new window if clipboard fails
            const newWindow = window.open('', '_blank');
            newWindow.document.write(`
                <html>
                <head><title>Generated Table</title></head>
                <body>
                    <h2>Generated Table HTML</h2>
                    <textarea style="width: 100%; height: 300px;">${tableHtml}</textarea>
                    <br><br>
                    <div>${tableHtml}</div>
                </body>
                </html>
            `);
        });
    }
    
    closeTableBuilder();
}

// Make functions available globally
window.openTableBuilder = openTableBuilder;
window.closeTableBuilder = closeTableBuilder;

console.log("Stickara: Table Builder Loaded");

// --- END OF FILE content-table-builder.js ---
