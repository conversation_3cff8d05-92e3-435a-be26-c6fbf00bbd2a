/**
 * Stickara Storage Dashboard
 * Provides a UI component to monitor cache performance and storage usage
 */

// Create a namespace to avoid global pollution
window.StickaraStorageDashboard = (function() {
    // Dashboard sections
    const SECTIONS = {
        OVERVIEW: 'overview',
        CACHE: 'cache',
        STORAGE: 'storage',
        COMPRESSION: 'compression',
        PERFORMANCE: 'performance',
        TASKS: 'tasks'
    };

    // Chart types
    const CHART_TYPES = {
        BAR: 'bar',
        LINE: 'line',
        PIE: 'pie',
        AREA: 'area'
    };

    // Configuration
    const config = {
        refreshInterval: 5000,         // Dashboard refresh interval in milliseconds
        maxDataPoints: 100,            // Maximum number of data points to keep
        showInPopup: true,             // Show dashboard in popup
        enableCharts: true,            // Enable charts
        enableExport: true,            // Enable data export
        theme: 'light',                // Dashboard theme (light only)
        debug: false                   // Debug mode
    };

    // Private variables
    let dashboardElement = null;
    let refreshTimer = null;
    let isVisible = false;
    let performanceData = {
        cache: {
            hits: [],
            misses: [],
            ratio: []
        },
        storage: {
            usage: [],
            items: []
        },
        compression: {
            savings: [],
            ratio: []
        },
        tasks: {
            completed: [],
            failed: []
        }
    };

    /**
     * Log messages when debug mode is enabled
     * @param {...any} args - Arguments to log
     */
    function debugLog(...args) {
        if (config.debug) {
            console.log('[StickaraStorageDashboard]', ...args);
        }
    }

    /**
     * Initializes the dashboard
     * @returns {Promise<boolean>} A promise that resolves with true if initialization was successful
     */
    async function init() {
        try {
            // Create dashboard element if it doesn't exist
            if (!dashboardElement) {
                createDashboardElement();
            }

            // Start data collection
            startDataCollection();

            return true;
        } catch (error) {
            console.error('Stickara: Error initializing storage dashboard:', error);
            return false;
        }
    }

    /**
     * Creates the dashboard element
     */
    function createDashboardElement() {
        // Create container
        dashboardElement = document.createElement('div');
        dashboardElement.id = 'stickara-storage-dashboard';
        dashboardElement.className = 'stickara-dashboard';
        dashboardElement.style.display = 'none';

        // Apply styles
        applyDashboardStyles();

        // Create dashboard content
        dashboardElement.innerHTML = `
            <div class="stickara-dashboard-header">
                <h2>Stickara Storage Dashboard</h2>
                <div class="stickara-dashboard-controls">
                    <button id="stickara-dashboard-refresh" title="Refresh">↻</button>
                    <button id="stickara-dashboard-export" title="Export Data">⤓</button>
                    <button id="stickara-dashboard-close" title="Close">✕</button>
                </div>
            </div>
            <div class="stickara-dashboard-tabs">
                <button class="stickara-tab active" data-section="${SECTIONS.OVERVIEW}">Overview</button>
                <button class="stickara-tab" data-section="${SECTIONS.CACHE}">Cache</button>
                <button class="stickara-tab" data-section="${SECTIONS.STORAGE}">Storage</button>
                <button class="stickara-tab" data-section="${SECTIONS.COMPRESSION}">Compression</button>
                <button class="stickara-tab" data-section="${SECTIONS.PERFORMANCE}">Performance</button>
                <button class="stickara-tab" data-section="${SECTIONS.TASKS}">Tasks</button>
            </div>
            <div class="stickara-dashboard-content">
                <div class="stickara-dashboard-section active" id="stickara-section-${SECTIONS.OVERVIEW}">
                    <h3>Storage Overview</h3>
                    <div class="stickara-dashboard-stats">
                        <div class="stickara-stat-card">
                            <div class="stickara-stat-title">Total Storage</div>
                            <div class="stickara-stat-value" id="stickara-stat-total-storage">-</div>
                        </div>
                        <div class="stickara-stat-card">
                            <div class="stickara-stat-title">Items</div>
                            <div class="stickara-stat-value" id="stickara-stat-total-items">-</div>
                        </div>
                        <div class="stickara-stat-card">
                            <div class="stickara-stat-title">Cache Hit Ratio</div>
                            <div class="stickara-stat-value" id="stickara-stat-cache-hit-ratio">-</div>
                        </div>
                        <div class="stickara-stat-card">
                            <div class="stickara-stat-title">Compression Ratio</div>
                            <div class="stickara-stat-value" id="stickara-stat-compression-ratio">-</div>
                        </div>
                    </div>
                    <div class="stickara-dashboard-chart" id="stickara-chart-overview"></div>
                </div>
                <div class="stickara-dashboard-section" id="stickara-section-${SECTIONS.CACHE}">
                    <h3>Cache Performance</h3>
                    <div class="stickara-dashboard-stats">
                        <div class="stickara-stat-card">
                            <div class="stickara-stat-title">Cache Hits</div>
                            <div class="stickara-stat-value" id="stickara-stat-cache-hits">-</div>
                        </div>
                        <div class="stickara-stat-card">
                            <div class="stickara-stat-title">Cache Misses</div>
                            <div class="stickara-stat-value" id="stickara-stat-cache-misses">-</div>
                        </div>
                        <div class="stickara-stat-card">
                            <div class="stickara-stat-title">Memory Cache Items</div>
                            <div class="stickara-stat-value" id="stickara-stat-memory-cache-items">-</div>
                        </div>
                        <div class="stickara-stat-card">
                            <div class="stickara-stat-title">Persistent Cache Items</div>
                            <div class="stickara-stat-value" id="stickara-stat-persistent-cache-items">-</div>
                        </div>
                    </div>
                    <div class="stickara-dashboard-chart" id="stickara-chart-cache"></div>
                </div>
                <!-- Additional sections would be defined here -->
            </div>
        `;

        // Add event listeners
        addDashboardEventListeners();

        // Append to document
        document.body.appendChild(dashboardElement);
    }

    /**
     * Applies styles to the dashboard
     */
    function applyDashboardStyles() {
        // Create style element if it doesn't exist
        let styleElement = document.getElementById('stickara-dashboard-styles');

        if (!styleElement) {
            styleElement = document.createElement('style');
            styleElement.id = 'stickara-dashboard-styles';
            document.head.appendChild(styleElement);
        }

        // Define styles
        styleElement.textContent = `
            .stickara-dashboard {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 800px;
                max-width: 90vw;
                height: 600px;
                max-height: 90vh;
                background-color: #fff;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                display: flex;
                flex-direction: column;
                overflow: hidden;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            }

            .stickara-dashboard-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px 20px;
                border-bottom: 1px solid #eaeaea;
            }

            .stickara-dashboard-header h2 {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
                color: #333;
            }

            .stickara-dashboard-controls {
                display: flex;
                gap: 8px;
            }

            .stickara-dashboard-controls button {
                background: none;
                border: none;
                font-size: 16px;
                cursor: pointer;
                width: 28px;
                height: 28px;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .stickara-dashboard-controls button:hover {
                background-color: #f5f5f5;
            }

            .stickara-dashboard-tabs {
                display: flex;
                padding: 0 20px;
                border-bottom: 1px solid #eaeaea;
                overflow-x: auto;
            }

            .stickara-tab {
                padding: 12px 16px;
                background: none;
                border: none;
                border-bottom: 2px solid transparent;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                color: #666;
                white-space: nowrap;
            }

            .stickara-tab.active {
                border-bottom-color: #4a6ee0;
                color: #4a6ee0;
            }

            .stickara-dashboard-content {
                flex: 1;
                overflow-y: auto;
                padding: 20px;
            }

            .stickara-dashboard-section {
                display: none;
            }

            .stickara-dashboard-section.active {
                display: block;
            }

            .stickara-dashboard-section h3 {
                margin: 0 0 16px;
                font-size: 16px;
                font-weight: 600;
                color: #333;
            }

            .stickara-dashboard-stats {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
                gap: 16px;
                margin-bottom: 24px;
            }

            .stickara-stat-card {
                background-color: #f9f9f9;
                border-radius: 6px;
                padding: 16px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            }

            .stickara-stat-title {
                font-size: 13px;
                color: #666;
                margin-bottom: 8px;
            }

            .stickara-stat-value {
                font-size: 24px;
                font-weight: 600;
                color: #333;
            }

            .stickara-dashboard-chart {
                width: 100%;
                height: 300px;
                background-color: #f9f9f9;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #999;
            }


        `;
    }

    /**
     * Adds event listeners to dashboard elements
     */
    function addDashboardEventListeners() {
        // Close button
        const closeButton = dashboardElement.querySelector('#stickara-dashboard-close');
        if (closeButton) {
            closeButton.addEventListener('click', hide);
        }

        // Refresh button
        const refreshButton = dashboardElement.querySelector('#stickara-dashboard-refresh');
        if (refreshButton) {
            refreshButton.addEventListener('click', refreshDashboard);
        }

        // Export button
        const exportButton = dashboardElement.querySelector('#stickara-dashboard-export');
        if (exportButton) {
            exportButton.addEventListener('click', exportData);
        }

        // Tab buttons
        const tabButtons = dashboardElement.querySelectorAll('.stickara-tab');
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all tabs and sections
                tabButtons.forEach(btn => btn.classList.remove('active'));
                const sections = dashboardElement.querySelectorAll('.stickara-dashboard-section');
                sections.forEach(section => section.classList.remove('active'));

                // Add active class to clicked tab and corresponding section
                button.classList.add('active');
                const sectionId = `stickara-section-${button.dataset.section}`;
                const section = dashboardElement.querySelector(`#${sectionId}`);
                if (section) {
                    section.classList.add('active');
                }
            });
        });
    }

    /**
     * Starts data collection for the dashboard
     */
    function startDataCollection() {
        // Clear any existing timer
        if (refreshTimer) {
            clearInterval(refreshTimer);
        }

        // Collect initial data
        collectData();

        // Set up timer for regular updates
        refreshTimer = setInterval(collectData, config.refreshInterval);
    }

    /**
     * Collects data for the dashboard
     */
    async function collectData() {
        try {
            // Get data from various sources
            const storageData = await getStorageData();
            const cacheData = await getCacheData();
            const compressionData = await getCompressionData();
            const taskData = await getTaskData();

            // Update performance data
            updatePerformanceData(storageData, cacheData, compressionData, taskData);

            // Update dashboard if visible
            if (isVisible) {
                updateDashboard();
            }
        } catch (error) {
            console.error('Stickara: Error collecting dashboard data:', error);
        }
    }

    /**
     * Gets storage usage data
     * @returns {Promise<Object>} A promise that resolves with storage data
     */
    async function getStorageData() {
        const data = {
            totalSize: 0,
            totalItems: 0,
            breakdown: {
                highlights: { count: 0, size: 0 },
                notes: { count: 0, size: 0 },
                metadata: { count: 0, size: 0 },
                cache: { count: 0, size: 0 }
            }
        };

        try {
            if (window.StickaraIndexedDB) {
                // Get counts for each store
                const highlightCount = await window.StickaraIndexedDB.countHighlights();
                const noteCount = await window.StickaraIndexedDB.countNotes();

                // Get sample items to estimate size
                const highlights = await window.StickaraIndexedDB.getHighlights({ limit: 10 });
                const notes = await window.StickaraIndexedDB.getNotes({ limit: 10 });

                // Estimate sizes
                const avgHighlightSize = highlights.length > 0
                    ? highlights.reduce((sum, h) => sum + JSON.stringify(h).length, 0) / highlights.length
                    : 0;

                const avgNoteSize = notes.length > 0
                    ? notes.reduce((sum, n) => sum + JSON.stringify(n).length, 0) / notes.length
                    : 0;

                // Update data
                data.breakdown.highlights.count = highlightCount || 0;
                data.breakdown.highlights.size = avgHighlightSize * highlightCount;

                data.breakdown.notes.count = noteCount || 0;
                data.breakdown.notes.size = avgNoteSize * noteCount;

                // Get cache stats if available
                if (window.StickaraIndexedDB.getMemoryCacheStats) {
                    const cacheStats = window.StickaraIndexedDB.getMemoryCacheStats();
                    data.breakdown.cache.count = cacheStats.itemCount || 0;
                    // Rough estimate of cache size
                    data.breakdown.cache.size = cacheStats.itemCount * 1024; // Assume 1KB per item
                }

                // Calculate totals
                data.totalItems = Object.values(data.breakdown).reduce((sum, store) => sum + store.count, 0);
                data.totalSize = Object.values(data.breakdown).reduce((sum, store) => sum + store.size, 0);
            }
        } catch (error) {
            console.error('Stickara: Error getting storage data:', error);
        }

        return data;
    }

    /**
     * Gets cache performance data
     * @returns {Promise<Object>} A promise that resolves with cache data
     */
    async function getCacheData() {
        const data = {
            hits: 0,
            misses: 0,
            ratio: 0,
            memoryItems: 0,
            persistentItems: 0
        };

        try {
            // Get cache stats from IndexedDB if available
            if (window.StickaraIndexedDB && window.StickaraIndexedDB.getMemoryCacheStats) {
                const cacheStats = window.StickaraIndexedDB.getMemoryCacheStats();
                data.memoryItems = cacheStats.itemCount || 0;
            }

            // Get cache stats from cache manager if available
            if (window.StickaraCacheManager && window.StickaraCacheManager.getStats) {
                const cacheStats = window.StickaraCacheManager.getStats();
                data.hits = cacheStats.hits || 0;
                data.misses = cacheStats.misses || 0;
                data.persistentItems = cacheStats.persistentItems || 0;

                // Calculate hit ratio
                const total = data.hits + data.misses;
                data.ratio = total > 0 ? data.hits / total : 0;
            }
        } catch (error) {
            console.error('Stickara: Error getting cache data:', error);
        }

        return data;
    }

    /**
     * Gets compression performance data
     * @returns {Promise<Object>} A promise that resolves with compression data
     */
    async function getCompressionData() {
        const data = {
            originalSize: 0,
            compressedSize: 0,
            savings: 0,
            ratio: 0,
            itemsCompressed: 0,
            algorithms: {}
        };

        try {
            // Get compression stats from advanced compression if available
            if (window.StickaraAdvancedCompression) {
                // Get available algorithms
                const algorithms = window.StickaraAdvancedCompression.getAvailableAlgorithms();
                data.availableAlgorithms = algorithms;

                // Get compression config
                const config = window.StickaraAdvancedCompression.getConfig();
                data.defaultAlgorithm = config.defaultAlgorithm;
                data.compressionThreshold = config.compressionThreshold;
            }

            // Get compressed items from IndexedDB
            if (window.StickaraIndexedDB) {
                // Get sample of compressed highlights
                const highlights = await window.StickaraIndexedDB.getHighlights({ limit: 100 });
                const compressedHighlights = highlights.filter(h => h.compressed);

                // Get sample of compressed notes
                const notes = await window.StickaraIndexedDB.getNotes({ limit: 100 });
                const compressedNotes = notes.filter(n => n.compressed);

                // Calculate compression stats
                const compressedItems = [...compressedHighlights, ...compressedNotes];
                data.itemsCompressed = compressedItems.length;

                // Calculate sizes and ratios
                if (compressedItems.length > 0) {
                    data.originalSize = compressedItems.reduce((sum, item) => sum + (item.originalSize || 0), 0);
                    data.compressedSize = compressedItems.reduce((sum, item) => sum + (item.compressedSize || 0), 0);
                    data.savings = data.originalSize - data.compressedSize;
                    data.ratio = data.originalSize > 0 ? data.originalSize / data.compressedSize : 0;

                    // Count algorithms used
                    compressedItems.forEach(item => {
                        const algo = item.algorithm || 'unknown';
                        data.algorithms[algo] = (data.algorithms[algo] || 0) + 1;
                    });
                }
            }
        } catch (error) {
            console.error('Stickara: Error getting compression data:', error);
        }

        return data;
    }

    /**
     * Gets background task data
     * @returns {Promise<Object>} A promise that resolves with task data
     */
    async function getTaskData() {
        const data = {
            tasksCompleted: 0,
            tasksFailed: 0,
            tasksInQueue: 0,
            lastRun: null,
            taskTypes: {}
        };

        try {
            // Get task stats from background optimizer if available
            if (window.StickaraBackgroundOptimizer) {
                const stats = window.StickaraBackgroundOptimizer.getStats();
                data.tasksCompleted = stats.tasksCompleted || 0;
                data.tasksFailed = stats.tasksSkipped || 0;
                data.lastRun = stats.lastRun;
                data.bytesReclaimed = stats.bytesReclaimed || 0;

                // Get current task queue
                const queue = window.StickaraBackgroundOptimizer.getTaskQueue();
                data.tasksInQueue = queue.length;

                // Count task types
                queue.forEach(task => {
                    const type = task.type || 'unknown';
                    data.taskTypes[type] = (data.taskTypes[type] || 0) + 1;
                });
            }
        } catch (error) {
            console.error('Stickara: Error getting task data:', error);
        }

        return data;
    }

    /**
     * Updates performance data with new measurements
     * @param {Object} storageData - Storage usage data
     * @param {Object} cacheData - Cache performance data
     * @param {Object} compressionData - Compression performance data
     * @param {Object} taskData - Background task data
     */
    function updatePerformanceData(storageData, cacheData, compressionData, taskData) {
        const timestamp = Date.now();

        // Add new data points
        performanceData.cache.hits.push({ timestamp, value: cacheData.hits });
        performanceData.cache.misses.push({ timestamp, value: cacheData.misses });
        performanceData.cache.ratio.push({ timestamp, value: cacheData.ratio });

        performanceData.storage.usage.push({ timestamp, value: storageData.totalSize });
        performanceData.storage.items.push({ timestamp, value: storageData.totalItems });

        performanceData.compression.savings.push({ timestamp, value: compressionData.savings });
        performanceData.compression.ratio.push({ timestamp, value: compressionData.ratio });

        performanceData.tasks.completed.push({ timestamp, value: taskData.tasksCompleted });
        performanceData.tasks.failed.push({ timestamp, value: taskData.tasksFailed });

        // Limit data points
        const limitDataPoints = (array) => {
            if (array.length > config.maxDataPoints) {
                return array.slice(array.length - config.maxDataPoints);
            }
            return array;
        };

        // Apply limits to all data series
        Object.keys(performanceData).forEach(category => {
            Object.keys(performanceData[category]).forEach(series => {
                performanceData[category][series] = limitDataPoints(performanceData[category][series]);
            });
        });
    }

    /**
     * Updates the dashboard with current data
     */
    function updateDashboard() {
        try {
            // Get the latest data points
            const getLatestValue = (category, series) => {
                const data = performanceData[category][series];
                return data.length > 0 ? data[data.length - 1].value : 0;
            };

            // Update overview stats
            updateElement('stickara-stat-total-storage', formatBytes(getLatestValue('storage', 'usage')));
            updateElement('stickara-stat-total-items', formatNumber(getLatestValue('storage', 'items')));
            updateElement('stickara-stat-cache-hit-ratio', formatPercent(getLatestValue('cache', 'ratio')));
            updateElement('stickara-stat-compression-ratio', formatRatio(getLatestValue('compression', 'ratio')));

            // Update cache stats
            updateElement('stickara-stat-cache-hits', formatNumber(getLatestValue('cache', 'hits')));
            updateElement('stickara-stat-cache-misses', formatNumber(getLatestValue('cache', 'misses')));

            // Update charts if enabled
            if (config.enableCharts) {
                updateCharts();
            }
        } catch (error) {
            console.error('Stickara: Error updating dashboard:', error);
        }
    }

    /**
     * Updates a dashboard element with a value
     * @param {string} elementId - The ID of the element to update
     * @param {string} value - The value to set
     */
    function updateElement(elementId, value) {
        const element = dashboardElement.querySelector(`#${elementId}`);
        if (element) {
            element.textContent = value;
        }
    }

    /**
     * Updates charts with current data
     */
    function updateCharts() {
        // This is a placeholder for chart updates
        // In a real implementation, you would use a charting library
        // like Chart.js to render charts

        // For now, just show a message
        const chartElements = dashboardElement.querySelectorAll('.stickara-dashboard-chart');
        chartElements.forEach(element => {
            element.textContent = 'Charts will be rendered here';
        });
    }

    /**
     * Refreshes the dashboard
     */
    function refreshDashboard() {
        collectData();
    }

    /**
     * Exports dashboard data
     */
    function exportData() {
        try {
            // Create export data
            const exportData = {
                timestamp: Date.now(),
                performanceData,
                config
            };

            // Convert to JSON
            const jsonData = JSON.stringify(exportData, null, 2);

            // Create download link
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `stickara-storage-stats-${new Date().toISOString().slice(0, 10)}.json`;
            a.click();

            // Clean up
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Stickara: Error exporting dashboard data:', error);
        }
    }

    /**
     * Formats a byte value to a human-readable string
     * @param {number} bytes - The byte value to format
     * @returns {string} The formatted string
     */
    function formatBytes(bytes) {
        if (bytes === 0) return '0 B';

        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));

        return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Formats a number with thousands separators
     * @param {number} num - The number to format
     * @returns {string} The formatted string
     */
    function formatNumber(num) {
        return num.toLocaleString();
    }

    /**
     * Formats a ratio as a multiplier
     * @param {number} ratio - The ratio to format
     * @returns {string} The formatted string
     */
    function formatRatio(ratio) {
        return ratio > 0 ? ratio.toFixed(1) + 'x' : '-';
    }

    /**
     * Formats a value as a percentage
     * @param {number} value - The value to format
     * @returns {string} The formatted string
     */
    function formatPercent(value) {
        return (value * 100).toFixed(1) + '%';
    }

    // Initialize the dashboard when loaded
    init().catch(error => {
        console.error('Stickara: Failed to initialize storage dashboard:', error);
    });

    // Return the public API
    return {
        // Core dashboard operations
        init,
        show: () => {
            if (dashboardElement) {
                dashboardElement.style.display = 'flex';
                isVisible = true;
                refreshDashboard();
            }
        },
        hide: () => {
            if (dashboardElement) {
                dashboardElement.style.display = 'none';
                isVisible = false;
            }
        },
        toggle: () => {
            if (isVisible) {
                hide();
            } else {
                show();
            }
        },

        // Data access
        getPerformanceData: () => ({ ...performanceData }),
        refreshDashboard,
        exportData,

        // Configuration
        updateConfig: (newConfig) => {
            Object.assign(config, newConfig);
            return { ...config };
        },
        getConfig: () => ({ ...config })
    };
})();

console.log("Stickara: Storage Dashboard Loaded");
