﻿// --- START OF FILE content-template-editor.js ---

/**
 * Template Editor Compatibility Layer for Stickara
 * Redirects all template editing to the Visual Template Builder
 * This file maintains compatibility with existing code while transitioning to the visual system
 */

/**
 * Opens the template editor - now redirects to Visual Template Builder
 */
function openTemplateEditor() {
    // Always redirect to Visual Template Builder
    if (typeof openVisualTemplateBuilder === 'function') {
        openVisualTemplateBuilder();
        return;
    }

    // Fallback notification if Visual Template Builder is not available
    showNotificationMessage('🎨 Visual Template Builder is loading...', 'info');

    // Try again after a short delay
    setTimeout(() => {
        if (typeof openVisualTemplateBuilder === 'function') {
            openVisualTemplateBuilder();
        } else {
            showNotificationMessage('❌ Visual Template Builder is not available. Please refresh the page.', 'error');
        }
    }, 1000);
}

/**
 * Legacy function - redirects to Visual Template Builder
 */
function closeTemplateEditor() {
    // This function is kept for compatibility but does nothing
    // since we redirect to Visual Template Builder
    if (typeof closeVisualTemplateBuilder === 'function') {
        closeVisualTemplateBuilder();
    }
}

/**
 * Show notification message
 */
function showNotificationMessage(message, type = 'info') {
    // Try to use existing notification system
    if (typeof showStatus === 'function') {
        showStatus(message, type);
        return;
    }

    // Fallback notification
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 6px;
        color: white;
        font-weight: bold;
        z-index: 2147483652;
        max-width: 300px;
        font-family: Arial, sans-serif;
        font-size: 14px;
    `;

    // Set background color based on type
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };

    notification.style.backgroundColor = colors[type] || colors.info;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            document.body.removeChild(notification);
        }
    }, 3000);
}

// Legacy compatibility functions - all redirect to Visual Template Builder

/**
 * Legacy template management functions
 */
function refreshTemplateList() {
    // Redirect to Visual Template Builder
    openTemplateEditor();
}

function editTemplate(templateName) {
    // Redirect to Visual Template Builder
    openTemplateEditor();
}

function deleteTemplate(templateName) {
    // Use template manager if available
    if (typeof templateManager !== 'undefined' && templateManager.deleteTemplate) {
        if (confirm(`Are you sure you want to delete the template "${templateName}"?`)) {
            const success = templateManager.deleteTemplate(templateName);
            if (success) {
                showNotificationMessage(`Template "${templateName}" deleted successfully`, 'success');
            } else {
                showNotificationMessage(`Failed to delete template "${templateName}"`, 'error');
            }
        }
    } else {
        showNotificationMessage('Template management not available', 'error');
    }
}

function saveTemplate() {
    // Redirect to Visual Template Builder
    openTemplateEditor();
}

function testTemplate() {
    // Redirect to Visual Template Builder
    openTemplateEditor();
}

function importTemplates() {
    // Redirect to Visual Template Builder
    openTemplateEditor();
}

function exportTemplates() {
    // Redirect to Visual Template Builder
    openTemplateEditor();
}

// Legacy helper functions
function insertTemplateSnippet(snippet) {
    // These functions are no longer needed with Visual Template Builder
    showNotificationMessage('Please use the Visual Template Builder for template creation', 'info');
    openTemplateEditor();
}

function wrapSelectedText(before, after) {
    // These functions are no longer needed with Visual Template Builder
    showNotificationMessage('Please use the Visual Template Builder for template formatting', 'info');
    openTemplateEditor();
}

console.log("Stickara: Template Editor Compatibility Layer Loaded - All functions redirect to Visual Template Builder");

// Make functions available globally for compatibility
window.openTemplateEditor = openTemplateEditor;
window.closeTemplateEditor = closeTemplateEditor;
window.refreshTemplateList = refreshTemplateList;
window.editTemplate = editTemplate;
window.deleteTemplate = deleteTemplate;

// --- END OF FILE content-template-editor.js ---
