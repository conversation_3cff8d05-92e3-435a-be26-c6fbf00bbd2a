/**
 * Stickara - Rendering Optimization Module
 * This module optimizes the rendering pipeline for smoother animations and transitions.
 * It implements various performance techniques like:
 * - Debouncing and throttling for expensive operations
 * - Batching DOM updates
 * - Using requestAnimationFrame for smooth animations
 * - Preventing layout thrashing
 */

// Make sure we don't redefine functions if the script is injected multiple times
if (typeof window.SB_RENDERING_OPTIMIZATION_LOADED === 'undefined') {
    window.SB_RENDERING_OPTIMIZATION_LOADED = true;

    console.log("Stickara: Loading Rendering Optimization Module v1.0...");

    // --- Utility Functions ---

    /**
     * Debounce function to limit how often a function can be called
     * @param {Function} func - The function to debounce
     * @param {number} wait - The time to wait in milliseconds
     * @param {boolean} immediate - Whether to call the function immediately
     * @returns {Function} - The debounced function
     */
    window.debounce = function(func, wait, immediate) {
        let timeout;
        return function() {
            const context = this, args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    };

    /**
     * Throttle function to limit how often a function can be called
     * @param {Function} func - The function to throttle
     * @param {number} limit - The time limit in milliseconds
     * @returns {Function} - The throttled function
     */
    window.throttle = function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    };

    /**
     * Batch DOM updates to prevent layout thrashing
     * @param {Function} updateFn - The function that performs DOM updates
     */
    window.batchDOMUpdates = function(updateFn) {
        if (window.requestAnimationFrame) {
            window.requestAnimationFrame(() => {
                updateFn();
            });
        } else {
            // Fallback for browsers that don't support requestAnimationFrame
            setTimeout(updateFn, 0);
        }
    };

    /**
     * Optimize animations by using requestAnimationFrame and transform/opacity
     * @param {HTMLElement} element - The element to animate
     * @param {Object} properties - The CSS properties to animate
     * @param {number} duration - The duration of the animation in milliseconds
     * @param {string} easing - The easing function to use
     * @param {Function} callback - The callback to call when the animation is complete
     */
    window.optimizedAnimate = function(element, properties, duration, easing, callback) {
        if (!element) return;

        // Default values
        duration = duration || 300;
        easing = easing || 'ease';

        // Store original transition
        const originalTransition = element.style.transition;

        // Set transition
        element.style.transition = `all ${duration}ms ${easing}`;

        // Apply properties
        window.batchDOMUpdates(() => {
            for (const prop in properties) {
                if (properties.hasOwnProperty(prop)) {
                    // Use transform and opacity when possible for better performance
                    if (prop === 'left' || prop === 'top') {
                        // Use transform instead of left/top
                        const currentTransform = window.getComputedStyle(element).transform;
                        const matrix = new DOMMatrix(currentTransform);
                        const currentX = matrix.e;
                        const currentY = matrix.f;

                        let newX = currentX;
                        let newY = currentY;

                        if (prop === 'left') {
                            const currentLeft = parseInt(window.getComputedStyle(element).left) || 0;
                            newX = currentX + (parseInt(properties[prop]) - currentLeft);
                        } else if (prop === 'top') {
                            const currentTop = parseInt(window.getComputedStyle(element).top) || 0;
                            newY = currentY + (parseInt(properties[prop]) - currentTop);
                        }

                        element.style.transform = `translate(${newX}px, ${newY}px)`;
                    } else {
                        element.style[prop] = properties[prop];
                    }
                }
            }
        });

        // Set up callback
        const onTransitionEnd = function() {
            element.removeEventListener('transitionend', onTransitionEnd);
            element.style.transition = originalTransition;
            if (typeof callback === 'function') {
                callback();
            }
        };

        element.addEventListener('transitionend', onTransitionEnd);
    };

    /**
     * Optimize scrolling by using passive event listeners and throttling
     * @param {HTMLElement} element - The element to optimize scrolling for
     */
    window.optimizeScrolling = function(element) {
        if (!element) return;

        // Remove existing scroll listeners
        const oldScroll = element.onscroll;
        element.onscroll = null;

        // Add throttled scroll listener with passive option
        if (oldScroll) {
            const throttledScroll = window.throttle(oldScroll, 16); // ~60fps
            element.addEventListener('scroll', throttledScroll, { passive: true });
        }
    };

    /**
     * Optimize resizing by using throttling and batching
     * @param {HTMLElement} element - The element to optimize resizing for
     * @param {Function} resizeHandler - The function to call when resizing
     */
    window.optimizeResizing = function(element, resizeHandler) {
        if (!element || !resizeHandler) return;

        const throttledResize = window.throttle(function() {
            window.batchDOMUpdates(resizeHandler);
        }, 16); // ~60fps

        element.addEventListener('resize', throttledResize, { passive: true });

        return throttledResize; // Return the function so it can be removed later
    };

    /**
     * Optimize drag operations by using requestAnimationFrame
     * @param {Function} dragUpdateFn - The function that updates the element position
     * @returns {Function} - The optimized drag update function
     */
    window.optimizeDragOperation = function(dragUpdateFn) {
        let ticking = false;

        return function(event) {
            if (!ticking) {
                window.requestAnimationFrame(() => {
                    dragUpdateFn(event);
                    ticking = false;
                });
                ticking = true;
            }
        };
    };

    // --- Apply Optimizations to Existing Elements ---

    /**
     * Apply rendering optimizations to the main note container
     */
    window.optimizeNoteContainer = function() {
        const noteContainer = document.getElementById('Stickara-container');
        if (!noteContainer) return;

        // Add will-change hint when dragging starts
        const originalMouseDown = noteContainer.onmousedown;
        noteContainer.onmousedown = function(e) {
            if (e.target.id === 'Stickara-header') {
                noteContainer.style.willChange = 'transform';
            }
            if (originalMouseDown) originalMouseDown.call(this, e);
        };

        // Remove will-change hint when dragging ends
        document.addEventListener('mouseup', function() {
            if (noteContainer) {
                // Delay removal to ensure smooth animation completion
                setTimeout(() => {
                    noteContainer.style.willChange = '';
                }, 200);
            }
        });

        // Optimize scrolling in the note text area
        const noteText = document.getElementById('Stickara-text');
        if (noteText) {
            window.optimizeScrolling(noteText);
        }
    };

    /**
     * Apply rendering optimizations to highlight elements
     */
    window.optimizeHighlights = function() {
        // Find all highlight elements
        const highlights = document.querySelectorAll('.Stickara-highlight');

        highlights.forEach(highlight => {
            // DISABLED: will-change optimization for highlights with linked notes to prevent blinking
            if (!highlight.classList.contains('has-linked-note')) {
                // Add will-change hint when hovering over a highlight (only for highlights without notes)
                highlight.addEventListener('mouseenter', function() {
                    this.style.willChange = 'filter, box-shadow';
                });

                // Remove will-change hint when leaving a highlight
                highlight.addEventListener('mouseleave', function() {
                    this.style.willChange = '';
                });
            }

            // Find note display elements within highlights
            const noteDisplay = highlight.querySelector('.Stickara-highlight-note-display');
            if (noteDisplay) {
                // Add will-change hint when dragging starts
                const dragHandle = noteDisplay.querySelector('.Stickara-note-drag-handle');
                if (dragHandle) {
                    dragHandle.addEventListener('mousedown', function() {
                        noteDisplay.style.willChange = 'transform';
                    });
                }

                // Remove will-change hint when dragging ends
                document.addEventListener('mouseup', function() {
                    if (noteDisplay) {
                        // Delay removal to ensure smooth animation completion
                        setTimeout(() => {
                            noteDisplay.style.willChange = '';
                        }, 200);
                    }
                });
            }
        });
    };

    /**
     * Apply rendering optimizations to modal elements
     */
    window.optimizeModals = function() {
        // Find all modal elements
        const modals = document.querySelectorAll('.Stickara-modal-content, #Stickara-flashcard-modal');

        modals.forEach(modal => {
            // Add will-change hint when the modal appears
            if (modal.style.display === 'block' || modal.style.display === 'flex') {
                modal.style.willChange = 'transform, opacity';

                // Remove will-change hint after animation completes
                setTimeout(() => {
                    modal.style.willChange = '';
                }, 300); // Assuming animation duration is 300ms
            }

            // Find close buttons
            const closeButtons = modal.querySelectorAll('.Stickara-modal-close, .Stickara-btn');
            closeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Add will-change hint for closing animation
                    modal.style.willChange = 'transform, opacity';
                });
            });
        });
    };

    /**
     * Apply rendering optimizations to the quick snippet button
     */
    window.optimizeQuickSnippetButton = function() {
        // Find all quick snippet buttons
        const snippetButtons = document.querySelectorAll('.Stickara-quick-snippet-btn');

        snippetButtons.forEach(button => {
            // Add will-change hint when hovering
            button.addEventListener('mouseenter', function() {
                this.style.willChange = 'transform, box-shadow';
            });

            // Remove will-change hint when leaving
            button.addEventListener('mouseleave', function() {
                this.style.willChange = '';
            });
        });
    };

    /**
     * Apply all rendering optimizations
     */
    window.applyRenderingOptimizations = function() {
        console.log("Stickara: Applying rendering optimizations...");

        // Optimize note container
        window.optimizeNoteContainer();

        // Optimize highlights
        window.optimizeHighlights();

        // Optimize modals
        window.optimizeModals();

        // Optimize quick snippet button
        window.optimizeQuickSnippetButton();

        // Optimize window resize handling
        window.optimizeResizing(window, function() {
            // Update positions of any fixed elements
            const noteContainer = document.getElementById('Stickara-container');
            if (noteContainer && noteContainer.style.display !== 'none') {
                // Ensure the note container stays within viewport
                const rect = noteContainer.getBoundingClientRect();
                if (rect.right > window.innerWidth) {
                    noteContainer.style.left = (window.innerWidth - rect.width - 20) + 'px';
                }
                if (rect.bottom > window.innerHeight) {
                    noteContainer.style.top = (window.innerHeight - rect.height - 20) + 'px';
                }
            }
        });

        console.log("Stickara: Rendering optimizations applied");
    };

    // --- Patch Existing Functions ---

    /**
     * Patch the showNote function to use optimized animations
     */
    window.patchShowNoteFunction = function() {
        if (typeof window.showNote === 'function') {
            const originalShowNote = window.showNote;

            window.showNote = function() {
                const noteContainer = document.getElementById('Stickara-container');
                if (!noteContainer) {
                    return originalShowNote.apply(this, arguments);
                }

                // Check if the note is already visible
                if (noteContainer.classList.contains('visible')) {
                    return;
                }

                // Add will-change hint before animation
                noteContainer.style.willChange = 'transform, opacity';

                // Call the original function
                const result = originalShowNote.apply(this, arguments);

                // Remove will-change hint after animation completes
                setTimeout(() => {
                    noteContainer.style.willChange = '';
                }, 300); // Assuming animation duration is 300ms

                return result;
            };

            console.log("Stickara: Patched showNote function for optimized animations");
        }
    };

    /**
     * Patch the hideNote function to use optimized animations
     */
    window.patchHideNoteFunction = function() {
        if (typeof window.hideNote === 'function') {
            const originalHideNote = window.hideNote;

            window.hideNote = function() {
                const noteContainer = document.getElementById('Stickara-container');
                if (!noteContainer) {
                    return originalHideNote.apply(this, arguments);
                }

                // Check if the note is already hidden
                if (!noteContainer.classList.contains('visible')) {
                    return;
                }

                // Add will-change hint before animation
                noteContainer.style.willChange = 'transform, opacity';

                // Call the original function
                const result = originalHideNote.apply(this, arguments);

                // No need to remove will-change hint as the element will be hidden

                return result;
            };


        }
    };

    /**
     * Patch the makeDraggable function to use optimized drag operations
     */
    window.patchMakeDraggableFunction = function() {
        if (typeof window.makeDraggable === 'function') {
            const originalMakeDraggable = window.makeDraggable;

            window.makeDraggable = function(element, container, highlightId) {
                if (!element) {
                    return originalMakeDraggable.apply(this, arguments);
                }

                // Add contain: layout to the element
                element.style.contain = element.style.contain || 'content';

                // Call the original function
                return originalMakeDraggable.apply(this, arguments);
            };


        }
    };

    /**
     * Apply all function patches
     */
    window.applyFunctionPatches = function() {
        console.log("Stickara: Applying function patches for rendering optimization...");

        // Patch showNote function
        window.patchShowNoteFunction();

        // Patch hideNote function
        window.patchHideNoteFunction();

        // Patch makeDraggable function
        window.patchMakeDraggableFunction();

        console.log("Stickara: Function patches applied");
    };

    // --- Initialize Optimizations ---

    /**
     * Initialize all rendering optimizations
     */
    window.initRenderingOptimizations = function() {
        console.log("Stickara: Initializing rendering optimizations...");

        // Apply CSS optimizations
        window.applyRenderingOptimizations();

        // Apply function patches
        window.applyFunctionPatches();

        // Set up MutationObserver to optimize new elements
        const observer = new MutationObserver(function(mutations) {
            let shouldOptimize = false;

            mutations.forEach(function(mutation) {
                if (mutation.addedNodes.length > 0) {
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check if this is a Stickara element
                            if (node.id && node.id.startsWith('Stickara-') ||
                                node.classList && Array.from(node.classList).some(c => c.startsWith('Stickara-'))) {
                                shouldOptimize = true;
                                break;
                            }
                        }
                    }
                }
            });

            if (shouldOptimize) {
                // Debounce the optimization to avoid multiple calls
                window.debounce(window.applyRenderingOptimizations, 100)();
            }
        });

        // Observe the entire document for Stickara elements
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log("Stickara: Rendering optimizations initialized");
    };

    // Auto-initialize when the script is loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', window.initRenderingOptimizations);
    } else {
        // DOM is already ready
        setTimeout(window.initRenderingOptimizations, 500); // Small delay to ensure other scripts are loaded
    }

    console.log("Stickara: Rendering Optimization Module Loaded");
}
