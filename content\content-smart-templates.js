// --- START OF FILE content-smart-templates.js ---

/**
 * Advanced Smart Templates System for Stickara
 * Supports dynamic prompts, conditional logic, and date calculations
 */

// Smart template processing state
let smartTemplateProcessingActive = false;
let smartTemplateUserInputs = {};

/**
 * Main function to process smart templates with advanced features
 * @param {string} templateContent - The template content with smart syntax
 * @returns {Promise<string>} - Processed template content
 */
async function processSmartTemplate(templateContent) {
    if (typeof templateContent !== 'string') return '';

    console.log("--- Processing Smart Template ---");
    smartTemplateProcessingActive = true;
    smartTemplateUserInputs = {};

    try {
        // First process basic placeholders for backward compatibility
        let processedContent = processBasicPlaceholders(templateContent);

        // Process smart template features
        processedContent = await processPrompts(processedContent);
        processedContent = await processConditionalLogic(processedContent);
        processedContent = await processDateCalculations(processedContent);
        processedContent = await processTableSyntax(processedContent);
        processedContent = await processContextVariables(processedContent);

        console.log("--- Smart Template Processing Complete ---");
        return processedContent;
    } catch (error) {
        console.error("Smart Template Processing Error:", error);
        if (typeof showStatus === 'function') {
            showStatus('Error processing smart template: ' + error.message, 'error');
        }
        return templateContent; // Return original on error
    } finally {
        smartTemplateProcessingActive = false;
    }
}

/**
 * Process basic placeholders for backward compatibility
 * @param {string} content - Template content
 * @returns {string} - Content with basic placeholders replaced
 */
function processBasicPlaceholders(content) {
    // {DATE}
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const formattedDate = `${year}-${month}-${day}`;
    content = content.replaceAll('{DATE}', formattedDate);

    // {URL}
    const currentUrl = window.location.href;
    content = content.replaceAll('{URL}', currentUrl);

    // {SELECTION}
    const selection = getSelectedText ? getSelectedText() : '';
    content = content.replaceAll('{SELECTION}', selection);

    return content;
}

/**
 * Process user prompts in templates
 * @param {string} content - Template content
 * @returns {Promise<string>} - Content with prompts processed
 */
async function processPrompts(content) {
    // Match {{Prompt:question|default}} or {{Prompt:question}}
    const promptRegex = /\{\{Prompt:([^|}]+)(?:\|([^}]*))?\}\}/g;
    let match;
    const prompts = [];

    // Collect all prompts first
    while ((match = promptRegex.exec(content)) !== null) {
        prompts.push({
            fullMatch: match[0],
            question: match[1].trim(),
            defaultValue: match[2] ? match[2].trim() : '',
            index: match.index
        });
    }

    // Process prompts in order
    for (const prompt of prompts) {
        let userInput;

        // Check if this is a dropdown selection
        if (prompt.defaultValue.includes(',')) {
            userInput = await showDropdownPrompt(prompt.question, prompt.defaultValue.split(','));
        } else {
            userInput = await showTextPrompt(prompt.question, prompt.defaultValue);
        }

        if (userInput !== null) {
            content = content.replace(prompt.fullMatch, userInput);
            smartTemplateUserInputs[prompt.question] = userInput;
        } else {
            // User cancelled, replace with default or empty
            content = content.replace(prompt.fullMatch, prompt.defaultValue);
        }
    }

    return content;
}

/**
 * Show text input prompt to user
 * @param {string} question - The question to ask
 * @param {string} defaultValue - Default value
 * @returns {Promise<string|null>} - User input or null if cancelled
 */
function showTextPrompt(question, defaultValue = '') {
    return new Promise((resolve) => {
        const userInput = prompt(question, defaultValue);
        resolve(userInput);
    });
}

/**
 * Show dropdown selection prompt to user
 * @param {string} question - The question to ask
 * @param {string[]} options - Available options
 * @returns {Promise<string|null>} - Selected option or null if cancelled
 */
function showDropdownPrompt(question, options) {
    return new Promise((resolve) => {
        // Create a simple selection dialog
        const optionsText = options.map((opt, idx) => `${idx + 1}. ${opt.trim()}`).join('\n');
        const message = `${question}\n\n${optionsText}\n\nEnter the number of your choice (1-${options.length}):`;

        const userChoice = prompt(message);

        if (userChoice === null) {
            resolve(null);
            return;
        }

        const choiceIndex = parseInt(userChoice) - 1;
        if (choiceIndex >= 0 && choiceIndex < options.length) {
            resolve(options[choiceIndex].trim());
        } else {
            // Invalid choice, use first option as default
            resolve(options[0].trim());
        }
    });
}

/**
 * Process conditional logic blocks
 * @param {string} content - Template content
 * @returns {Promise<string>} - Content with conditionals processed
 */
async function processConditionalLogic(content) {
    // Match {{If:condition}} ... {{EndIf}} blocks with optional {{Else}}
    const conditionalRegex = /\{\{If:([^}]+)\}\}([\s\S]*?)(?:\{\{Else\}\}([\s\S]*?))?\{\{EndIf\}\}/g;
    let match;

    while ((match = conditionalRegex.exec(content)) !== null) {
        const condition = match[1].trim();
        const ifContent = match[2];
        const elseContent = match[3] || '';

        const conditionResult = await evaluateCondition(condition);
        const replacementContent = conditionResult ? ifContent : elseContent;

        content = content.replace(match[0], replacementContent);
    }

    return content;
}

/**
 * Evaluate a condition
 * @param {string} condition - The condition to evaluate
 * @returns {Promise<boolean>} - Condition result
 */
async function evaluateCondition(condition) {
    const lowerCondition = condition.toLowerCase();

    // Check for different condition types
    if (lowerCondition === 'hastags') {
        return checkHasTags();
    } else if (lowerCondition === 'reminderset') {
        return checkReminderSet();
    } else if (lowerCondition === 'hasselection') {
        return checkHasSelection();
    } else if (lowerCondition === 'weekend') {
        return checkIsWeekend();
    } else if (lowerCondition.startsWith('urlcontains:')) {
        const keywords = lowerCondition.substring(12).split(',');
        return checkUrlContains(keywords);
    } else if (lowerCondition.startsWith('daterange:')) {
        const range = lowerCondition.substring(10);
        return checkDateRange(range);
    }

    return false;
}

/**
 * Check if current note has tags
 * @returns {boolean}
 */
function checkHasTags() {
    // Access current note data if available
    if (typeof notes !== 'undefined' && typeof currentNoteIndex !== 'undefined') {
        const currentNote = notes[currentNoteIndex];
        return currentNote && currentNote.tags && currentNote.tags.length > 0;
    }
    return false;
}

/**
 * Check if current note has reminder set
 * @returns {boolean}
 */
function checkReminderSet() {
    if (typeof notes !== 'undefined' && typeof currentNoteIndex !== 'undefined') {
        const currentNote = notes[currentNoteIndex];
        return currentNote && currentNote.reminder !== null;
    }
    return false;
}

/**
 * Check if there's selected text
 * @returns {boolean}
 */
function checkHasSelection() {
    const selection = getSelectedText ? getSelectedText() : '';
    return selection.length > 0;
}

/**
 * Check if current day is weekend
 * @returns {boolean}
 */
function checkIsWeekend() {
    const today = new Date();
    const dayOfWeek = today.getDay();
    return dayOfWeek === 0 || dayOfWeek === 6; // Sunday = 0, Saturday = 6
}

/**
 * Check if URL contains any of the keywords
 * @param {string[]} keywords - Keywords to check
 * @returns {boolean}
 */
function checkUrlContains(keywords) {
    const currentUrl = window.location.href.toLowerCase();
    return keywords.some(keyword => currentUrl.includes(keyword.trim()));
}

/**
 * Check date range conditions
 * @param {string} range - Date range identifier
 * @returns {boolean}
 */
function checkDateRange(range) {
    const today = new Date();
    const month = today.getMonth() + 1; // 1-12

    switch (range) {
        case 'exam-season':
            // Assume exam season is May and December
            return month === 5 || month === 12;
        case 'holiday-season':
            // November and December
            return month === 11 || month === 12;
        case 'summer':
            // June, July, August
            return month >= 6 && month <= 8;
        case 'winter':
            // December, January, February
            return month === 12 || month === 1 || month === 2;
        default:
            return false;
    }
}

/**
 * Process date calculations
 * @param {string} content - Template content
 * @returns {Promise<string>} - Content with date calculations processed
 */
async function processDateCalculations(content) {
    // Match {{DueDate+7days}}, {{CurrentDate:format}}, {{Tomorrow}}, etc.
    const dateRegex = /\{\{(DueDate|CurrentDate|Tomorrow|Yesterday)([+-]\d+(?:days?|weeks?|months?))?(?::([^}]+))?\}\}/g;
    let match;

    while ((match = dateRegex.exec(content)) !== null) {
        const dateType = match[1];
        const calculation = match[2];
        const format = match[3];

        let resultDate = new Date();

        // Set base date
        switch (dateType) {
            case 'Tomorrow':
                resultDate.setDate(resultDate.getDate() + 1);
                break;
            case 'Yesterday':
                resultDate.setDate(resultDate.getDate() - 1);
                break;
            case 'DueDate':
                // For DueDate, start from today and apply calculation
                break;
            case 'CurrentDate':
            default:
                // Use current date
                break;
        }

        // Apply calculation if present
        if (calculation) {
            resultDate = applyDateCalculation(resultDate, calculation);
        }

        const formattedDate = formatDate(resultDate, format);
        content = content.replace(match[0], formattedDate);
    }

    return content;
}

/**
 * Apply date calculation to a date
 * @param {Date} date - Base date
 * @param {string} calculation - Calculation string like "+7days"
 * @returns {Date} - Calculated date
 */
function applyDateCalculation(date, calculation) {
    const match = calculation.match(/([+-])(\d+)(days?|weeks?|months?)/);
    if (!match) return date;

    const operator = match[1];
    const amount = parseInt(match[2]);
    const unit = match[3];

    const multiplier = operator === '+' ? 1 : -1;
    const finalAmount = amount * multiplier;

    const newDate = new Date(date);

    if (unit.startsWith('day')) {
        newDate.setDate(newDate.getDate() + finalAmount);
    } else if (unit.startsWith('week')) {
        newDate.setDate(newDate.getDate() + (finalAmount * 7));
    } else if (unit.startsWith('month')) {
        newDate.setMonth(newDate.getMonth() + finalAmount);
    }

    return newDate;
}

/**
 * Format date for display
 * @param {Date} date - Date to format
 * @param {string} format - Optional format string
 * @returns {string} - Formatted date string
 */
function formatDate(date, format) {
    if (!format) {
        // Default format: YYYY-MM-DD
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    // Handle different format types
    switch (format.toLowerCase()) {
        case 'short':
            return date.toLocaleDateString();
        case 'long':
            return date.toLocaleDateString(undefined, {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        case 'medium':
            return date.toLocaleDateString(undefined, {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        case 'iso':
            return date.toISOString().split('T')[0];
        case 'mmmm do, yyyy':
            return date.toLocaleDateString(undefined, {
                month: 'long',
                day: 'numeric',
                year: 'numeric'
            });
        default:
            // Try to parse custom format
            try {
                return date.toLocaleDateString(undefined, {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });
            } catch (e) {
                // Fallback to default
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }
    }
}

/**
 * Process table syntax
 * @param {string} content - Template content
 * @returns {Promise<string>} - Content with table syntax processed
 */
async function processTableSyntax(content) {
    // Match {{Table:rows x cols|options}} syntax
    const tableRegex = /\{\{Table:(\d+)x(\d+)(?:\|([^}]+))?\}\}/g;
    let match;

    while ((match = tableRegex.exec(content)) !== null) {
        const rows = parseInt(match[1]);
        const cols = parseInt(match[2]);
        const options = match[3] ? parseTableOptions(match[3]) : {};

        const tableHtml = generateSmartTable(rows, cols, options);
        content = content.replace(match[0], tableHtml);
    }

    // Process table data prompts
    const tablePromptRegex = /\{\{TablePrompt:([^}]+)\}\}/g;
    while ((match = tablePromptRegex.exec(content)) !== null) {
        const promptText = match[1];
        const userInput = await showTextPrompt(promptText, '');
        content = content.replace(match[0], userInput || '');
    }

    return content;
}

/**
 * Parse table options from string
 * @param {string} optionsStr - Options string
 * @returns {object} - Parsed options
 */
function parseTableOptions(optionsStr) {
    const options = {};
    const pairs = optionsStr.split('|');

    pairs.forEach(pair => {
        const [key, value] = pair.split('=');
        if (key && value) {
            options[key.trim()] = value.trim();
        }
    });

    return options;
}

/**
 * Generate smart table HTML
 * @param {number} rows - Number of rows
 * @param {number} cols - Number of columns
 * @param {object} options - Table options
 * @returns {string} - Generated table HTML
 */
function generateSmartTable(rows, cols, options) {
    const hasHeaders = options.headers === 'true';
    const style = options.style || 'basic';
    const responsive = options.responsive === 'true';

    let tableClass = 'stickara-smart-table';
    let tableStyle = 'width: 100%; border-collapse: collapse;';

    switch (style) {
        case 'bordered':
            tableClass += ' table-bordered';
            break;
        case 'striped':
            tableClass += ' table-striped';
            break;
        case 'data':
            tableClass += ' table-data';
            break;
    }

    if (responsive) {
        tableClass += ' table-responsive';
    }

    let html = `<table class="${tableClass}" style="${tableStyle}">`;

    for (let row = 0; row < rows; row++) {
        html += '<tr>';

        for (let col = 0; col < cols; col++) {
            const isHeaderRow = hasHeaders && row === 0;
            const tag = isHeaderRow ? 'th' : 'td';
            const cellStyle = getSmartTableCellStyle(style);

            if (isHeaderRow) {
                html += `<${tag} style="${cellStyle}">{{TablePrompt:Header for column ${col + 1}|Column ${col + 1}}}</${tag}>`;
            } else {
                html += `<${tag} style="${cellStyle}">{{TablePrompt:Data for row ${row + 1}, column ${col + 1}|Data}}</${tag}>`;
            }
        }

        html += '</tr>';
    }

    html += '</table>';

    return html;
}

/**
 * Get cell styles for smart tables
 * @param {string} style - Table style
 * @returns {string} - CSS styles
 */
function getSmartTableCellStyle(style) {
    const baseStyle = 'padding: 8px; text-align: left;';

    switch (style) {
        case 'bordered':
            return baseStyle + ' border: 1px solid #ddd;';
        case 'striped':
            return baseStyle + ' border-bottom: 1px solid #eee;';
        case 'data':
            return baseStyle + ' border: 1px solid #ddd; background: #f9f9f9;';
        default:
            return baseStyle + ' border-bottom: 1px solid #eee;';
    }
}

/**
 * Process context variables
 * @param {string} content - Template content
 * @returns {Promise<string>} - Content with context variables processed
 */
async function processContextVariables(content) {
    // Replace context variables like {{Tags}}, {{ReminderDate}}, etc.
    if (typeof notes !== 'undefined' && typeof currentNoteIndex !== 'undefined') {
        const currentNote = notes[currentNoteIndex];

        if (currentNote) {
            // Replace {{Tags}}
            if (currentNote.tags && currentNote.tags.length > 0) {
                content = content.replaceAll('{{Tags}}', currentNote.tags.join(', '));
            } else {
                content = content.replaceAll('{{Tags}}', '');
            }

            // Replace {{ReminderDate}}
            if (currentNote.reminder) {
                const reminderDate = new Date(currentNote.reminder);
                content = content.replaceAll('{{ReminderDate}}', formatDate(reminderDate));
            } else {
                content = content.replaceAll('{{ReminderDate}}', '');
            }
        }
    }

    // Replace {{CurrentTime}}
    const now = new Date();
    const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    content = content.replaceAll('{{CurrentTime}}', timeString);

    return content;
}

// Make functions available globally
window.processSmartTemplate = processSmartTemplate;
window.smartTemplateProcessingActive = () => smartTemplateProcessingActive;

console.log("Stickara: Smart Templates Engine Loaded");

// --- END OF FILE content-smart-templates.js ---
