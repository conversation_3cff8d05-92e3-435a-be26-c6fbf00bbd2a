// --- START OF FILE permission-manager.js ---

/**
 * Advanced Permission Management System for Stickara
 *
 * This module provides fine-grained permission control for the extension,
 * implementing the principle of least privilege and just-in-time permissions.
 */

// Create a namespace to avoid global pollution
window.StickaraPermissions = (function() {

    /**
     * Permission states
     */
    const PERMISSION_STATES = {
        GRANTED: 'granted',
        DENIED: 'denied',
        PROMPT: 'prompt'
    };

    /**
     * Permission definitions with descriptions and justifications
     */
    const PERMISSIONS = {
        storage: {
            description: 'Store your notes and settings',
            justification: 'Required to save your notes and preferences',
            optional: false
        },
        activeTab: {
            description: 'Access the current tab',
            justification: 'Required to create notes on the current webpage',
            optional: false
        },
        tabs: {
            description: 'Access browser tabs',
            justification: 'Required to track tab changes and display notes on the correct pages',
            optional: false
        },
        notifications: {
            description: 'Show notifications',
            justification: 'Required for reminder notifications',
            optional: true
        },
        downloads: {
            description: 'Download files',
            justification: 'Required to export notes as files',
            optional: true
        },
        identity: {
            description: 'Access your Google account',
            justification: 'Required for Google Drive sync and Calendar integration',
            optional: true
        }
    };

    /**
     * Current permission state
     */
    let permissionState = {};

    /**
     * Initializes the permission manager
     * @returns {Promise<void>}
     */
    async function initialize() {
        try {
            // Load saved permission state
            const result = await new Promise(resolve => {
                chrome.storage.local.get('permissionState', resolve);
            });

            permissionState = result.permissionState || {};

            // Check current permissions
            await refreshPermissionState();

            console.log("Stickara: Permission Manager initialized");
        } catch (e) {
            console.error("Stickara: Error initializing Permission Manager:", e);
        }
    }

    /**
     * Refreshes the current permission state
     * @returns {Promise<void>}
     */
    async function refreshPermissionState() {
        try {
            // Check each permission
            for (const permission of Object.keys(PERMISSIONS)) {
                const hasPermission = await checkPermission(permission);
                permissionState[permission] = hasPermission ?
                    PERMISSION_STATES.GRANTED :
                    (permissionState[permission] === PERMISSION_STATES.DENIED ?
                        PERMISSION_STATES.DENIED :
                        PERMISSION_STATES.PROMPT);
            }

            // Save updated state
            await new Promise(resolve => {
                chrome.storage.local.set({ permissionState }, resolve);
            });
        } catch (e) {
            console.error("Stickara: Error refreshing permission state:", e);
        }
    }

    /**
     * Checks if a permission is currently granted
     * @param {string} permission - The permission to check
     * @returns {Promise<boolean>} Whether the permission is granted
     */
    async function checkPermission(permission) {
        try {
            // Check if the permissions API is available
            if (!chrome.permissions || typeof chrome.permissions.contains !== 'function') {
                // If permissions API is not available, assume permission is granted
                // This happens in content scripts where permissions API is not accessible
                console.log(`Stickara: Permissions API not available, assuming ${permission} is granted`);
                return true;
            }

            // For Chrome extensions, we need to use the permissions API
            const result = await new Promise(resolve => {
                chrome.permissions.contains({ permissions: [permission] }, (result) => {
                    resolve(result === true);
                });
            });

            return result;
        } catch (e) {
            console.log(`Stickara: Error checking permission ${permission}, assuming granted:`, e);
            // If there's an error, assume the permission is granted to avoid blocking functionality
            return true;
        }
    }

    /**
     * Requests a permission from the user
     * @param {string} permission - The permission to request
     * @param {string} reason - The reason for requesting this permission
     * @returns {Promise<boolean>} Whether the permission was granted
     */
    async function requestPermission(permission, reason) {
        try {
            // Check if permission is defined
            if (!PERMISSIONS[permission]) {
                console.error(`Stickara: Unknown permission ${permission}`);
                return false;
            }

            // If already denied, show a different UI
            if (permissionState[permission] === PERMISSION_STATES.DENIED) {
                const userWantsToRetry = await showPermissionRetryDialog(permission, reason);
                if (!userWantsToRetry) {
                    return false;
                }
            }

            // Show permission request dialog
            await showPermissionRequestDialog(permission, reason);

            // Request the permission
            const granted = await new Promise(resolve => {
                chrome.permissions.request({ permissions: [permission] }, resolve);
            });

            // Update state
            permissionState[permission] = granted ?
                PERMISSION_STATES.GRANTED :
                PERMISSION_STATES.DENIED;

            // Save updated state
            await new Promise(resolve => {
                chrome.storage.local.set({ permissionState }, resolve);
            });

            return granted;
        } catch (e) {
            console.error(`Stickara: Error requesting permission ${permission}:`, e);
            return false;
        }
    }

    /**
     * Shows a permission request dialog
     * @param {string} permission - The permission being requested
     * @param {string} reason - The reason for requesting this permission
     * @returns {Promise<void>}
     */
    async function showPermissionRequestDialog(permission, reason) {
        return new Promise(resolve => {
            // Create dialog elements
            const overlay = document.createElement('div');
            overlay.style.position = 'fixed';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            overlay.style.zIndex = '9999';
            overlay.style.display = 'flex';
            overlay.style.justifyContent = 'center';
            overlay.style.alignItems = 'center';

            const dialog = document.createElement('div');
            dialog.style.backgroundColor = 'white';
            dialog.style.padding = '20px';
            dialog.style.borderRadius = '8px';
            dialog.style.maxWidth = '400px';
            dialog.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';

            const title = document.createElement('h2');
            title.textContent = 'Permission Request';
            title.style.margin = '0 0 16px 0';

            const description = document.createElement('p');
            description.textContent = `Stickara needs permission to: ${PERMISSIONS[permission].description}`;

            const justification = document.createElement('p');
            justification.textContent = PERMISSIONS[permission].justification;

            const customReason = document.createElement('p');
            customReason.textContent = reason || '';

            const buttonContainer = document.createElement('div');
            buttonContainer.style.display = 'flex';
            buttonContainer.style.justifyContent = 'flex-end';
            buttonContainer.style.marginTop = '16px';

            const cancelButton = document.createElement('button');
            cancelButton.textContent = 'Deny';
            cancelButton.style.marginRight = '8px';
            cancelButton.style.padding = '8px 16px';
            cancelButton.style.border = 'none';
            cancelButton.style.borderRadius = '4px';
            cancelButton.style.backgroundColor = '#f1f1f1';
            cancelButton.style.cursor = 'pointer';

            const allowButton = document.createElement('button');
            allowButton.textContent = 'Allow';
            allowButton.style.padding = '8px 16px';
            allowButton.style.border = 'none';
            allowButton.style.borderRadius = '4px';
            allowButton.style.backgroundColor = '#4CAF50';
            allowButton.style.color = 'white';
            allowButton.style.cursor = 'pointer';

            // Assemble dialog
            buttonContainer.appendChild(cancelButton);
            buttonContainer.appendChild(allowButton);

            dialog.appendChild(title);
            dialog.appendChild(description);
            dialog.appendChild(justification);
            if (reason) dialog.appendChild(customReason);
            dialog.appendChild(buttonContainer);

            overlay.appendChild(dialog);

            // Add event listeners
            cancelButton.addEventListener('click', () => {
                document.body.removeChild(overlay);
                resolve();
            });

            allowButton.addEventListener('click', () => {
                document.body.removeChild(overlay);
                resolve();
            });

            // Add to document
            document.body.appendChild(overlay);
        });
    }

    /**
     * Shows a dialog for retrying a previously denied permission
     * @param {string} permission - The permission being requested
     * @param {string} reason - The reason for requesting this permission
     * @returns {Promise<boolean>} Whether the user wants to retry
     */
    async function showPermissionRetryDialog(permission, reason) {
        return new Promise(resolve => {
            // Create dialog elements
            const overlay = document.createElement('div');
            overlay.style.position = 'fixed';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            overlay.style.zIndex = '9999';
            overlay.style.display = 'flex';
            overlay.style.justifyContent = 'center';
            overlay.style.alignItems = 'center';

            const dialog = document.createElement('div');
            dialog.style.backgroundColor = 'white';
            dialog.style.padding = '20px';
            dialog.style.borderRadius = '8px';
            dialog.style.maxWidth = '400px';
            dialog.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';

            const title = document.createElement('h2');
            title.textContent = 'Permission Previously Denied';
            title.style.margin = '0 0 16px 0';

            const description = document.createElement('p');
            description.textContent = `You previously denied permission for: ${PERMISSIONS[permission].description}`;

            const justification = document.createElement('p');
            justification.textContent = `This permission is needed to: ${PERMISSIONS[permission].justification}`;

            const customReason = document.createElement('p');
            customReason.textContent = reason || '';

            const buttonContainer = document.createElement('div');
            buttonContainer.style.display = 'flex';
            buttonContainer.style.justifyContent = 'flex-end';
            buttonContainer.style.marginTop = '16px';

            const cancelButton = document.createElement('button');
            cancelButton.textContent = 'Keep Denied';
            cancelButton.style.marginRight = '8px';
            cancelButton.style.padding = '8px 16px';
            cancelButton.style.border = 'none';
            cancelButton.style.borderRadius = '4px';
            cancelButton.style.backgroundColor = '#f1f1f1';
            cancelButton.style.cursor = 'pointer';

            const retryButton = document.createElement('button');
            retryButton.textContent = 'Try Again';
            retryButton.style.padding = '8px 16px';
            retryButton.style.border = 'none';
            retryButton.style.borderRadius = '4px';
            retryButton.style.backgroundColor = '#2196F3';
            retryButton.style.color = 'white';
            retryButton.style.cursor = 'pointer';

            // Assemble dialog
            buttonContainer.appendChild(cancelButton);
            buttonContainer.appendChild(retryButton);

            dialog.appendChild(title);
            dialog.appendChild(description);
            dialog.appendChild(justification);
            if (reason) dialog.appendChild(customReason);
            dialog.appendChild(buttonContainer);

            overlay.appendChild(dialog);

            // Add event listeners
            cancelButton.addEventListener('click', () => {
                document.body.removeChild(overlay);
                resolve(false);
            });

            retryButton.addEventListener('click', () => {
                document.body.removeChild(overlay);
                resolve(true);
            });

            // Add to document
            document.body.appendChild(overlay);
        });
    }

    /**
     * Checks if a feature can be used based on required permissions
     * @param {string|Array<string>} requiredPermissions - The required permission(s)
     * @returns {Promise<boolean>} Whether the feature can be used
     */
    async function canUseFeature(requiredPermissions) {
        // Convert single permission to array
        const permissions = Array.isArray(requiredPermissions) ?
            requiredPermissions : [requiredPermissions];

        // Check each permission
        for (const permission of permissions) {
            const hasPermission = await checkPermission(permission);
            if (!hasPermission) {
                return false;
            }
        }

        return true;
    }

    /**
     * Ensures a feature has all required permissions, requesting them if needed
     * @param {string|Array<string>} requiredPermissions - The required permission(s)
     * @param {string} featureName - The name of the feature
     * @param {string} reason - The reason for requesting these permissions
     * @returns {Promise<boolean>} Whether all permissions were granted
     */
    async function ensurePermissionsForFeature(requiredPermissions, featureName, reason) {
        // Convert single permission to array
        const permissions = Array.isArray(requiredPermissions) ?
            requiredPermissions : [requiredPermissions];

        // Check each permission
        for (const permission of permissions) {
            const hasPermission = await checkPermission(permission);

            if (!hasPermission) {
                const featureReason = `${featureName}: ${reason}`;
                const granted = await requestPermission(permission, featureReason);

                if (!granted) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Gets the current permission state
     * @returns {Object} The current permission state
     */
    function getPermissionState() {
        return { ...permissionState };
    }

    /**
     * Gets information about all permissions
     * @returns {Object} Information about all permissions
     */
    function getPermissionInfo() {
        return { ...PERMISSIONS };
    }

    // Return the public API
    return {
        initialize,
        checkPermission,
        requestPermission,
        canUseFeature,
        ensurePermissionsForFeature,
        getPermissionState,
        getPermissionInfo,
        PERMISSION_STATES
    };
})();

// Initialize the permission manager
window.StickaraPermissions.initialize().catch(e => {
    console.error("Stickara: Error initializing Permission Manager:", e);
});

console.log("Stickara: Permission Manager Loaded");
// --- END OF FILE permission-manager.js ---
