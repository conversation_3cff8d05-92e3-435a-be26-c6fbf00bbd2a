// --- START OF FILE content-search.js ---

// --- State variables specific to in-note search ---
let inNoteSearchQuery = ''; // The current search term
let inNoteHighlightElements = []; // Array to hold the found <mark> elements
let currentHighlightIndex = -1; // Index of the currently focused highlight (-1 for none)
const IN_NOTE_SEARCH_HIGHLIGHT_CLASS = 'Stickara-in-note-highlight'; // CSS class for search highlights
const IN_NOTE_SEARCH_HIGHLIGHT_ACTIVE_CLASS = 'active'; // CSS class for the active highlight
const IN_NOTE_SEARCH_COUNT_ID = 'Stickara-in-note-search-count';
const IN_NOTE_SEARCH_PREV_ID = 'Stickara-in-note-search-prev';
const IN_NOTE_SEARCH_NEXT_ID = 'Stickara-in-note-search-next';
// Assume `inNoteSearchActive` and `noteText` are defined globally (e.g., in content-state.js)
// --- End State variables ---

/**
 * Handles input events in the in-note search input field.
 * @param {Event} event - The input event.
 */
function handleInNoteSearchInput(event) {
    const query = event.target.value;
    // Basic debouncing to avoid searching on every keystroke
    clearTimeout(window.StickaraInNoteSearchTimeout);
    window.StickaraInNoteSearchTimeout = setTimeout(() => {
        performInNoteSearch(query);
    }, 300); // 300ms debounce
}

/**
 * Handles keydown events in the in-note search input field (Enter, Escape).
 * @param {KeyboardEvent} event - The keydown event.
 */
function handleInNoteSearchKeydown(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        const direction = event.shiftKey ? -1 : 1; // Shift+Enter = Previous
        navigateInNoteHighlight(direction);
    } else if (event.key === 'Escape') {
        event.preventDefault();
        if (typeof hideInNoteSearchBar === 'function') {
             hideInNoteSearchBar(); // Defined in ui.js
        } else {
             console.error("hideInNoteSearchBar function not found!");
        }
    }
}

/**
 * Performs the search within the noteText content.
 * @param {string} query - The search query string.
 */
function performInNoteSearch(query) {
    if (typeof inNoteSearchActive === 'undefined' || typeof noteText === 'undefined') {
        console.error("performInNoteSearch: Global state 'inNoteSearchActive' or 'noteText' is missing!");
        return;
    }
    if (!inNoteSearchActive || !noteText) {
         // console.warn("performInNoteSearch: Search not active or noteText missing."); // DEBUG: Less noisy
         return;
    }

    inNoteSearchQuery = query; // Update the state variable
    clearInNoteHighlights(); // Remove previous highlights

    if (!query || query.trim().length < 1) { // Minimum 1 char to search
        updateInNoteSearchCount(0, 0);
        return;
    }

    const flags = 'gi'; // Global, case-insensitive
    let regex;
    let escapedQuery = '';
    try {
        // Escape special regex characters in the user's query
        escapedQuery = query.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
        if (!escapedQuery) { // Don't search if query becomes empty after escaping
            updateInNoteSearchCount(0, 0);
            return;
        }
        regex = new RegExp(escapedQuery, flags); // Keep this for potential later use if needed
    } catch (e) {
        console.error("Stickara: Invalid regex from search query:", e);
        updateInNoteSearchCount(0, 0, true); // Indicate error
        return;
    }

    // --- Highlighting using innerHTML replacement ---
    const originalContent = noteText.innerHTML;
    const selection = window.getSelection();
    let savedRange = null;
    // Ensure range is valid and within noteText before saving
    if (selection && selection.rangeCount > 0) {
        const currentRange = selection.getRangeAt(0);
        if (noteText.contains(currentRange.commonAncestorContainer)) {
            savedRange = currentRange.cloneRange();
        }
    }


    try {
        // IMPORTANT: Avoid wrapping existing tags. This regex attempts to match query
        // only if it's not potentially inside an HTML tag definition.
        const markHTML = `<mark class="${IN_NOTE_SEARCH_HIGHLIGHT_CLASS}">$&</mark>`;
        // Enhanced Regex to better avoid matching inside tags/entities
        // (?![^<>]*>) prevents matching inside a closing tag's text content like </scri**pt**>
        // (?![^<]*?>) prevents matching inside an attribute like <div ti**tle**="test">
        // (?![^&;]*;) prevents matching inside an entity like &quo**t**;
        const safeRegex = new RegExp(`(?![^<>]*>)(?![^&;]*;)(${escapedQuery})(?![^<]*?>)`, flags);

        const newContent = originalContent.replace(safeRegex, markHTML);

        if (newContent !== originalContent) {
            noteText.innerHTML = newContent;
        } else {
            // If content didn't change, likely no matches found
             inNoteHighlightElements = []; // Ensure it's empty
        }

        // Collect the created mark elements AFTER updating innerHTML
        inNoteHighlightElements = Array.from(noteText.querySelectorAll(`mark.${IN_NOTE_SEARCH_HIGHLIGHT_CLASS}`));

        // Restore selection if possible
        currentHighlightIndex = -1; // Reset before trying to restore or navigate
        if (savedRange) {
             try {
                 // Check if containers still exist after innerHTML manipulation
                 if (document.body.contains(savedRange.startContainer) && document.body.contains(savedRange.endContainer)) {
                     selection.removeAllRanges();
                     selection.addRange(savedRange);
                 } else {
                      throw new Error("Saved range containers are no longer in the DOM.");
                 }
             } catch (restoreError) {
                 console.warn("Could not restore selection range after search highlight:", restoreError);
                 // If restore fails but we have highlights, navigate to the first one
                 if (inNoteHighlightElements.length > 0) {
                     currentHighlightIndex = 0;
                     navigateToHighlight(0); // Navigate without changing index, just highlight/scroll
                 } else {
                      noteText.focus(); // Fallback focus if no highlights
                 }
             }
        } else if (inNoteHighlightElements.length > 0) {
            // Otherwise, select the first match
            currentHighlightIndex = 0;
            navigateToHighlight(0); // Navigate without changing index
        } else {
             // If no matches, just focus (already handled if selection restore fails)
             noteText.focus();
        }

    } catch (e) {
        console.error("Stickara: Error during innerHTML replacement for highlighting:", e);
        // Attempt to restore original content on error
        try {
             noteText.innerHTML = originalContent;
        } catch (restoreHtmlError) {
             console.error("Failed to restore original innerHTML after highlighting error:", restoreHtmlError);
             // If restore fails, the note content might be corrupted. Maybe clear it?
             // noteText.innerHTML = ''; // Drastic fallback
        }
        inNoteHighlightElements = [];
        currentHighlightIndex = -1;
    }
    // --- End Highlighting ---

    // Update count based on the final state
    updateInNoteSearchCount(currentHighlightIndex >= 0 ? currentHighlightIndex + 1 : 0, inNoteHighlightElements.length);

}

/** Clears all in-note search highlights. */
function clearInNoteHighlights() {
    // Accesses the globally declared inNoteHighlightElements
    if (!noteText || !inNoteHighlightElements || inNoteHighlightElements.length === 0) {
        // console.log("clearInNoteHighlights: No highlights to clear or noteText missing."); // DEBUG: Less noisy
        inNoteHighlightElements = []; // Ensure it's definitely empty
        currentHighlightIndex = -1;
        return;
    }
    // console.log(`clearInNoteHighlights: Clearing ${inNoteHighlightElements.length} highlights.`); // DEBUG: Less noisy

    // Store selection before potentially modifying DOM structure
    const selection = window.getSelection();
    let savedRange = null;
     if (selection && selection.rangeCount > 0) {
         const currentRange = selection.getRangeAt(0);
         if (noteText.contains(currentRange.commonAncestorContainer)) {
             savedRange = currentRange.cloneRange();
         }
     }

    // Use a copy of the array in case the live NodeList changes during iteration
    const marksToRemove = [...inNoteHighlightElements];

    marksToRemove.forEach(mark => {
        if (mark.parentNode) {
            // Replace the mark with its content nodes
            try {
                // Use a DocumentFragment for efficiency when replacing with multiple children
                const fragment = document.createDocumentFragment();
                while (mark.firstChild) {
                    fragment.appendChild(mark.firstChild);
                }
                mark.parentNode.replaceChild(fragment, mark);
            } catch (replaceError) {
                console.warn("Error replacing mark node:", replaceError, mark);
                // If replaceChild fails, try removing the node as a fallback
                try { mark.remove(); } catch (removeError) { console.error("Failed to remove mark after replace error:", removeError);}
            }
        } else {
             // console.warn("Highlight mark had no parentNode during clearing:", mark); // DEBUG: Less noisy
             // It might already be removed if innerHTML was reset elsewhere
        }
    });

    // Normalize adjacent text nodes that might have been created after removing marks
    try {
        noteText.normalize();
    } catch (normalizeError) {
        console.warn("Error normalizing noteText after clearing highlights:", normalizeError);
    }


    // Restore selection if possible
     if (savedRange) {
         try {
             // Check if the container still exists before attempting to restore
             if (document.body.contains(savedRange.startContainer) && document.body.contains(savedRange.endContainer)) {
                  // Additional check: Ensure offsets are still valid
                  if (savedRange.startOffset <= savedRange.startContainer.length && savedRange.endOffset <= savedRange.endContainer.length) {
                       selection.removeAllRanges();
                       selection.addRange(savedRange);
                  } else {
                       console.warn("Selection offsets invalid after clearing highlights, focusing noteText.");
                       noteText.focus();
                  }
             } else {
                 console.warn("Selection container no longer exists after clearing highlights, focusing noteText.");
                 noteText.focus();
             }
         } catch (e) {
             console.warn("Stickara: Could not restore selection after clearing highlights.", e);
             noteText.focus(); // Fallback focus
         }
    } else {
        // If no selection was saved, just focus the note text area
        noteText.focus();
    }

    // Reset state AFTER DOM manipulation and selection restore attempt
    inNoteHighlightElements = [];
    currentHighlightIndex = -1;
    updateInNoteSearchCount(0, 0);
    // console.log("clearInNoteHighlights: Finished clearing."); // DEBUG: Less noisy
}

/**
 * Updates the search count display (e.g., "1 / 5").
 * @param {number} current - The index of the current match (1-based, 0 if none).
 * @param {number} total - The total number of matches found.
 * @param {boolean} [error=false] - Indicates if there was a search error.
 */
function updateInNoteSearchCount(current, total, error = false) {
    const countSpan = document.getElementById(IN_NOTE_SEARCH_COUNT_ID);
    if (countSpan) {
        if (error) {
            countSpan.textContent = 'Error';
            countSpan.style.color = 'red';
        } else if (total === 0 && inNoteSearchQuery) {
            countSpan.textContent = '0 / 0';
            countSpan.style.color = 'orange'; // Indicate "no results found" for a query
        } else if (total === 0 && !inNoteSearchQuery) {
            countSpan.textContent = '0 / 0';
            countSpan.style.color = ''; // Default color if no query
        } else {
            countSpan.textContent = `${current} / ${total}`;
            countSpan.style.color = ''; // Default color
        }
    } else {
        // console.warn("updateInNoteSearchCount: Count span element not found.");
    }
    // Disable/enable buttons based on count
    const prevBtn = document.getElementById(IN_NOTE_SEARCH_PREV_ID);
    const nextBtn = document.getElementById(IN_NOTE_SEARCH_NEXT_ID);
    if (prevBtn) {
        prevBtn.disabled = (total <= 1);
    } else {
         // console.warn("updateInNoteSearchCount: Prev button element not found.");
    }
    if (nextBtn) {
        nextBtn.disabled = (total <= 1);
    } else {
         // console.warn("updateInNoteSearchCount: Next button element not found.");
    }
}

/**
 * Navigates to the previous or next highlight.
 * @param {number} direction - 1 for next, -1 for previous.
 */
function navigateInNoteHighlight(direction) {
    if (!inNoteHighlightElements || inNoteHighlightElements.length === 0) {
         console.log("navigateInNoteHighlight: No highlights to navigate."); // DEBUG
         return;
    }

    const oldIndex = currentHighlightIndex;

    // Remove active class from the current highlight
    if (currentHighlightIndex >= 0 && inNoteHighlightElements[currentHighlightIndex]) {
        // Check if element still exists before removing class
        if(document.body.contains(inNoteHighlightElements[currentHighlightIndex])) {
            inNoteHighlightElements[currentHighlightIndex].classList.remove(IN_NOTE_SEARCH_HIGHLIGHT_ACTIVE_CLASS);
        } else {
            console.warn(`Highlight element at index ${currentHighlightIndex} no longer in DOM before class removal.`);
            // Consider recalculating highlights if this happens often
        }
    }

    // Calculate new index
    currentHighlightIndex += direction;

    // Wrap around
    if (currentHighlightIndex < 0) {
        currentHighlightIndex = inNoteHighlightElements.length - 1;
    } else if (currentHighlightIndex >= inNoteHighlightElements.length) {
        currentHighlightIndex = 0;
    }

    console.log(`Navigating highlight: ${oldIndex + 1} -> ${currentHighlightIndex + 1} (of ${inNoteHighlightElements.length})`); // DEBUG

    navigateToHighlight(currentHighlightIndex); // Use helper to activate/scroll
}

/**
 * Helper function to activate and scroll to a specific highlight index.
 * @param {number} index - The index of the highlight element to navigate to.
 */
function navigateToHighlight(index) {
     if (index < 0 || index >= inNoteHighlightElements.length) {
        console.warn(`navigateToHighlight: Invalid index ${index} requested.`);
        return;
     }

     const currentMark = inNoteHighlightElements[index];
     // Ensure element exists and is still part of the document before interacting
     if (currentMark && document.body.contains(currentMark)) {
         // Add active class and scroll into view
         currentMark.classList.add(IN_NOTE_SEARCH_HIGHLIGHT_ACTIVE_CLASS);
         // Use try-catch for scrollIntoView as it can sometimes fail in complex layouts
         try {
             currentMark.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
         } catch (scrollError) {
             console.warn("Error scrolling to highlight:", scrollError);
         }

         // Update count display
         updateInNoteSearchCount(index + 1, inNoteHighlightElements.length);
     } else {
         console.warn(`Stickara: Current highlight element not found or not in DOM at index ${index}. Attempting to re-sync highlights.`);
         updateInNoteSearchCount(0, inNoteHighlightElements.length); // Show 0 / total?
         currentHighlightIndex = -1; // Reset index
     }
}

console.log("Stickara: In-Note Search Logic Loaded (v2 - Scope Fix, Robustness)");
// --- END OF FILE content-search.js ---