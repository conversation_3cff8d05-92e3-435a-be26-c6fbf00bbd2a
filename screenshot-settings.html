<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Stickara Screenshot Settings</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #4285f4;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .checkbox-group {
            margin-top: 10px;
        }
        .checkbox-label {
            font-weight: normal;
            display: flex;
            align-items: center;
        }
        .checkbox-label input {
            width: auto;
            margin-right: 10px;
        }
        .info-text {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        .buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button.primary {
            background-color: #4285f4;
            color: white;
        }
        button.secondary {
            background-color: #f1f1f1;
            color: #333;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Screenshot Settings</h1>
        
        <div class="form-group">
            <label for="storage-location">Storage Location</label>
            <select id="storage-location">
                <option value="local">Local Only</option>
                <option value="drive">Google Drive Only</option>
                <option value="both">Both Local and Google Drive</option>
            </select>
            <p class="info-text">Choose where your screenshots will be saved.</p>
        </div>
        
        <div id="drive-settings" class="hidden">
            <div class="form-group">
                <label for="drive-folder-name">Google Drive Folder Name</label>
                <input type="text" id="drive-folder-name" value="Stickara Screenshots">
                <p class="info-text">Name of the folder where screenshots will be saved in Google Drive.</p>
            </div>
            
            <div class="form-group checkbox-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="use-app-data-folder" checked>
                    Use private folder (not visible in Drive UI)
                </label>
                <p class="info-text">When enabled, screenshots are saved to a private application folder that isn't visible in the Google Drive interface.</p>
            </div>
            
            <div class="form-group">
                <label for="auto-delete-after">Auto-Delete After (Days)</label>
                <input type="number" id="auto-delete-after" min="0" value="0">
                <p class="info-text">Number of days after which screenshots will be automatically deleted from Google Drive. Use 0 for never.</p>
            </div>
            
            <div class="form-group checkbox-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="ask-before-upload" checked>
                    Ask before uploading to Google Drive
                </label>
                <p class="info-text">When enabled, you'll be asked for confirmation before each screenshot is uploaded to Google Drive.</p>
            </div>
        </div>
        
        <div class="buttons">
            <button id="cancel-btn" class="secondary">Cancel</button>
            <button id="save-btn" class="primary">Save Settings</button>
        </div>
    </div>
    
    <script src="screenshot-settings.js"></script>
</body>
</html>
