/**
 * <PERSON>ara Settings Module
 * Handles user preferences for appearance and behavior
 */

// Storage key for settings
const SETTINGS_KEY = 'Stickara_settings';

// Default settings
const DEFAULT_SETTINGS = {
    // Default note size and position
    defaultNoteSize: {
        width: '340px',
        height: '380px'
    },
    defaultNotePosition: {
        top: '100px',
        right: '24px'
    },

    // Version for future migrations
    version: '1.0'
};

// Current settings (initialized with defaults)
let settings = { ...DEFAULT_SETTINGS };

/**
 * Loads settings from storage
 * @returns {Promise} Promise that resolves when settings are loaded
 */
function loadSettings() {
    return new Promise((resolve) => {
        chrome.storage.local.get([SETTINGS_KEY], (result) => {
            const error = chrome.runtime.lastError;
            if (error) {
                console.error("Stickara: Error loading settings:", error.message);
                // Use defaults if there's an error
                settings = { ...DEFAULT_SETTINGS };
            } else if (result[SETTINGS_KEY]) {
                // Merge with defaults to ensure all properties exist
                settings = {
                    ...DEFAULT_SETTINGS,
                    ...result[SETTINGS_KEY]
                };
            } else {
                // No saved settings, use defaults
                settings = { ...DEFAULT_SETTINGS };
            }

            // Apply settings immediately
            applySettings();

            resolve(settings);
        });
    });
}

/**
 * Saves settings to storage
 * @returns {Promise} Promise that resolves when settings are saved
 */
function saveSettings() {
    return new Promise((resolve, reject) => {
        chrome.storage.local.set({ [SETTINGS_KEY]: settings }, () => {
            const error = chrome.runtime.lastError;
            if (error) {
                console.error("Stickara: Error saving settings:", error.message);
                reject(error);
            } else {
                resolve();
            }
        });
    });
}

/**
 * Applies the current settings to the UI
 */
function applySettings() {
    // Note: Default note size and position will be applied when creating new notes
}

/**
 * Updates a specific setting
 * @param {string} key - The setting key to update
 * @param {any} value - The new value
 * @returns {Promise} Promise that resolves when setting is updated and saved
 */
function updateSetting(key, value) {
    // Update the setting
    if (key.includes('.')) {
        // Handle nested properties like 'defaultNoteSize.width'
        const [parent, child] = key.split('.');
        if (!settings[parent]) settings[parent] = {};
        settings[parent][child] = value;
    } else {
        settings[key] = value;
    }

    // Apply and save
    applySettings();
    return saveSettings();
}



/**
 * Gets the current settings
 * @returns {Object} The current settings object
 */
function getSettings() {
    return { ...settings }; // Return a copy to prevent direct modification
}

/**
 * Gets the default note size based on current settings
 * @returns {Object} Object with width and height properties
 */
function getDefaultNoteSize() {
    return { ...settings.defaultNoteSize };
}

/**
 * Gets the default note position based on current settings
 * @returns {Object} Object with top and right properties
 */
function getDefaultNotePosition() {
    return { ...settings.defaultNotePosition };
}

// Initialize settings on load
document.addEventListener('DOMContentLoaded', loadSettings);

// Export functions for use in other modules
window.StickaraSettings = {
    loadSettings,
    saveSettings,
    updateSetting,
    getSettings,
    getDefaultNoteSize,
    getDefaultNotePosition
};

console.log("Stickara: Settings Module Loaded");
