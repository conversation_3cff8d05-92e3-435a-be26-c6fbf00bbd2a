// --- START OF FILE content-ui.js ---

// Assume necessary variables and other functions are defined...

// --- Highlight Palette Constants ---
const HIGHLIGHT_PALETTE_ID = 'Stickara-highlight-palette';
const HIGHLIGHT_PALETTE_SWATCH_CLASS = 'Stickara-highlight-palette-swatch';
// --- Screenshot Option IDs ---
const SCREENSHOT_OPTION_DOWNLOAD_ID = 'Stickara-screenshot-option-download';
const SCREENSHOT_OPTIONS_CONTAINER_ID = 'Stickara-screenshot-options-container';

// --- Highlight Palette State Variable ---
let isHighlightPaletteOpen = false;
// let currentHighlightStyle = 'color'; // REMOVED: No longer need to track state

// --- Reference for the window keydown listener ---
let windowKeydownListener = null;

/**
 * Creates the toggle button if it doesn't exist.
 */
function createToggleButton() {
    const toggleId = typeof TOGGLE_ID !== 'undefined' ? TOGGLE_ID : 'Stickara-toggle';

    if (document.getElementById(toggleId)) {
        return;
    }

    try {
        toggleButton = document.createElement('button');
        toggleButton.id = toggleId;
        toggleButton.innerText = 'Stickara';
        toggleButton.setAttribute('aria-label', 'Toggle Stickara');

        // Keep original styling - just ensure visibility on ChatGPT

        requestAnimationFrame(() => {
            if (typeof toggleNoteVisibility === 'function') {
                toggleButton.addEventListener('click', toggleNoteVisibility);
            } else {
                console.error("Stickara CRITICAL: toggleNoteVisibility function not found. Button inactive.");
                toggleButton.disabled = true;
                toggleButton.title = "Error initializing toggle button.";
                toggleButton.style.opacity = "0.5";
                toggleButton.style.cursor = "not-allowed";
                toggleButton.style.backgroundColor = "#aaa";
            }
        });

        document.body.appendChild(toggleButton);

    } catch (error) {
        console.error("Stickara: Error creating toggle button:", error);
    }
}

/**
 * Creates the main Stickara container and its core children if they don't exist.
 */
function createNoteUI() {
    // Use constants safely
    const noteId = typeof NOTE_ID !== 'undefined' ? NOTE_ID : 'Stickara-container';
    if (document.getElementById(noteId)) return; // Already exists

    // Check if we're on YouTube and should use iframe isolation
    const isYouTube = window.location.hostname.includes('youtube.com');

    if (isYouTube) {
        console.log("Stickara: YouTube detected, using enhanced keyboard isolation");
        // For now, use standard UI with enhanced keyboard isolation
        // The iframe approach can be enabled later once fully tested
    }

    // Define other constants safely
    const headerId = typeof HEADER_ID !== 'undefined' ? HEADER_ID : 'Stickara-header';
    const titleInputId = typeof NOTE_TITLE_INPUT_ID !== 'undefined' ? NOTE_TITLE_INPUT_ID : 'Stickara-note-title-input';
    const timestampId = typeof TIMESTAMP_ID !== 'undefined' ? TIMESTAMP_ID : 'Stickara-timestamp';
    const noteSwitcherId = typeof NOTE_SWITCHER_ID !== 'undefined' ? NOTE_SWITCHER_ID : 'Stickara-note-switcher-btn';
    const textAreaId = typeof TEXTAREA_ID !== 'undefined' ? TEXTAREA_ID : 'Stickara-text';
    const tagsId = typeof TAGS_ID !== 'undefined' ? TAGS_ID : 'Stickara-tags';
    const reminderId = typeof REMINDER_ID !== 'undefined' ? REMINDER_ID : 'Stickara-reminder';
    const imageInputId = typeof IMAGE_INPUT_ID !== 'undefined' ? IMAGE_INPUT_ID : 'Stickara-image-input';
    const searchBarId = 'Stickara-in-note-search-bar'; // Fixed ID assumed
    const searchInputId = 'Stickara-in-note-search-input'; // Fixed ID assumed

    // Safely get defaults
    const defaultData = (typeof createDefaultNoteData === 'function') ? createDefaultNoteData() : { color: 'yellow', opacity: 1.0, textSize: '14px' };
    const defaultColor = defaultData.color; const defaultOpacity = defaultData.opacity; const defaultFontSize = parseInt(defaultData.textSize) || 14;

    noteContainer = document.createElement('div');
    noteContainer.id = noteId;
    noteContainer.classList.add(`theme-${defaultColor}`);
    noteContainer.setAttribute('role', 'dialog'); noteContainer.setAttribute('aria-modal', 'false'); noteContainer.setAttribute('aria-label', 'Stickara');
    noteContainer.style.opacity = String(defaultOpacity);

    // --- Header ---
    noteHeader = document.createElement('div');
    noteHeader.id = headerId;
    if (typeof startDrag === 'function') noteHeader.addEventListener('mousedown', startDrag); else console.warn("startDrag function missing for header.");

    // Title Input
    noteTitleInput = document.createElement('input');
    noteTitleInput.type = 'text'; noteTitleInput.id = titleInputId;
    const initialNoteIndex = typeof currentNoteIndex !== 'undefined' ? currentNoteIndex : 1;
    noteTitleInput.placeholder = `Note ${initialNoteIndex}`; noteTitleInput.maxLength = 50; noteTitleInput.spellcheck = false; noteTitleInput.setAttribute('aria-label', 'Note Title');
    if(typeof updateNoteSwitcher === 'function' && typeof scheduleSave === 'function') { noteTitleInput.addEventListener('input', () => { updateNoteSwitcher(); scheduleSave(); }); } else { console.warn("updateNoteSwitcher or scheduleSave missing for title input handler.");}
    noteTitleInput.addEventListener('mousedown', (e) => e.stopPropagation()); noteTitleInput.addEventListener('click', (e) => e.stopPropagation());
    noteHeader.appendChild(noteTitleInput);

    // Timestamp
    timestampSpan = document.createElement('span'); timestampSpan.id = timestampId;
    noteHeader.appendChild(timestampSpan);

    // Note Switcher Dropdown
    if (typeof createDropdown === 'function') { noteSwitcher = createDropdown(`Note ${initialNoteIndex}`, [], { icon: '📑', id: noteSwitcherId }); noteHeader.appendChild(noteSwitcher); }
    else { console.error("Stickara Error: createDropdown function not found, cannot create switcher."); }

    // Header Controls Wrapper
    if (typeof createHeaderControls === 'function') { const controlsWrapper = createHeaderControls(); noteHeader.appendChild(controlsWrapper); }
    else { console.error("Stickara Error: createHeaderControls function not found."); }



    noteContainer.appendChild(noteHeader);

    // --- Text Area ---
    noteText = document.createElement('div');
    noteText.id = textAreaId; noteText.contentEditable = true; noteText.setAttribute('aria-labelledby', titleInputId);
    if(typeof handleFormattingState === 'function' && typeof scheduleSave === 'function') { noteText.addEventListener('input', () => { handleFormattingState(); scheduleSave(); }); } else { console.warn("handleFormattingState or scheduleSave missing for noteText input handler."); }
    if (typeof handlePotentialActionClick === 'function') { noteText.addEventListener('click', handlePotentialActionClick); } else { console.warn("handlePotentialActionClick function missing for noteText click."); }

    noteText.style.fontSize = defaultFontSize + 'px';
    noteContainer.appendChild(noteText);

    // ---- Quick Snippets Toolbar ----
    const quickSnippetsToolbar = document.createElement('div');
    quickSnippetsToolbar.className = 'Stickara-quick-snippets-toolbar';
    quickSnippetsToolbar.style.position = 'relative';

    if (typeof createToolButton === 'function') {
        // Date/Time Button (First snippet button)
        if (typeof handleAddDateTimeNote === 'function') {
            const dateTimeButton = createToolButton('📅', '', 'Insert current date/time', handleAddDateTimeNote);
            dateTimeButton.id = 'Stickara-snippet-datetime-btn';
            quickSnippetsToolbar.appendChild(dateTimeButton);
        } else console.warn("handleAddDateTimeNote missing.");

        // Page Info Button (After date/time button)
        if (typeof handleAddPageInfoNote === 'function') {
            const pageInfoButton = createToolButton('📄', '', 'Insert page title and heading', handleAddPageInfoNote);
            pageInfoButton.id = 'Stickara-snippet-pageinfo-btn';
            quickSnippetsToolbar.appendChild(pageInfoButton);
        } else console.warn("handleAddPageInfoNote missing.");

       // --- START: Restore Single Highlight Button ---
       // Highlight Button (Calls toggleHighlightPalette)
        if (typeof toggleHighlightPalette === 'function' && typeof applyInNoteHighlight === 'function') {
            const highlightButton = createToolButton('🔆', '', 'Highlight selection in note', toggleHighlightPalette);
            highlightButton.id = 'Stickara-snippet-highlight-btn';
            quickSnippetsToolbar.appendChild(highlightButton);
        } else console.warn("Highlight snippet button dependencies missing.");
       // --- END: Restore Single Highlight Button ---

       // Highlight with Note Button removed as requested

        // Timestamp Button with enhanced formatting
        if (typeof handleAddTimestampNote === 'function') {
            const timestampButton = createToolButton('⏰', '', 'Add Video Timestamp', handleAddTimestampNote);
            timestampButton.id = 'Stickara-snippet-timestamp-btn';
            quickSnippetsToolbar.appendChild(timestampButton);
        } else console.warn("handleAddTimestampNote missing.");

        // Spacebar snippet button removed as requested by user
    } else {
        console.error("Stickara CRITICAL Error: createToolButton missing.");
        quickSnippetsToolbar.innerHTML = '<span style="color:red;">[Err]</span>';
    }
    noteContainer.appendChild(quickSnippetsToolbar);
    // ---- End Quick Snippets Toolbar ----

    // --- Input Fields (Tags, Reminder) in a scrollable container ---
    // Create metadata container for scrollable tags and reminder
    const metadataContainer = document.createElement('div');
    metadataContainer.id = 'Stickara-metadata-container';
    metadataContainer.title = 'Scroll to see more fields';

    // Create a wrapper for tags with a label
    const tagsWrapper = document.createElement('div');
    tagsWrapper.className = 'Stickara-input-wrapper';

    // Add a small icon/label
    const tagsLabel = document.createElement('span');
    tagsLabel.className = 'Stickara-input-label';
    tagsLabel.textContent = '🏷️';
    tagsLabel.title = 'Tags';
    tagsWrapper.appendChild(tagsLabel);

    // Create the tags input
    tagsInput = document.createElement('input');
    tagsInput.type = 'text'; tagsInput.placeholder = 'work, important, todo...'; tagsInput.id = tagsId;
    if (typeof scheduleSave === 'function') tagsInput.addEventListener('input', scheduleSave); else console.warn("scheduleSave missing for tagsInput.");
    tagsInput.addEventListener('click', (e) => e.stopPropagation());
    tagsWrapper.appendChild(tagsInput);

    // Create compact notebook selector (inline with tags)
    const notebookSelector = document.createElement('div');
    notebookSelector.id = 'Stickara-notebook-selector';
    notebookSelector.className = 'Stickara-notebook-selector';

    // Notebook toggle button with current selection display
    const notebookToggle = document.createElement('button');
    notebookToggle.id = 'Stickara-notebook-toggle';
    notebookToggle.className = 'Stickara-notebook-toggle';
    notebookToggle.title = 'Select notebook for this note';
    notebookToggle.innerHTML = '<span class="Stickara-notebook-icon">📚</span><span class="Stickara-notebook-current">Ungrouped</span><span class="Stickara-notebook-arrow">▲</span>';
    notebookToggle.addEventListener('click', (e) => {
        e.stopPropagation();
        if (typeof toggleNotebookDropdown === 'function') {
            toggleNotebookDropdown();
        }
    });
    notebookToggle.addEventListener('mousedown', (e) => e.stopPropagation());
    notebookSelector.appendChild(notebookToggle);

    // Notebook dropdown list (hidden by default)
    const notebookDropdown = document.createElement('div');
    notebookDropdown.id = 'Stickara-notebook-dropdown';
    notebookDropdown.className = 'Stickara-notebook-dropdown';
    notebookDropdown.style.display = 'none';
    notebookSelector.appendChild(notebookDropdown);

    tagsWrapper.appendChild(notebookSelector);
    metadataContainer.appendChild(tagsWrapper);

    // Create a wrapper for reminder with a label
    const reminderWrapper = document.createElement('div');
    reminderWrapper.className = 'Stickara-input-wrapper';

    // Add a small icon/label
    const reminderLabel = document.createElement('span');
    reminderLabel.className = 'Stickara-input-label';
    reminderLabel.textContent = '⏰';
    reminderLabel.title = 'Reminder';
    reminderWrapper.appendChild(reminderLabel);

    // Create the reminder input
    reminderInput = document.createElement('input');
    reminderInput.type = 'datetime-local'; reminderInput.id = reminderId; reminderInput.title = 'Set reminder date/time';
    if (typeof scheduleSave === 'function') reminderInput.addEventListener('change', scheduleSave); else console.warn("scheduleSave missing for reminderInput.");
    reminderInput.addEventListener('click', (e) => e.stopPropagation());
    reminderWrapper.appendChild(reminderInput);

    // Add Google Calendar checkbox
    const calendarCheckboxWrapper = document.createElement('div');
    calendarCheckboxWrapper.className = 'Stickara-calendar-option';
    calendarCheckboxWrapper.addEventListener('click', (e) => {
        e.stopPropagation();
        e.preventDefault();

        // Only toggle if the click is directly on the wrapper or label, not on the checkbox itself
        if (e.target === calendarCheckboxWrapper || e.target === calendarLabel) {
            // Get the checkbox
            const checkbox = document.getElementById('Stickara-calendar-checkbox');
            if (checkbox) {
                // Toggle the checkbox
                checkbox.checked = !checkbox.checked;

                // Trigger the change event
                const changeEvent = new Event('change', { bubbles: true });
                checkbox.dispatchEvent(changeEvent);

                console.log("Calendar checkbox toggled to:", checkbox.checked);
            }
        }
    });
    calendarCheckboxWrapper.addEventListener('mousedown', (e) => e.stopPropagation());

    const calendarCheckbox = document.createElement('input');
    calendarCheckbox.type = 'checkbox';
    calendarCheckbox.id = 'Stickara-calendar-checkbox';
    calendarCheckbox.title = 'Add to Google Calendar';
    calendarCheckbox.addEventListener('click', (e) => {
        e.stopPropagation();
        // Don't prevent default here to allow normal checkbox behavior
        console.log("Direct checkbox click detected");

        // Ensure the change event is triggered
        setTimeout(() => {
            if (typeof scheduleSave === 'function') {
                scheduleSave();
                console.log("Scheduled save after direct checkbox click");
            }
        }, 0);
    });
    calendarCheckbox.addEventListener('mousedown', (e) => e.stopPropagation());
    if (typeof scheduleSave === 'function') calendarCheckbox.addEventListener('change', scheduleSave); else console.warn("scheduleSave missing for calendar checkbox.");

    const calendarLabel = document.createElement('label');
    calendarLabel.htmlFor = 'Stickara-calendar-checkbox';
    calendarLabel.textContent = 'Add to Calendar';
    calendarLabel.title = 'Add this reminder to Google Calendar';
    calendarLabel.addEventListener('click', (e) => {
        e.stopPropagation();
        // Don't prevent default to allow label to toggle checkbox
    });
    calendarLabel.addEventListener('mousedown', (e) => e.stopPropagation());

    calendarCheckboxWrapper.appendChild(calendarCheckbox);
    calendarCheckboxWrapper.appendChild(calendarLabel);
    reminderWrapper.appendChild(calendarCheckboxWrapper);

    metadataContainer.appendChild(reminderWrapper);

    // Add the metadata container to the note
    noteContainer.appendChild(metadataContainer);

    // No scroll hint animation to keep scrolling subtle

    // --- Hidden Image File Input ---
    const fileInput = document.createElement('input');
    fileInput.type = 'file'; fileInput.accept = 'image/*'; fileInput.style.display = 'none'; fileInput.id = imageInputId;
    if (typeof handleImageUpload === 'function') { fileInput.addEventListener('change', handleImageUpload); } else { console.warn("handleImageUpload missing."); }
    noteContainer.appendChild(fileInput);

    // --- In-Note Search Bar ---
    const searchBar = document.createElement('div');
    searchBar.id = searchBarId; searchBar.style.display = 'none';
    const searchInput = document.createElement('input');
    searchInput.type = 'text'; searchInput.id = searchInputId; searchInput.placeholder = 'Search in note...';
    if (typeof handleInNoteSearchInput === 'function') searchInput.addEventListener('input', handleInNoteSearchInput); else console.warn("handleInNoteSearchInput missing.");
    if (typeof handleInNoteSearchKeydown === 'function') searchInput.addEventListener('keydown', handleInNoteSearchKeydown); else console.warn("handleInNoteSearchKeydown missing.");
    searchBar.appendChild(searchInput);
    // Search Nav Buttons
    if (typeof createToolButton === 'function' && typeof navigateInNoteHighlight === 'function') {
        const searchPrevId = typeof IN_NOTE_SEARCH_PREV_ID !== 'undefined' ? IN_NOTE_SEARCH_PREV_ID : 'Stickara-in-note-search-prev';
        const searchNextId = typeof IN_NOTE_SEARCH_NEXT_ID !== 'undefined' ? IN_NOTE_SEARCH_NEXT_ID : 'Stickara-in-note-search-next';
        const searchPrevBtn = createToolButton('↑', '', 'Previous Match', () => navigateInNoteHighlight(-1), searchPrevId);
        const searchNextBtn = createToolButton('↓', '', 'Next Match', () => navigateInNoteHighlight(1), searchNextId);
        searchBar.appendChild(searchPrevBtn); searchBar.appendChild(searchNextBtn);
    } else { console.warn("Search nav button dependencies missing."); }
    // Search Count
    const searchCountId = typeof IN_NOTE_SEARCH_COUNT_ID !== 'undefined' ? IN_NOTE_SEARCH_COUNT_ID : 'Stickara-in-note-search-count';
    const searchCountSpan = document.createElement('span'); searchCountSpan.id = searchCountId; searchCountSpan.textContent = '0 / 0';
    searchBar.appendChild(searchCountSpan);
    // Search Close Button
    if (typeof createToolButton === 'function' && typeof hideInNoteSearchBar === 'function') {
        const searchCloseBtn = createToolButton('×', '', 'Close Search', hideInNoteSearchBar);
        searchBar.appendChild(searchCloseBtn);
    } else { console.warn("Search close button dependencies missing."); }
    noteContainer.appendChild(searchBar);
    // --- End In-Note Search Bar ---

    // --- Append note container to body LAST ---
    document.body.appendChild(noteContainer);

    // --- Attach global listeners (mousemove, mouseup, click for dropdowns) ---
    if (typeof drag === 'function') document.addEventListener('mousemove', drag); else console.warn("Global listener 'drag' missing.");
    if (typeof stopDrag === 'function') document.addEventListener('mouseup', stopDrag); else console.warn("Global listener 'stopDrag' missing.");
    if (typeof closeAllDropdowns === 'function') document.addEventListener('click', closeAllDropdowns); else console.warn("Global listener 'closeAllDropdowns' missing.");
    if (typeof handleFormattingState === 'function') document.addEventListener('selectionchange', handleFormattingState); else console.warn("Global listener 'handleFormattingState' missing.");
    // Note: handleFindShortcut should be attached globally if needed for Esc etc. outside noteText
    if (typeof handleFindShortcut === 'function') document.addEventListener('keydown', handleFindShortcut); else console.warn("Global listener 'handleFindShortcut' missing.");

    // --- Enhanced keyboard isolation for YouTube ---
    // Note: This will be set up after UI elements are created

    // --- Create Resize Handles ---
    const handles = ['top', 'right', 'bottom', 'left', 'top-left', 'top-right', 'bottom-left', 'bottom-right'];
    handles.forEach(handleType => {
        const handle = document.createElement('div');
        handle.classList.add('Stickara-resizer', `Stickara-resizer-${handleType}`);
        handle.dataset.direction = handleType;
        // Add mousedown listener in interactions.js later
        noteContainer.appendChild(handle);
    });
    // -----------------------------

    // Initialize ResizeObserver
    if (typeof initResizeObserver === 'function') initResizeObserver(); else console.warn("initResizeObserver function missing.");
    // Calculate initial height
    if (typeof calculateTextAreaHeight === 'function') calculateTextAreaHeight(); else console.warn("calculateTextAreaHeight function missing.");

    // Initialize notebook sidebar
    if (typeof initializeNotebookSidebar === 'function') {
        // Delay initialization to ensure all dependencies are loaded
        setTimeout(() => {
            initializeNotebookSidebar();
        }, 100);
    } else {
        console.warn("initializeNotebookSidebar function missing.");
    }

    // --- Set up enhanced keyboard isolation for YouTube after all elements are created ---
    if (isYouTube) {
        // Delay setup to ensure all elements are properly initialized
        setTimeout(() => {
            setupYouTubeKeyboardIsolation();
        }, 100);
    }

    console.log("Stickara: Standard note UI created" + (isYouTube ? " with YouTube keyboard isolation" : ""));
} // End createNoteUI

/**
 * Creates an iframe-isolated note UI for YouTube and other sites with keyboard conflicts
 */
function createIframeNoteUI() {
    const noteId = typeof NOTE_ID !== 'undefined' ? NOTE_ID : 'Stickara-container';

    // Create the main container that will hold the iframe
    noteContainer = document.createElement('div');
    noteContainer.id = noteId;
    noteContainer.classList.add('Stickara-iframe-container');
    noteContainer.setAttribute('role', 'dialog');
    noteContainer.setAttribute('aria-modal', 'false');
    noteContainer.setAttribute('aria-label', 'Stickara');

    // Create the iframe that will contain the actual note UI
    const noteIframe = document.createElement('iframe');
    noteIframe.id = 'Stickara-note-iframe';
    // Use more restrictive sandbox for security - remove allow-same-origin
    noteIframe.setAttribute('sandbox', 'allow-scripts allow-forms');
    noteIframe.style.width = '100%';
    noteIframe.style.height = '100%';
    noteIframe.style.border = 'none';
    noteIframe.style.background = 'transparent';
    noteIframe.style.pointerEvents = 'auto';

    // Add the iframe to the container
    noteContainer.appendChild(noteIframe);

    // Add the container to the page
    document.body.appendChild(noteContainer);

    // Initialize the iframe content
    initializeIframeContent(noteIframe);

    console.log("Stickara: Iframe-isolated note UI created for YouTube");
}

/**
 * Initializes the content inside the note iframe
 */
function initializeIframeContent(iframe) {
    iframe.addEventListener('load', () => {
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            const iframeWindow = iframe.contentWindow;

            // Create the HTML structure inside the iframe
            iframeDoc.open();
            iframeDoc.write(createIframeHTML());
            iframeDoc.close();

            // Wait for DOM to be ready, then add event listeners
            setTimeout(() => {
                setupIframeEventListeners(iframeDoc, iframeWindow);
                setupIframeCommunication(iframe, iframeWindow);
                initializeIframeNoteUI(iframeWindow, iframeDoc);
            }, 100);

        } catch (error) {
            console.error("Stickara: Error initializing iframe content:", error);
        }
    });

    // Set the iframe source to trigger the load event
    iframe.src = 'about:blank';
}

/**
 * Sets up event listeners inside the iframe (CSP-compliant)
 */
function setupIframeEventListeners(iframeDoc, iframeWindow) {
    try {
        // Add keyboard event listeners
        iframeDoc.addEventListener('keydown', function(e) {
            // All keyboard events are naturally isolated in this iframe
            // This is the key benefit - no special handling needed!
        });

        // Set up communication with parent
        iframeWindow.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'stickara-command') {
                handleIframeCommand(event.data, iframeDoc);
            }
        });

        // Set up input event listeners for saving
        const titleInput = iframeDoc.getElementById('iframe-title');
        const contentTextarea = iframeDoc.getElementById('iframe-content');

        if (titleInput) {
            titleInput.addEventListener('input', function() {
                notifyParentOfChange(iframeWindow, titleInput.value, contentTextarea ? contentTextarea.value : '');
            });
        }

        if (contentTextarea) {
            contentTextarea.addEventListener('input', function() {
                notifyParentOfChange(iframeWindow, titleInput ? titleInput.value : '', contentTextarea.value);
            });

            // Focus the content area by default
            setTimeout(() => {
                contentTextarea.focus();
            }, 50);
        }

        // Notify parent that iframe is ready
        iframeWindow.parent.postMessage({
            type: 'stickara-iframe-ready'
        }, '*');

    } catch (error) {
        console.error("Stickara: Error setting up iframe event listeners:", error);
    }
}

/**
 * Handles commands sent from parent to iframe
 */
function handleIframeCommand(data, iframeDoc) {
    try {
        switch(data.command) {
            case 'focus':
                const textArea = iframeDoc.getElementById('iframe-content') ||
                               iframeDoc.querySelector('textarea') ||
                               iframeDoc.querySelector('input[type="text"]');
                if (textArea) {
                    textArea.focus();
                    // Place cursor at end for text inputs
                    if (textArea.setSelectionRange) {
                        const len = textArea.value.length;
                        textArea.setSelectionRange(len, len);
                    }
                }
                break;

            case 'update-content':
                if (data.title !== undefined) {
                    const titleInput = iframeDoc.getElementById('iframe-title');
                    if (titleInput) titleInput.value = data.title;
                }
                if (data.content !== undefined) {
                    const contentTextarea = iframeDoc.getElementById('iframe-content');
                    if (contentTextarea) contentTextarea.value = data.content;
                }
                break;
        }
    } catch (error) {
        console.error("Stickara: Error handling iframe command:", error);
    }
}

/**
 * Notifies parent window of content changes
 */
function notifyParentOfChange(iframeWindow, title, content) {
    try {
        iframeWindow.parent.postMessage({
            type: 'stickara-note-data',
            data: {
                title: title,
                content: content
            }
        }, '*');
    } catch (error) {
        console.error("Stickara: Error notifying parent of change:", error);
    }
}



/**
 * Sets up communication between the iframe and parent window
 */
function setupIframeCommunication(iframe, iframeWindow) {
    // Store iframe reference for later use
    window.stickaraIframe = iframe;
    window.stickaraIframeWindow = iframeWindow;

    // Listen for messages from the iframe
    window.addEventListener('message', function(event) {
        if (event.source !== iframeWindow) return;

        if (event.data && event.data.type === 'stickara-iframe-ready') {
            console.log("Stickara: Iframe is ready, initializing note UI");
            // Iframe is ready, we can now initialize the note UI inside it

        } else if (event.data && event.data.type === 'stickara-note-data') {
            // Handle note data changes from iframe
            try {
                // Update the global note data
                if (event.data.data && typeof notes !== 'undefined' && notes && typeof currentNoteIndex !== 'undefined') {
                    // Ensure the notes array has an entry for the current index
                    if (!notes[currentNoteIndex]) {
                        notes[currentNoteIndex] = typeof createDefaultNoteData === 'function' ?
                            createDefaultNoteData() :
                            { title: '', content: '', tags: '', reminder: '', timestamp: new Date().toISOString() };
                    }

                    if (event.data.data.title !== undefined) {
                        notes[currentNoteIndex].title = event.data.data.title;
                    }
                    if (event.data.data.content !== undefined) {
                        notes[currentNoteIndex].content = event.data.data.content;
                    }

                    // Schedule save
                    if (typeof scheduleSave === 'function') {
                        scheduleSave();
                    }
                }
            } catch (error) {
                console.error("Stickara: Error handling note data from iframe:", error);
            }

        } else if (event.data && event.data.type === 'stickara-resize') {
            // Handle iframe resize requests
            const newHeight = event.data.height;
            if (newHeight && iframe) {
                iframe.style.height = newHeight + 'px';
            }
        }
    });

    // Send initial configuration to iframe
    try {
        iframeWindow.postMessage({
            type: 'stickara-command',
            command: 'init',
            config: {}
        }, '*');
    } catch (error) {
        console.error("Stickara: Error sending initial config to iframe:", error);
    }
}

/**
 * Initializes the note UI components inside the iframe
 */
function initializeIframeNoteUI(iframeWindow, iframeDoc) {
    try {
        // Set up global references for compatibility with existing code
        setupIframeGlobalReferences(iframeDoc);

        // Load existing note data if available
        loadIframeNoteData(iframeDoc);

        console.log("Stickara: Iframe note UI initialized successfully");

    } catch (error) {
        console.error("Stickara: Error initializing iframe note UI:", error);
    }
}

/**
 * Sets up global references for iframe elements (for compatibility)
 */
function setupIframeGlobalReferences(iframeDoc) {
    // Set up global references that other parts of the code expect
    // Point to iframe elements instead of parent page elements
    noteText = iframeDoc.getElementById('iframe-content');
    noteTitleInput = iframeDoc.getElementById('iframe-title');
    noteHeader = iframeDoc.querySelector('.note-header');

    // Note: noteContainer still refers to the outer container in the parent page
    // This maintains the positioning and dragging functionality

    console.log("Stickara: Global references set up for iframe elements");
}

/**
 * Loads existing note data into the iframe
 */
function loadIframeNoteData(iframeDoc) {
    try {
        // Get current note data from the parent page
        if (typeof notes !== 'undefined' && notes && notes[currentNoteIndex]) {
            const currentNote = notes[currentNoteIndex];

            // Update iframe content
            const titleInput = iframeDoc.getElementById('iframe-title');
            const contentTextarea = iframeDoc.getElementById('iframe-content');

            if (titleInput && currentNote.title) {
                titleInput.value = currentNote.title;
            }

            if (contentTextarea && currentNote.content) {
                // Convert HTML content to plain text for textarea
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = currentNote.content;
                contentTextarea.value = tempDiv.textContent || tempDiv.innerText || '';
            }

            console.log("Stickara: Loaded existing note data into iframe");
        }
    } catch (error) {
        console.error("Stickara: Error loading note data into iframe:", error);
    }
}

/**
 * Sets up enhanced keyboard isolation for YouTube
 */
function setupYouTubeKeyboardIsolation() {
    console.log("Stickara: Setting up enhanced YouTube keyboard isolation");

    // Check if required elements exist
    if (!noteContainer) {
        console.warn("Stickara: Cannot set up keyboard isolation - noteContainer not found");
        return;
    }

    // Double-check we're on YouTube
    if (!window.location.hostname.includes('youtube.com')) {
        console.warn("Stickara: Not on YouTube, skipping keyboard isolation setup");
        return;
    }

    // Create a high-priority event listener that captures all keyboard events
    // when the note is focused and prevents them from reaching YouTube
    const keyboardIsolationHandler = (event) => {
        // Only handle events when note container is visible
        if (!noteContainer || !noteContainer.classList.contains('visible')) {
            return;
        }

        // Check if the event target is within our note container
        const isWithinNote = noteContainer.contains(event.target) ||
                           event.target === noteContainer ||
                           (noteText && (event.target === noteText || noteText.contains(event.target))) ||
                           (noteTitleInput && (event.target === noteTitleInput || noteTitleInput.contains(event.target)));

        if (!isWithinNote) {
            return;
        }

        // For any key event within our note, stop it from reaching YouTube
        event.stopImmediatePropagation();

        // Special handling for spacebar - the main conflict
        if (event.key === ' ') {
            console.log("Stickara: Isolated spacebar event from YouTube");
        }

        // Let the event continue its normal behavior within our note
        // but prevent it from bubbling to YouTube's handlers
    };

    // Attach the handler at the document level with capture=true
    // This ensures we catch events before YouTube's handlers
    document.addEventListener('keydown', keyboardIsolationHandler, true);
    document.addEventListener('keyup', keyboardIsolationHandler, true);
    document.addEventListener('keypress', keyboardIsolationHandler, true);

    // Store reference for potential cleanup
    window.stickaraKeyboardIsolationHandler = keyboardIsolationHandler;

    // Also add focus management to ensure our note gets proper focus
    if (noteText) {
        noteText.addEventListener('focus', () => {
            console.log("Stickara: Note text focused - keyboard isolation active");
        });

        noteText.addEventListener('blur', () => {
            console.log("Stickara: Note text blurred - keyboard isolation inactive");
        });
    } else {
        console.warn("Stickara: noteText element not found for focus management");
    }

    if (noteTitleInput) {
        noteTitleInput.addEventListener('focus', () => {
            console.log("Stickara: Note title focused - keyboard isolation active");
        });

        noteTitleInput.addEventListener('blur', () => {
            console.log("Stickara: Note title blurred - keyboard isolation inactive");
        });
    } else {
        console.warn("Stickara: noteTitleInput element not found for focus management");
    }

    console.log("Stickara: Enhanced YouTube keyboard isolation setup complete");
}


/**
 * Shows the in-note search bar.
 */
function showInNoteSearchBar() {
    const searchBar = document.getElementById('Stickara-in-note-search-bar');
    const searchInput = document.getElementById('Stickara-in-note-search-input');
    if (typeof noteContainer !== 'undefined' && typeof inNoteSearchActive !== 'undefined' && searchBar && searchInput && noteContainer && noteContainer.classList.contains('visible')) {
        // Minimize check removed
        searchBar.style.display = 'flex';
        inNoteSearchActive = true;
        searchInput.value = ''; searchInput.focus();
        if (typeof clearInNoteHighlights === 'function') clearInNoteHighlights(); else console.warn("clearInNoteHighlights function missing.");
        if (typeof calculateTextAreaHeight === 'function') calculateTextAreaHeight(); else console.warn("calculateTextAreaHeight function missing.");
    } else { console.warn("Cannot show search bar - prerequisites missing or note not visible."); }
}

/**
 * Hides the in-note search bar and clears highlights.
 */
function hideInNoteSearchBar() {
    const searchBar = document.getElementById('Stickara-in-note-search-bar');
    if (searchBar) {
        searchBar.style.display = 'none';
        if(typeof inNoteSearchActive !== 'undefined') inNoteSearchActive = false;
        if (typeof clearInNoteHighlights === 'function') clearInNoteHighlights(); else console.warn("clearInNoteHighlights function missing.");
        if(typeof noteText !== 'undefined' && noteText) noteText.focus();
        if (typeof calculateTextAreaHeight === 'function') calculateTextAreaHeight(); else console.warn("calculateTextAreaHeight function missing.");
    }
}

/**
 * Creates the flashcard study modal overlay and its contents if they don't exist.
 */
function createFlashcardModal() {
    const modalId = typeof FLASHCARD_MODAL_ID !== 'undefined' ? FLASHCARD_MODAL_ID : 'Stickara-flashcard-overlay';
    if (document.getElementById(modalId)) return;

    const requiredFuncs = ['toggleFlashcardAnswer', 'handleFlashcardContentKeyPress', 'prevFlashcard', 'nextFlashcard', 'closeFlashcardModal', 'handleFlashcardKeyPress'];
    const missingFuncs = requiredFuncs.filter(funcName => typeof window[funcName] !== 'function');
    if (missingFuncs.length > 0) { console.error(`Stickara CRITICAL: Cannot create flashcard modal. Missing functions: ${missingFuncs.join(', ')}`); return; }

    flashcardModalOverlay = document.createElement('div');
    flashcardModalOverlay.id = modalId; flashcardModalOverlay.setAttribute('role', 'dialog'); flashcardModalOverlay.setAttribute('aria-modal', 'true');
    flashcardModalOverlay.setAttribute('aria-labelledby', 'Stickara-flashcard-title'); flashcardModalOverlay.style.display = 'none';

    const modal = document.createElement('div'); modal.id = 'Stickara-flashcard-modal';
    const modalTitle = document.createElement('h4'); modalTitle.id = 'Stickara-flashcard-title'; modalTitle.textContent = 'Study Flashcards';
    modal.appendChild(modalTitle);

    const contentId = typeof FLASHCARD_CONTENT_ID !== 'undefined' ? FLASHCARD_CONTENT_ID : 'Stickara-flashcard-content';
    flashcardContent = document.createElement('div'); flashcardContent.id = contentId; flashcardContent.textContent = 'Loading...'; flashcardContent.setAttribute('role', 'button');
    flashcardContent.setAttribute('tabindex', '0'); flashcardContent.setAttribute('aria-label', 'Flashcard Content');
    flashcardContent.addEventListener('click', toggleFlashcardAnswer); flashcardContent.addEventListener('keydown', handleFlashcardContentKeyPress);
    modal.appendChild(flashcardContent);

    const controls = document.createElement('div'); controls.id = 'Stickara-flashcard-controls';
    const nav = document.createElement('div'); nav.id = 'Stickara-flashcard-nav';

    const prevId = typeof FLASHCARD_PREV_ID !== 'undefined' ? FLASHCARD_PREV_ID : 'Stickara-flashcard-prev';
    flashcardPrevBtn = document.createElement('button'); flashcardPrevBtn.id = prevId; flashcardPrevBtn.textContent = '← Prev';
    flashcardPrevBtn.setAttribute('aria-label', 'Previous flashcard'); flashcardPrevBtn.addEventListener('click', prevFlashcard);
    nav.appendChild(flashcardPrevBtn);

    const nextId = typeof FLASHCARD_NEXT_ID !== 'undefined' ? FLASHCARD_NEXT_ID : 'Stickara-flashcard-next';
    flashcardNextBtn = document.createElement('button'); flashcardNextBtn.id = nextId; flashcardNextBtn.textContent = 'Next →';
    flashcardNextBtn.setAttribute('aria-label', 'Next flashcard'); flashcardNextBtn.addEventListener('click', nextFlashcard);
    nav.appendChild(flashcardNextBtn);
    controls.appendChild(nav);

    const counterId = typeof FLASHCARD_COUNTER_ID !== 'undefined' ? FLASHCARD_COUNTER_ID : 'Stickara-flashcard-counter';
    flashcardCounter = document.createElement('span'); flashcardCounter.id = counterId; flashcardCounter.textContent = 'Card 0 / 0'; flashcardCounter.setAttribute('aria-live', 'polite');
    controls.appendChild(flashcardCounter);
    modal.appendChild(controls);

    const closeId = typeof FLASHCARD_CLOSE_ID !== 'undefined' ? FLASHCARD_CLOSE_ID : 'Stickara-flashcard-close-btn';
    const closeBtn = document.createElement('button'); closeBtn.id = closeId; closeBtn.innerHTML = '×';
    closeBtn.title = 'Close Study Mode (Esc)'; closeBtn.setAttribute('aria-label', 'Close Study Mode');
    closeBtn.addEventListener('click', closeFlashcardModal);
    modal.appendChild(closeBtn);

    flashcardModalOverlay.appendChild(modal);
    document.body.appendChild(flashcardModalOverlay);

    flashcardModalOverlay.addEventListener('keydown', handleFlashcardKeyPress);
    flashcardModalOverlay.addEventListener('click', (e) => { if (e.target === flashcardModalOverlay) closeFlashcardModal(); });
    console.log("Stickara: Flashcard Modal UI created.");
}


// --- START: Restore Highlight Palette UI Functions with Modified Handlers ---

/** Creates the small highlight palette with Style and Color options. */
function createHighlightColorPalette() { // Rename maybe? createHighlightStylePalette?
    // --- Validation & Setup ---
    // Ensure noteText, applyInNoteHighlight, and handleHighlightSelection are available
    if (typeof noteText === 'undefined' || noteText === null) {
        console.warn("Warning: noteText not available for highlight palette. Trying to find it...");
        // Try to find noteText element
        noteText = document.querySelector('#stickara-note-text') ||
                  document.querySelector('[contenteditable="true"]') ||
                  document.querySelector('.stickara-note-text');
        if (!noteText) {
            console.error("Error: Could not find noteText element for highlight palette.");
            return null;
        }
    }

    if (typeof applyInNoteHighlight !== 'function') {
        console.error("Error: applyInNoteHighlight function not available for highlight palette.");
        return null;
    }

    if (typeof handleHighlightSelection !== 'function') {
        console.error("Error: handleHighlightSelection function not available for highlight palette.");
        return null;
    }
    if (typeof HIGHLIGHT_COLORS === 'undefined' || typeof HIGHLIGHT_PALETTE_ID === 'undefined' || typeof HIGHLIGHT_PALETTE_SWATCH_CLASS === 'undefined' || typeof DEFAULT_HIGHLIGHT_COLOR === 'undefined') {
        console.error("Error: Required constants for highlight palette missing."); return null;
    }
    const isHighlightColorsValid = typeof HIGHLIGHT_COLORS === 'object' && HIGHLIGHT_COLORS !== null;
    const colors = isHighlightColorsValid ? Object.keys(HIGHLIGHT_COLORS) : [DEFAULT_HIGHLIGHT_COLOR];
    const defaultBg = isHighlightColorsValid ? (HIGHLIGHT_COLORS[DEFAULT_HIGHLIGHT_COLOR] || '#fbf09d') : '#fbf09d';

    const styles = {
        underline: { icon: 'U', title: 'Underline' },
        wavy: { icon: '~', title: 'Wavy Underline' },
        'border-thick': { icon: '□', title: 'Thick Border' },
        'strikethrough': { icon: 'S̶', title: 'Strikethrough' },
        'blur': { icon: '🕶️', title: 'Blur (Privacy)' }
    };

    // Add snippet options
    const snippets = {
        'review-pro': { icon: '👍', title: 'Pro' },
        'review-con': { icon: '👎', title: 'Con' },
        'spec': { icon: '📋', title: 'Spec' }
    };

    // --- Create Palette Container ---
    const palette = document.createElement('div');
    palette.id = HIGHLIGHT_PALETTE_ID; palette.classList.add('Stickara-highlight-palette');
    palette.style.flexDirection = 'column';

    // --- Create Style Selector Container ---
    const styleSelector = document.createElement('div');
    styleSelector.className = 'Stickara-highlight-style-selector';

    Object.entries(styles).forEach(([styleName, styleInfo]) => {
        const styleButton = document.createElement('button');
        styleButton.className = 'Stickara-highlight-style-btn';
        styleButton.dataset.style = styleName;
        styleButton.innerHTML = `<span class="Stickara-icon">${styleInfo.icon}</span>`;
        styleButton.title = styleInfo.title;
        styleButton.setAttribute('aria-label', styleInfo.title);

        // --- MODIFIED Style Button Click Handler (Handles both Note & Page) ---
        styleButton.addEventListener('click', (e) => {
            e.stopPropagation();
            const selectedStyle = styleButton.dataset.style;
            const selection = window.getSelection();

            if (!selection || selection.rangeCount === 0 || selection.isCollapsed) {
                console.log("Stickara Palette: No selection to highlight.");
                if (typeof closeHighlightPalette === 'function') closeHighlightPalette();
                return;
            }

            // Check if selection is inside the note editor
            let isInsideNote = false;
            const range = selection.getRangeAt(0);
            const container = range.commonAncestorContainer;
            if (container && noteText) { // Ensure noteText exists
                 isInsideNote = noteText.contains(container.nodeType === Node.ELEMENT_NODE ? container : container.parentElement);
            }

            // Call the appropriate function
            if (isInsideNote) {
                console.log(`Stickara Palette: Applying IN NOTE style: ${selectedStyle}`);
                applyInNoteHighlight(null, selectedStyle); // (from content-interactions.js)
            } else {
                 console.log(`Stickara Palette: Applying ON PAGE style: ${selectedStyle}`);
                 handleHighlightSelection(null, selectedStyle); // (from content-highlighting.js)
            }

            if (typeof closeHighlightPalette === 'function') closeHighlightPalette(); else console.warn("closeHighlightPalette missing.");
        });
        styleSelector.appendChild(styleButton);
    });
    palette.appendChild(styleSelector);

    // --- Create Snippet Selector Container ---
    const snippetSelector = document.createElement('div');
    snippetSelector.className = 'Stickara-highlight-snippet-selector';

    // No separator needed anymore

    Object.entries(snippets).forEach(([snippetType, snippetInfo]) => {
        const snippetButton = document.createElement('button');
        snippetButton.className = 'Stickara-highlight-snippet-btn';
        snippetButton.dataset.snippet = snippetType;
        snippetButton.innerHTML = `<span class="Stickara-icon">${snippetInfo.icon}</span>`;
        snippetButton.title = snippetInfo.title;
        snippetButton.setAttribute('aria-label', snippetInfo.title);

        // Snippet Button Click Handler
        snippetButton.addEventListener('click', (e) => {
            e.stopPropagation();
            const selectedSnippet = snippetButton.dataset.snippet;
            const selection = window.getSelection();

            if (!selection || selection.rangeCount === 0 || selection.isCollapsed) {
                console.log("Stickara Palette: No selection for snippet.");
                if (typeof closeHighlightPalette === 'function') closeHighlightPalette();
                return;
            }

            // Check if selection is inside the note editor
            let isInsideNote = false;
            const range = selection.getRangeAt(0);
            const container = range.commonAncestorContainer;
            if (container && noteText) { // Ensure noteText exists
                isInsideNote = noteText.contains(container.nodeType === Node.ELEMENT_NODE ? container : container.parentElement);
            }

            // Call the appropriate function based on snippet type
            if (isInsideNote) {
                // For in-note snippets, we'll use a special formatting
                console.log(`Stickara Palette: Applying IN NOTE snippet: ${selectedSnippet}`);

                // Get the selected text
                const selectedText = selection.toString().trim();
                if (!selectedText) {
                    console.log("Stickara Palette: Empty selection for snippet.");
                    if (typeof closeHighlightPalette === 'function') closeHighlightPalette();
                    return;
                }

                // Create formatted text with appropriate styling for the Stickara
                let formattedText = '';
                if (selectedSnippet === 'review-pro') {
                    // Use our security utility function to sanitize text
                    const sanitizedText = typeof sanitizeText === 'function' ?
                        sanitizeText(selectedText) :
                        document.createTextNode(selectedText).textContent;
                    formattedText = `<span style="font-weight: bold; color: #4CAF50">👍 Pro: </span>${sanitizedText}`;
                } else if (selectedSnippet === 'review-con') {
                    const sanitizedText = typeof sanitizeText === 'function' ?
                        sanitizeText(selectedText) :
                        document.createTextNode(selectedText).textContent;
                    formattedText = `<span style="font-weight: bold; color: #F44336">👎 Con: </span>${sanitizedText}`;
                } else if (selectedSnippet === 'spec') {
                    const sanitizedText = typeof sanitizeText === 'function' ?
                        sanitizeText(selectedText) :
                        document.createTextNode(selectedText).textContent;
                    formattedText = `<span style="font-weight: bold; color: #2196F3">📋 Spec: </span>${sanitizedText}`;
                }

                // Replace the selected text with the formatted text
                document.execCommand('insertHTML', false, formattedText);

                // Check if we need to create a Pro/Con comparison view
                if (typeof checkForProConSnippets === 'function') {
                    console.log("Stickara: Snippet added in UI, checking for Pro/Con comparison");
                    setTimeout(checkForProConSnippets, 300);
                }

            } else {
                // For on-page snippets, call the appropriate handler
                console.log(`Stickara Palette: Applying ON PAGE snippet: ${selectedSnippet}`);

                if (selectedSnippet === 'review-pro' && typeof handleHighlightReviewSnippet === 'function') {
                    handleHighlightReviewSnippet('pro', DEFAULT_HIGHLIGHT_COLOR, 'color');
                } else if (selectedSnippet === 'review-con' && typeof handleHighlightReviewSnippet === 'function') {
                    handleHighlightReviewSnippet('con', DEFAULT_HIGHLIGHT_COLOR, 'color');
                } else if (selectedSnippet === 'spec' && typeof handleHighlightSpecSnippet === 'function') {
                    handleHighlightSpecSnippet(DEFAULT_HIGHLIGHT_COLOR, 'color');
                } else {
                    console.warn(`Stickara Palette: Handler for snippet type ${selectedSnippet} not found.`);
                }
            }

            if (typeof closeHighlightPalette === 'function') closeHighlightPalette(); else console.warn("closeHighlightPalette missing.");
        });

        snippetSelector.appendChild(snippetButton);
    });

    palette.appendChild(snippetSelector);

    // --- Create Color Swatch Container ---
    const colorSelector = document.createElement('div');
    colorSelector.className = 'Stickara-highlight-color-selector';

    colors.forEach(colorName => {
        const swatchButton = document.createElement('button');
        swatchButton.className = HIGHLIGHT_PALETTE_SWATCH_CLASS;
        swatchButton.dataset.color = colorName;
        swatchButton.style.backgroundColor = isHighlightColorsValid ? (HIGHLIGHT_COLORS[colorName] || defaultBg) : defaultBg;
        swatchButton.title = `Highlight as ${colorName}`;
        swatchButton.setAttribute('aria-label', `Highlight selection as ${colorName}`);

        // --- MODIFIED Color Swatch Click Handler (Handles both Note & Page) ---
        swatchButton.addEventListener('click', (e) => {
            e.stopPropagation();
            const selectedColor = swatchButton.dataset.color;
            const selection = window.getSelection();

            if (!selection || selection.rangeCount === 0 || selection.isCollapsed) {
                console.log("Stickara Palette: No selection to highlight.");
                if (typeof closeHighlightPalette === 'function') closeHighlightPalette();
                return;
            }

            // Check if selection is inside the note editor
            let isInsideNote = false;
            const range = selection.getRangeAt(0);
            const container = range.commonAncestorContainer;
            if (container && noteText) { // Ensure noteText exists
                 isInsideNote = noteText.contains(container.nodeType === Node.ELEMENT_NODE ? container : container.parentElement);
            }

            // Call the appropriate function
            if (isInsideNote) {
                console.log(`Stickara Palette: Applying IN NOTE color: ${selectedColor}`);
                applyInNoteHighlight(selectedColor, 'color'); // (from content-interactions.js)
            } else {
                 console.log(`Stickara Palette: Applying ON PAGE color: ${selectedColor}`);
                 handleHighlightSelection(selectedColor, 'color'); // (from content-highlighting.js)
            }

            if (typeof closeHighlightPalette === 'function') closeHighlightPalette();
        });
        colorSelector.appendChild(swatchButton);
    });
    palette.appendChild(colorSelector);

    // --- Final Palette Setup ---
    palette.addEventListener('click', (e) => e.stopPropagation());
    palette.style.visibility = 'hidden';
    palette.style.display = 'flex';

    return palette;
}

/** Toggles the visibility and position of the highlight color palette. */
function toggleHighlightPalette() {
    if (typeof closeAllDropdowns !== 'function' || typeof closeHighlightPalette !== 'function' || typeof isHighlightPaletteOpen === 'undefined' || typeof HIGHLIGHT_PALETTE_ID === 'undefined') {
        console.error("Error: Required functions/state/constants missing for palette toggle."); return; }

    closeAllDropdowns();

    let palette = document.getElementById(HIGHLIGHT_PALETTE_ID);
    const triggerButton = document.getElementById('Stickara-snippet-highlight-btn');
    const snippetsToolbar = triggerButton?.closest('.Stickara-quick-snippets-toolbar');

     if (!triggerButton || !snippetsToolbar) { console.error("Error: Highlight trigger button or toolbar parent not found."); return; }

    if (isHighlightPaletteOpen && palette) {
        closeHighlightPalette();
    } else {
        if (!palette) {
            if (typeof createHighlightColorPalette !== 'function') { console.error("createHighlightColorPalette missing."); return; }
            palette = createHighlightColorPalette();
            if (!palette) { console.error("Failed to create highlight palette."); return; }
            snippetsToolbar.appendChild(palette);
        }

        if (palette) {
            palette.style.visibility = 'hidden'; palette.style.display = 'flex'; const paletteHeight = palette.offsetHeight;
            palette.style.position = 'absolute'; const topPosition = triggerButton.offsetTop - paletteHeight - 5;
            palette.style.top = `${topPosition}px`; palette.style.left = `${triggerButton.offsetLeft}px`; palette.style.right = 'auto';
            palette.style.display = 'flex'; palette.style.visibility = 'visible';
            isHighlightPaletteOpen = true; palette.setAttribute('aria-hidden', 'false');
            // Focus the first swatch or button
            palette.querySelector(`.${HIGHLIGHT_PALETTE_SWATCH_CLASS}, .Stickara-highlight-style-btn`)?.focus();
        } else { console.error("Could not position palette, element missing."); }
    }
}

/** Closes the highlight color palette if it's open. */
function closeHighlightPalette() {
    if (typeof isHighlightPaletteOpen === 'undefined' || typeof HIGHLIGHT_PALETTE_ID === 'undefined') { console.warn("Cannot close palette, state/constants missing."); return; }
    if (!isHighlightPaletteOpen) return;
    const palette = document.getElementById(HIGHLIGHT_PALETTE_ID);
    if (palette) {
        palette.style.display = 'none'; palette.style.visibility = 'hidden';
        palette.setAttribute('aria-hidden', 'true');
        isHighlightPaletteOpen = false;
    }
}

/** Closes all open dropdowns AND the highlight palette. */
function closeAllDropdowns(e) {
    // If the event target is the calendar checkbox or its wrapper, don't do anything
    if (e?.target.closest('.Stickara-calendar-option, #Stickara-calendar-checkbox')) {
        return;
    }

    const tagsId = typeof TAGS_ID !== 'undefined' ? TAGS_ID : 'Stickara-tags';
    const reminderId = typeof REMINDER_ID !== 'undefined' ? REMINDER_ID : 'Stickara-reminder';
    const noteTitleInputId = typeof NOTE_TITLE_INPUT_ID !== 'undefined' ? NOTE_TITLE_INPUT_ID : 'Stickara-note-title-input';
    const flashcardModalId = typeof FLASHCARD_MODAL_ID !== 'undefined' ? FLASHCARD_MODAL_ID : 'Stickara-flashcard-overlay';
    const highlightPaletteId = typeof HIGHLIGHT_PALETTE_ID !== 'undefined' ? HIGHLIGHT_PALETTE_ID : 'Stickara-highlight-palette';
    const diagramEditorSelector = '.Stickara-diagram-editor';

    // Don't close if clicking inside dropdown/palette/input/editor
    const shouldKeepOpen = e?.target.closest(
        `.Stickara-dropdown-content, #${highlightPaletteId}, #${tagsId}, #${reminderId}, #${noteTitleInputId}, ${diagramEditorSelector}, #${flashcardModalId}, #Stickara-metadata-container, .Stickara-input-wrapper, .Stickara-input-label, .Stickara-calendar-option, #Stickara-calendar-checkbox`
    );

    if (shouldKeepOpen) return;

    // Close standard dropdowns
    document.querySelectorAll('.Stickara-dropdown-content.show').forEach(content => {
        content.classList.remove('show'); content.setAttribute('aria-hidden', 'true');
    });

    // Close the highlight palette specifically
    if (typeof closeHighlightPalette === 'function') closeHighlightPalette();
    else console.warn("closeHighlightPalette function missing in closeAllDropdowns.");
}

// --- END: Restore Highlight Palette UI Functions ---


// --- UI Update Helpers ---

/** Calculates and sets the appropriate height for the noteText element */
function calculateTextAreaHeight() {
     if (!noteContainer || !noteHeader || !tagsInput || !reminderInput || !noteText) { return; }
    const metadataContainer = document.getElementById('Stickara-metadata-container');
    const quickSnippetsToolbar = noteContainer.querySelector('.Stickara-quick-snippets-toolbar');
    const searchBar = document.getElementById('Stickara-in-note-search-bar');

    // Minimize functionality removed
    noteText.style.display = '';
    if (quickSnippetsToolbar) quickSnippetsToolbar.style.display = '';
    if (metadataContainer) metadataContainer.style.display = '';
    if (searchBar && typeof inNoteSearchActive !== 'undefined' && inNoteSearchActive) searchBar.style.display = 'flex';
    else if (searchBar) searchBar.style.display = 'none';

    try {
        const containerStyle = window.getComputedStyle(noteContainer); const textStyle = window.getComputedStyle(noteText);
        const metadataStyle = metadataContainer ? window.getComputedStyle(metadataContainer) : { offsetHeight: 0, marginTop: '0', marginBottom: '0'};
        const snippetsStyle = quickSnippetsToolbar ? window.getComputedStyle(quickSnippetsToolbar) : { offsetHeight: 0, marginTop: '0', marginBottom: '0'};
        const searchBarStyle = searchBar && searchBar.style.display !== 'none' ? window.getComputedStyle(searchBar) : { offsetHeight: 0, marginTop: '0', marginBottom: '0'};
        const containerHeight = noteContainer.offsetHeight; const headerHeight = noteHeader.offsetHeight;

        // Use the metadata container height instead of individual inputs
        const metadataHeight = (metadataContainer?.offsetHeight || 0) + parseFloat(metadataStyle.marginTop || '0') + parseFloat(metadataStyle.marginBottom || '0');
        const snippetsHeight = (quickSnippetsToolbar?.offsetHeight || 0) + parseFloat(snippetsStyle.marginTop || '0') + parseFloat(snippetsStyle.marginBottom || '0');
        const searchBarHeight = (searchBar?.offsetHeight || 0) + parseFloat(searchBarStyle.marginTop || '0') + parseFloat(searchBarStyle.marginBottom || '0');
        const textMargins = parseFloat(textStyle.marginTop || '0') + parseFloat(textStyle.marginBottom || '0');
        const containerVPadding = parseFloat(containerStyle.paddingTop || '0') + parseFloat(containerStyle.paddingBottom || '0');

        // Calculate with the metadata container height
        const occupiedHeight = headerHeight + snippetsHeight + searchBarHeight + metadataHeight + textMargins;
        const availableHeight = Math.max(0, containerHeight - occupiedHeight - containerVPadding);
        const calculatedHeight = Math.max(50, availableHeight); // Min height 50px
        noteText.style.height = calculatedHeight + 'px'; noteText.style.maxHeight = calculatedHeight + 'px';
    } catch (e) { console.error("Error calculating text area height:", e); noteText.style.height = '150px'; noteText.style.maxHeight = '150px'; }
}


/** Updates the visual state of color swatches */
function updateActiveSwatch(color) {
    // This function might not be strictly necessary now that palette doesn't persist state,
    // but keeping it doesn't hurt.
    if (!noteContainer) return;
    noteContainer.querySelectorAll('.Stickara-color-swatch').forEach(swatch => {
        swatch.classList.toggle('active', swatch.dataset.color === color); });
}

/** Updates the visual state of opacity buttons */
function updateActiveOpacity(opacity) {
    if (!noteContainer) return;
    noteContainer.querySelectorAll('.Stickara-opacity-btn').forEach(btn => {
        const btnOpacity = parseFloat(btn.dataset.opacity);
        btn.classList.toggle('active', !isNaN(btnOpacity) && Math.abs(btnOpacity - opacity) < 0.01); });
}

/** Updates the visual state of text shadow button */
function updateActiveTextShadow(isEnabled) {
    if (!noteContainer) return;
    noteContainer.querySelectorAll('.Stickara-text-shadow-btn').forEach(btn => {
        btn.classList.toggle('active', isEnabled);
        btn.dataset.textShadow = String(isEnabled);
    });
}
/** Updates the visual state of the pin button */
function updatePinButtonState(isPinned) {
    if (!noteContainer) return;
    const pinBtnId = typeof 'Stickara-pin-btn' !== 'undefined' ? 'Stickara-pin-btn' : null;
    const pinButton = pinBtnId ? noteContainer.querySelector(`#${pinBtnId}`) : null;
    if (pinButton) { const newTitle = isPinned ? 'Unpin from page' : 'Pin to page'; pinButton.classList.toggle('active', isPinned);
        pinButton.title = newTitle; pinButton.setAttribute('aria-label', newTitle);
        const iconSpan = pinButton.querySelector('.Stickara-icon'); if (iconSpan) iconSpan.textContent = isPinned ? '📍' : '📌'; }
    // Removed console.warn to reduce noise - button may not be created yet
}

/** Updates the visual state of the important note button (formerly global pin) */
function updateGlobalPinButtonState(isGloballyPinned) {
    if (!noteContainer) return;
    const globalPinBtnId = typeof 'Stickara-global-pin-btn' !== 'undefined' ? 'Stickara-global-pin-btn' : null;
    const globalPinButton = globalPinBtnId ? noteContainer.querySelector(`#${globalPinBtnId}`) : null;
    if (globalPinButton) {
        const newTitle = isGloballyPinned ? 'Remove from important notes' : 'Mark as important note';
        globalPinButton.classList.toggle('active', isGloballyPinned);
        globalPinButton.title = newTitle;
        globalPinButton.setAttribute('aria-label', newTitle);
        const iconSpan = globalPinButton.querySelector('.Stickara-icon');
        if (iconSpan) iconSpan.textContent = isGloballyPinned ? '⭐' : '☆';
    }
    // Removed console.warn to reduce noise - button may not be created yet
}

/** Updates the visual state of the minimize/maximize button - REMOVED */
// Minimize functionality removed

/** Updates the visual state of the global note button */
function updateGlobalNoteButtonState(isGlobal) {
    if (!noteContainer) return;
    const globalNoteBtnId = 'Stickara-global-note-btn';
    const globalNoteButton = noteContainer.querySelector(`#${globalNoteBtnId}`);
    if (globalNoteButton) {
        const newTitle = isGlobal ? 'Disable global note mode' : 'Enable global note mode';
        globalNoteButton.classList.toggle('active', isGlobal);
        globalNoteButton.title = newTitle;
        globalNoteButton.setAttribute('aria-label', newTitle);
        const iconSpan = globalNoteButton.querySelector('.Stickara-icon');
        if (iconSpan) iconSpan.textContent = isGlobal ? '🌐' : '🔗';
    }
    // Removed console.warn to reduce noise - button may not be created yet
}

/** Handles Find shortcut (Ctrl/Cmd+F) and Escape key for closing search bar. */
function handleFindShortcut(event) {
    // Escape Key Logic - Remains Global
    if (event.key === 'Escape') {
        const searchInputId = 'Stickara-in-note-search-input';
        const searchBar = document.getElementById('Stickara-in-note-search-bar');
        const searchInput = document.getElementById(searchInputId);
        if (searchBar && searchBar.style.display !== 'none') {
            const activeEl = document.activeElement;
            if (activeEl && (activeEl === searchInput || searchBar.contains(activeEl))) {
                 event.preventDefault(); event.stopPropagation(); // Stop Esc
                 if (typeof hideInNoteSearchBar === 'function') hideInNoteSearchBar(); else console.error("hideInNoteSearchBar missing.");
            }
        }
    }
    // Ctrl+F logic is now handled primarily in the noteText bubbling listener
}


/**
 * Shows a status notification in the note UI
 * @param {string} message - The message to display
 * @param {string} type - The type of message ('success', 'error', 'info')
 * @param {number} duration - How long to show the message in ms (default: 3000)
 */
function showStatus(message, type = 'info', duration = 3000) {
    if (!noteContainer) return;

    // Remove any existing status notifications
    const existingStatus = document.getElementById('Stickara-status-notification');
    if (existingStatus) {
        existingStatus.remove();
    }

    // Create the status notification element
    const statusEl = document.createElement('div');
    statusEl.id = 'Stickara-status-notification';
    statusEl.className = `Stickara-status-notification ${type}`;
    statusEl.textContent = message;

    // Add appropriate icon based on type
    let icon = '💬';
    if (type === 'success') icon = '✅';
    if (type === 'error') icon = '❌';
    if (type === 'warning') icon = '⚠️';

    // Add icon to the beginning of the message
    statusEl.textContent = `${icon} ${message}`;

    // Add styles inline to ensure they're applied
    statusEl.style.position = 'absolute';
    statusEl.style.bottom = '10px';
    statusEl.style.left = '50%';
    statusEl.style.transform = 'translateX(-50%)';
    statusEl.style.backgroundColor = type === 'success' ? 'rgba(34, 197, 94, 0.9)' :
                                    type === 'error' ? 'rgba(239, 68, 68, 0.9)' :
                                    'rgba(59, 130, 246, 0.9)';
    statusEl.style.color = 'white';
    statusEl.style.padding = '8px 16px';
    statusEl.style.borderRadius = '6px';
    statusEl.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
    statusEl.style.zIndex = '1000';
    statusEl.style.fontSize = '14px';
    statusEl.style.fontWeight = '500';
    statusEl.style.opacity = '0';
    statusEl.style.transition = 'opacity 0.3s ease';

    // Add to the note container
    noteContainer.appendChild(statusEl);

    // Fade in
    setTimeout(() => {
        statusEl.style.opacity = '1';
    }, 10);

    // Fade out and remove after duration
    setTimeout(() => {
        statusEl.style.opacity = '0';
        setTimeout(() => {
            if (statusEl.parentNode) {
                statusEl.parentNode.removeChild(statusEl);
            }
        }, 300);
    }, duration);
}

/**
 * Shows feedback when copying text
 * @param {string} message - The message to display
 * @param {boolean} isError - Whether this is an error message
 */
function showCopyFeedback(message, isError = false) {
    showStatus(message, isError ? 'error' : 'success', 1500);
}

console.log("Stickara: UI Logic Loaded");
// --- END OF FILE content-ui.js ---