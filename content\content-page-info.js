// --- START OF FILE content-page-info.js ---

/**
 * Captures the page title and main H1 heading
 * @returns {Object} Object containing title and heading
 */
function capturePageInfo() {
    // Get page title
    const pageTitle = document.title || '';
    
    // Find the main H1 heading
    let mainHeading = '';
    const h1Elements = document.querySelectorAll('h1');
    
    if (h1Elements.length > 0) {
        // Get the most prominent H1 (usually the first one that's visible)
        for (const h1 of h1Elements) {
            // Skip hidden elements
            if (h1.offsetParent === null || 
                window.getComputedStyle(h1).display === 'none' || 
                window.getComputedStyle(h1).visibility === 'hidden') {
                continue;
            }
            
            // Skip Stickara UI elements
            if (h1.closest('#Stickara-note-container, .Stickara-diagram-editor, #Stickara-flashcard-modal')) {
                continue;
            }
            
            // Get the text content
            const headingText = h1.textContent.trim();
            if (headingText) {
                mainHeading = headingText;
                break;
            }
        }
    }
    
    return { pageTitle, mainHeading };
}

/**
 * Inserts the page title and main H1 heading at the cursor position
 */
function handleAddPageInfoNote() {
    if (!noteContainer || !noteText || !noteContainer.classList.contains('visible')) {
        showNote();
        // Use internal function after a delay to ensure UI is ready
        setTimeout(handleAddPageInfoNoteInternal, 150);
    } else {
        handleAddPageInfoNoteInternal();
    }
}

// Internal function to perform the page info addition
function handleAddPageInfoNoteInternal() {
    if (!noteText) {
        console.error("Stickara: noteText element not found for page info insertion.");
        return;
    }

    // Get page info
    const { pageTitle, mainHeading } = capturePageInfo();
    
    // Create formatted text
    let pageInfoText = '';
    
    if (pageTitle) {
        pageInfoText += `📄 Page: ${pageTitle}\n`;
    }
    
    if (mainHeading && mainHeading !== pageTitle) {
        pageInfoText += `📌 Heading: ${mainHeading}\n`;
    }
    
    if (pageInfoText) {
        // Add a separator line
        pageInfoText += `${'─'.repeat(40)}\n\n`;
        
        // Focus the note text area
        noteText.focus();
        
        // Insert text at cursor position
        document.execCommand('insertText', false, pageInfoText);
        
        // Schedule save
        scheduleSave();
        
        console.log(`Stickara: Page info inserted`);
    } else {
        console.warn("Stickara: No page info found to insert");
    }
}

// --- END OF FILE content-page-info.js ---
