// --- START OF FILE content-state.js ---

// --- Constants ---
const NOTE_ID = 'Stickara-container';
const TOGGLE_ID = 'Stickara-toggle';
const HEADER_ID = 'Stickara-header';
const TEXTAREA_ID = 'Stickara-text';
const TAGS_ID = 'Stickara-tags';
const REMINDER_ID = 'Stickara-reminder';
const TIMESTAMP_ID = 'Stickara-timestamp';
// const TITLE_ID = 'Stickara-title'; // Obsolete, replaced by input
const NOTE_TITLE_INPUT_ID = 'Stickara-note-title-input'; // NEW ID for title input
const HEADER_CONTROLS_ID = 'Stickara-header-controls';
const NOTE_SWITCHER_ID = 'Stickara-note-switcher-btn'; // Keep ID, purpose changed
const IMAGE_INPUT_ID = 'Stickara-image-input';
const TIMESTAMP_LINK_CLASS = 'Stickara-timestamp-link';

const FLASHCARD_MODAL_ID = 'Stickara-flashcard-overlay';
const FLASHCARD_CONTENT_ID = 'Stickara-flashcard-content';
const FLASHCARD_COUNTER_ID = 'Stickara-flashcard-counter';
const FLASHCARD_PREV_ID = 'Stickara-flashcard-prev';
const FLASHCARD_NEXT_ID = 'Stickara-flashcard-next';
const FLASHCARD_CLOSE_ID = 'Stickara-flashcard-close-btn';
const FLASHCARD_STUDY_BTN_ID = 'Stickara-study-flashcards';

const VOICE_RECORD_BTN_ID = 'Stickara-record-audio';

// --- Highlighting Constants ---
const HIGHLIGHT_KEY_PREFIX = 'Stickara_highlights_';
const HIGHLIGHT_CLASS = 'Stickara-highlight';
// --- NEW: Highlight Colors ---
const HIGHLIGHT_COLORS = {
    yellow: '#fff352', // Default
    pink: '#ffafcc',
    blue: '#a0c4ff',
    green: '#b9fbc0',
    purple: '#d6bfff',
};
const DEFAULT_HIGHLIGHT_COLOR = 'yellow';
// --- End Highlighting Constants ---

const STORAGE_KEY_PREFIX = 'Stickara_note_';
const STATE_KEY_PREFIX = 'Stickara_state_';

const DEFAULT_COLOR = 'yellow';
const DEFAULT_OPACITY = 1.0;
const DEFAULT_FONT_SIZE = 14; // pixels

// --- State Variables ---

// DOM Element References (will be populated by UI creation)
let noteContainer = null;
let noteText = null;
let noteHeader = null;
let toggleButton = null;
let timestampSpan = null;
let noteSwitcher = null; // The dropdown container div
let tagsInput = null;
let reminderInput = null;
let noteTitleInput = null; // <<< NEW: Reference for the title input
let flashcardModalOverlay = null;
let flashcardContent = null;
let flashcardCounter = null;
let flashcardPrevBtn = null;
let flashcardNextBtn = null;

let lastTimestampedVideoSrc = null;
let isFullyInitialized = false;

// Interaction State
let isDragging = false;
let dragStartX, dragStartY, initialLeft, initialTop;
let saveTimeout = null; // For debouncing saves
let resizeObserver = null; // For observing note container resize

// Data State
let currentNoteIndex = 1; // Default to note 1
let notes = {}; // Cache for loaded note data: { 1: {noteData}, 2: {noteData}, ..., 10: {noteData} }
let highlightsData = []; // Cache for loaded highlight data for the current page

// Flashcard State
let parsedFlashcards = []; // Store parsed cards for the current study session
let currentFlashcardIndex = 0;
let isFlashcardBackVisible = false;

// Voice Recording State
let recognition = null; // SpeechRecognition instance
let isRecording = false;
let pendingTranscript = ''; // Buffer for accumulating text before insertion
let insertionTimer = null;
// YouTube-aware insertion delay - faster on YouTube for better real-time experience
const INSERTION_DELAY = window.location.hostname.includes('youtube.com') ? 50 : 100;
let interimTranscriptElement = null; // Element to hold interim transcription preview
let originalNoteContent = ''; // Store original note content before interim preview
let voiceInsertionPoint = null; // Store cursor position for voice insertion
let lastInterimUpdate = 0; // Throttle interim updates for performance
let voiceStatusIndicator = null; // Visual status indicator for voice recording
let voiceAriaLiveRegion = null; // ARIA live region for screen readers
let lastProcessedCommand = null; // Track last processed voice command to avoid duplicates
let pendingVoiceCommands = []; // Store voice commands to execute after text insertion

// Performance monitoring for voice commands
const voiceCommandPerformance = {
    totalCommands: 0,
    successfulCommands: 0,
    failedCommands: 0,
    averageExecutionTime: 0,
    commandTypeStats: {},
    lastResetTime: Date.now()
};

// Voice command modifier key system
const voiceModifierKeySystem = {
    enabled: true, // Whether the modifier key system is enabled
    modifierKey: 'ctrlKey', // Default modifier key (ctrlKey, altKey, shiftKey)
    isModifierPressed: false, // Current state of the modifier key
    lastKeyState: false, // Previous state for change detection
    visualFeedbackEnabled: true, // Whether to show visual feedback
    keyListenersAttached: false // Track if event listeners are attached
};

const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

// Voice command patterns for line breaks and symbols
const VOICE_COMMANDS = {
    lineBreak: [
        /\b(new line|newline|line break|break line|enter)\b/gi,
        /\b(go to next line|next line|line return)\b/gi,
        /\b(press enter|hit enter)\b/gi
    ],
    paragraphBreak: [
        /\b(new paragraph|paragraph|paragraph break|double enter)\b/gi,
        /\b(start new paragraph|begin paragraph|start paragraph)\b/gi,
        /\b(double line break|double newline)\b/gi
    ],
    exclamationMark: [
        /\b(exclamation mark|exclamation point|bang)\b/gi
    ],
    atSymbol: [
        /\b(at sign|at symbol|at)\b/gi
    ],
    hashSymbol: [
        /\b(hash|pound sign|hashtag|number sign)\b/gi
    ],
    dollarSign: [
        /\b(dollar sign|dollar)\b/gi
    ],
    percentSign: [
        /\b(percent|percent sign)\b/gi
    ],
    openBrace: [
        /\b(open brace|left brace|open curly brace|left curly brace)\b/gi
    ],
    closeBrace: [
        /\b(close brace|right brace|close curly brace|right curly brace)\b/gi
    ],
    colon: [
        /\b(colon)\b/gi
    ],
    semicolon: [
        /\b(semicolon)\b/gi
    ],
    doubleQuote: [
        /\b(quote|double quote|quotation mark)\b/gi
    ],
    openQuote: [
        /\b(open quote|start quote)\b/gi
    ],
    closeQuote: [
        /\b(close quote|end quote)\b/gi
    ],
    apostrophe: [
        /\b(apostrophe|single quote)\b/gi
    ],
    // Basic punctuation
    period: [
        /\b(dot|full stop|period)\b/gi
    ],
    comma: [
        /\b(comma)\b/gi
    ],
    questionMark: [
        /\b(question mark|question)\b/gi
    ],
    // Mathematical/Technical symbols
    plusSign: [
        /\b(plus|plus sign)\b/gi
    ],
    minusSign: [
        /\b(minus|dash|hyphen|minus sign)\b/gi
    ],
    equalsSign: [
        /\b(equals|equals sign)\b/gi
    ],
    asterisk: [
        /\b(asterisk|star)\b/gi
    ],
    forwardSlash: [
        /\b(slash|forward slash)\b/gi
    ],
    backslash: [
        /\b(backslash)\b/gi
    ],
    // Brackets
    openParenthesis: [
        /\b(open parenthesis|left parenthesis|open paren)\b/gi
    ],
    closeParenthesis: [
        /\b(close parenthesis|right parenthesis|close paren)\b/gi
    ],
    openBracket: [
        /\b(open bracket|left bracket)\b/gi
    ],
    closeBracket: [
        /\b(close bracket|right bracket)\b/gi
    ],
    // Additional symbols
    underscore: [
        /\b(underscore)\b/gi
    ],
    pipe: [
        /\b(pipe|vertical bar)\b/gi
    ],
    ampersand: [
        /\b(ampersand|and sign)\b/gi
    ]
};

// Pre-compiled regex patterns for performance optimization
const COMPILED_VOICE_PATTERNS = {};

// Initialize compiled patterns on load
function initializeCompiledPatterns() {
    for (const [commandType, patterns] of Object.entries(VOICE_COMMANDS)) {
        COMPILED_VOICE_PATTERNS[commandType] = patterns.map(pattern => ({
            regex: new RegExp(pattern.source, pattern.flags),
            original: pattern
        }));
    }
    console.log('Stickara: Voice command patterns compiled for performance');
}

// Symbol mapping for voice commands
const VOICE_COMMAND_SYMBOLS = {
    exclamationMark: '!',
    atSymbol: '@',
    hashSymbol: '#',
    dollarSign: '$',
    percentSign: '%',
    openBrace: '{',
    closeBrace: '}',
    colon: ':',
    semicolon: ';',
    doubleQuote: '"',
    openQuote: '"',
    closeQuote: '"',
    apostrophe: "'",
    // Basic punctuation
    period: '.',
    comma: ',',
    questionMark: '?',
    // Mathematical/Technical symbols
    plusSign: '+',
    minusSign: '-',
    equalsSign: '=',
    asterisk: '*',
    forwardSlash: '/',
    backslash: '\\',
    // Brackets
    openParenthesis: '(',
    closeParenthesis: ')',
    openBracket: '[',
    closeBracket: ']',
    // Additional symbols
    underscore: '_',
    pipe: '|',
    ampersand: '&'
};

// In-Note Search State
let inNoteSearchActive = false; // Flag to track if the in-note search bar is active

// Environment State
const rawCurrentUrl = window.location.href;
// Store the raw URL for display purposes and use it directly for storage keys
// This ensures URLs are preserved exactly as they appear in the browser
window.stickaraRawCurrentUrl = rawCurrentUrl;
let currentUrl = rawCurrentUrl; // Use raw URL directly without normalization
let stateKey = `${STATE_KEY_PREFIX}${currentUrl}`;
let highlightKey = `${HIGHLIGHT_KEY_PREFIX}${currentUrl}`; // Define highlight storage key

// Initialize compiled patterns for performance
initializeCompiledPatterns();

// Prevent infinite loops during URL context updates
let isUpdatingURLContext = false;

/**
 * Updates the URL context when the page URL changes (for SPA navigation like YouTube)
 * This ensures notes display the correct URL and are associated with the current page
 */
function updateNoteURLContext() {
    // Prevent recursive calls
    if (isUpdatingURLContext) {
        console.log("Stickara: URL context update already in progress, skipping");
        return;
    }

    const newRawUrl = window.location.href;
    const oldRawUrl = window.stickaraRawCurrentUrl;

    // Only update if the URL actually changed
    if (newRawUrl === oldRawUrl) {
        return;
    }

    console.log(`Stickara: URL changed from ${oldRawUrl} to ${newRawUrl}`);

    // Set flag to prevent recursive calls
    isUpdatingURLContext = true;

    // Update global URL variables
    window.stickaraRawCurrentUrl = newRawUrl;

    // Use raw URL directly for storage keys to preserve exact URL format
    currentUrl = newRawUrl;

    // Update storage keys
    stateKey = `${STATE_KEY_PREFIX}${currentUrl}`;
    highlightKey = `${HIGHLIGHT_KEY_PREFIX}${currentUrl}`;

    // Reload the note context for the new URL
    // Add a small delay for YouTube to ensure the page has fully navigated
    setTimeout(() => {
        if (typeof reloadNotesForNewURL === 'function') {
            reloadNotesForNewURL();
        }

        // Reset the flag after processing
        setTimeout(() => {
            isUpdatingURLContext = false;
        }, 1000);
    }, 500);

    console.log(`Stickara: Updated URL context to ${newRawUrl}`);
}

/**
 * Sets up note URL update handling for SPA navigation (YouTube, etc.)
 * This is separate from the highlight system and focuses only on note URL updates
 */
function setupNoteURLUpdateHandling() {
    // Comprehensive list of SPA sites where URL context switching makes sense
    const spaSites = [
        // AI & Chat Platforms
        'chatgpt.com', 'chat.openai.com', 'claude.ai', 'bard.google.com', 'bing.com', 'copilot.microsoft.com',
        'character.ai', 'poe.com', 'perplexity.ai', 'you.com', 'phind.com', 'writesonic.com', 'jasper.ai',
        'copy.ai', 'rytr.me', 'anyword.com', 'shortly.ai', 'peppertype.ai', 'contentbot.ai', 'wordtune.com',

        // Social Media & Communication
        'twitter.com', 'x.com', 'facebook.com', 'instagram.com', 'linkedin.com', 'tiktok.com', 'snapchat.com',
        'pinterest.com', 'tumblr.com', 'reddit.com', 'discord.com', 'slack.com', 'telegram.org', 'whatsapp.com',
        'messenger.com', 'skype.com', 'zoom.us', 'teams.microsoft.com', 'meet.google.com', 'webex.com',
        'gotomeeting.com', 'bluejeans.com', 'whereby.com', 'jitsi.meet', 'bigbluebutton.org', 'gather.town',

        // Video & Streaming
        'youtube.com', 'vimeo.com', 'dailymotion.com', 'twitch.tv', 'netflix.com', 'hulu.com', 'disneyplus.com',
        'primevideo.com', 'hbomax.com', 'paramount.com', 'peacocktv.com', 'crunchyroll.com', 'funimation.com',
        'spotify.com', 'soundcloud.com', 'pandora.com', 'deezer.com', 'tidal.com', 'apple.com', 'music.amazon.com',

        // Development & Code
        'github.com', 'gitlab.com', 'bitbucket.org', 'codepen.io', 'jsfiddle.net', 'codesandbox.io', 'replit.com',
        'stackblitz.com', 'glitch.com', 'vercel.com', 'netlify.com', 'heroku.com', 'railway.app', 'render.com',
        'digitalocean.com', 'aws.amazon.com', 'console.cloud.google.com', 'portal.azure.com', 'firebase.google.com',
        'supabase.com', 'planetscale.com', 'neon.tech', 'upstash.com', 'mongodb.com', 'redis.com',

        // Design & Creative
        'figma.com', 'canva.com', 'sketch.com', 'adobe.com', 'invisionapp.com', 'miro.com', 'mural.co',
        'whimsical.com', 'lucidchart.com', 'draw.io', 'creately.com', 'conceptboard.com', 'milanote.com',
        'behance.net', 'dribbble.com', '99designs.com', 'awwwards.com', 'designspiration.com',

        // Productivity & Collaboration
        'notion.so', 'obsidian.md', 'roamresearch.com', 'logseq.com', 'craft.do', 'bear.app', 'ulysses.app',
        'typora.io', 'marktext.app', 'zettlr.com', 'joplin.app', 'standardnotes.org', 'simplenote.com',
        'evernote.com', 'onenote.com', 'googledocs.com', 'docs.google.com', 'office.com', 'onedrive.com',
        'dropbox.com', 'box.com', 'icloud.com', 'mega.nz', 'pcloud.com', 'sync.com',

        // Project Management
        'trello.com', 'asana.com', 'monday.com', 'clickup.com', 'airtable.com', 'basecamp.com', 'wrike.com',
        'smartsheet.com', 'teamwork.com', 'workfront.com', 'clarizen.com', 'zoho.com', 'freshworks.com',
        'hubspot.com', 'salesforce.com', 'pipedrive.com', 'crm.dynamics.com', 'zendesk.com', 'intercom.com',

        // E-commerce & Shopping
        'amazon.com', 'ebay.com', 'etsy.com', 'shopify.com', 'woocommerce.com', 'bigcommerce.com',
        'squarespace.com', 'wix.com', 'weebly.com', 'wordpress.com', 'webflow.com', 'framer.com',
        'bubble.io', 'zapier.com', 'ifttt.com', 'integromat.com', 'automate.io', 'microsoft.com',

        // Learning & Education
        'coursera.org', 'udemy.com', 'edx.org', 'khanacademy.org', 'codecademy.com', 'freecodecamp.org',
        'pluralsight.com', 'lynda.com', 'skillshare.com', 'masterclass.com', 'brilliant.org', 'duolingo.com',
        'babbel.com', 'rosettastone.com', 'memrise.com', 'anki.com', 'quizlet.com', 'kahoot.com',

        // News & Media
        'medium.com', 'substack.com', 'ghost.org', 'hashnode.com', 'dev.to', 'hackernews.com', 'producthunt.com',
        'indiehackers.com', 'betalist.com', 'angellist.com', 'crunchbase.com', 'techcrunch.com', 'theverge.com',
        'wired.com', 'arstechnica.com', 'engadget.com', 'gizmodo.com', 'mashable.com', 'buzzfeed.com',

        // Finance & Trading
        'robinhood.com', 'webull.com', 'etrade.com', 'schwab.com', 'fidelity.com', 'vanguard.com',
        'coinbase.com', 'binance.com', 'kraken.com', 'gemini.com', 'crypto.com', 'blockchain.com',
        'tradingview.com', 'investing.com', 'yahoo.com', 'marketwatch.com', 'bloomberg.com', 'reuters.com',

        // Travel & Maps
        'google.com', 'maps.google.com', 'earth.google.com', 'openstreetmap.org', 'mapbox.com', 'here.com',
        'booking.com', 'expedia.com', 'airbnb.com', 'vrbo.com', 'tripadvisor.com', 'kayak.com', 'skyscanner.com',
        'uber.com', 'lyft.com', 'doordash.com', 'grubhub.com', 'ubereats.com', 'postmates.com',

        // Gaming
        'steam.com', 'epicgames.com', 'origin.com', 'uplay.com', 'battle.net', 'roblox.com', 'minecraft.net',
        'fortnite.com', 'leagueoflegends.com', 'valorant.com', 'overwatch.com', 'hearthstone.com',
        'chess.com', 'lichess.org', 'poker.com', 'zynga.com', 'king.com', 'supercell.com',

        // Health & Fitness
        'myfitnesspal.com', 'fitbit.com', 'strava.com', 'garmin.com', 'nike.com', 'adidas.com',
        'peloton.com', 'headspace.com', 'calm.com', 'meditation.com', 'insight.com', 'ten.com',

        // Real Estate
        'zillow.com', 'realtor.com', 'redfin.com', 'trulia.com', 'apartments.com', 'rent.com',
        'airbnb.com', 'vrbo.com', 'homeaway.com', 'booking.com', 'hotels.com', 'expedia.com',

        // Job Search
        'linkedin.com', 'indeed.com', 'glassdoor.com', 'monster.com', 'careerbuilder.com', 'ziprecruiter.com',
        'dice.com', 'stackoverflow.com', 'angel.co', 'wellfound.com', 'hired.com', 'toptal.com',

        // Cloud Storage & File Sharing
        'drive.google.com', 'onedrive.com', 'dropbox.com', 'box.com', 'icloud.com', 'mega.nz',
        'pcloud.com', 'sync.com', 'tresorit.com', 'spideroak.com', 'backblaze.com', 'carbonite.com',

        // Analytics & Marketing
        'analytics.google.com', 'facebook.com', 'ads.google.com', 'mailchimp.com', 'constantcontact.com',
        'sendinblue.com', 'convertkit.com', 'aweber.com', 'getresponse.com', 'activecampaign.com',

        // CMS & Website Builders
        'wordpress.com', 'wix.com', 'squarespace.com', 'weebly.com', 'webflow.com', 'framer.com',
        'bubble.io', 'carrd.co', 'linktree.com', 'bio.link', 'linktr.ee', 'beacons.ai',

        // API & Development Tools
        'postman.com', 'insomnia.rest', 'swagger.io', 'rapidapi.com', 'apiary.io', 'mockapi.io',
        'jsonplaceholder.typicode.com', 'httpbin.org', 'reqres.in', 'jsonapi.org', 'graphql.org',

        // Security & VPN
        'nordvpn.com', 'expressvpn.com', 'surfshark.com', 'cyberghost.com', 'pia.com', 'protonvpn.com',
        'lastpass.com', '1password.com', 'bitwarden.com', 'dashlane.com', 'keeper.com', 'roboform.com',

        // Monitoring & DevOps
        'datadog.com', 'newrelic.com', 'splunk.com', 'elastic.co', 'grafana.com', 'prometheus.io',
        'sentry.io', 'rollbar.com', 'bugsnag.com', 'honeybadger.io', 'airbrake.io', 'raygun.com',

        // Communication & Email
        'gmail.com', 'outlook.com', 'yahoo.com', 'protonmail.com', 'tutanota.com', 'fastmail.com',
        'zoho.com', 'yandex.com', 'aol.com', 'icloud.com', 'mail.com', 'gmx.com',

        // Forums & Communities
        'stackoverflow.com', 'stackexchange.com', 'quora.com', 'reddit.com', 'discourse.org', 'flarum.org',
        'phpbb.com', 'vbulletin.com', 'invisioncommunity.com', 'xenforo.com', 'vanilla.com', 'nodebb.org',

        // Documentation & Wikis
        'wikipedia.org', 'fandom.com', 'wikia.com', 'confluence.atlassian.com', 'gitbook.com', 'bookstack.org',
        'dokuwiki.org', 'tiddlywiki.com', 'zim-wiki.org', 'trac.edgewall.org', 'redmine.org', 'mantisbt.org',

        // Testing & QA
        'browserstack.com', 'saucelabs.com', 'crossbrowsertesting.com', 'lambdatest.com', 'testingbot.com',
        'selenium.dev', 'cypress.io', 'playwright.dev', 'puppeteer.dev', 'webdriver.io', 'testcafe.io',

        // Database & Backend
        'mongodb.com', 'postgresql.org', 'mysql.com', 'redis.com', 'elasticsearch.co', 'neo4j.com',
        'cassandra.apache.org', 'couchdb.apache.org', 'rethinkdb.com', 'orientdb.org', 'arangodb.com',

        // Hosting & Infrastructure
        'aws.amazon.com', 'console.cloud.google.com', 'portal.azure.com', 'digitalocean.com', 'linode.com',
        'vultr.com', 'hetzner.com', 'ovh.com', 'scaleway.com', 'upcloud.com', 'cloudflare.com',

        // Content Management
        'contentful.com', 'strapi.io', 'sanity.io', 'forestry.io', 'netlify.com', 'gatsby.com',
        'nextjs.org', 'nuxtjs.org', 'svelte.dev', 'vue.js.org', 'react.dev', 'angular.io',

        // E-learning Platforms
        'canvas.instructure.com', 'blackboard.com', 'moodle.org', 'schoology.com', 'edmodo.com', 'classlink.com',
        'google.com', 'classroom.google.com', 'teams.microsoft.com', 'zoom.us', 'webex.com', 'gotomeeting.com',

        // Cryptocurrency & Blockchain
        'etherscan.io', 'bscscan.com', 'polygonscan.com', 'ftmscan.com', 'snowtrace.io', 'arbiscan.io',
        'optimistic.etherscan.io', 'explorer.solana.com', 'cardanoscan.io', 'tzstats.com', 'mintscan.io',

        // IoT & Smart Home
        'home.nest.com', 'alexa.amazon.com', 'smartthings.samsung.com', 'ifttt.com', 'home-assistant.io',
        'hubitat.com', 'wink.com', 'vera.com', 'homeseer.com', 'insteon.com', 'lutron.com',

        // Weather & Environment
        'weather.com', 'accuweather.com', 'weather.gov', 'windy.com', 'weatherunderground.com', 'darksky.net',
        'openweathermap.org', 'climacell.co', 'tomorrow.io', 'weatherapi.com', 'visualcrossing.com',

        // Sports & Entertainment
        'espn.com', 'nfl.com', 'nba.com', 'mlb.com', 'nhl.com', 'fifa.com', 'uefa.com', 'premierleague.com',
        'imdb.com', 'rottentomatoes.com', 'metacritic.com', 'letterboxd.com', 'goodreads.com', 'audible.com',

        // Government & Legal
        'irs.gov', 'ssa.gov', 'usps.com', 'dmv.org', 'courts.gov', 'justice.gov', 'fbi.gov', 'cia.gov',
        'whitehouse.gov', 'congress.gov', 'senate.gov', 'house.gov', 'supremecourt.gov', 'treasury.gov',

        // Scientific & Research
        'arxiv.org', 'pubmed.ncbi.nlm.nih.gov', 'scholar.google.com', 'researchgate.net', 'academia.edu',
        'mendeley.com', 'zotero.org', 'endnote.com', 'refworks.com', 'citeulike.org', 'bibsonomy.org',

        // Art & Culture
        'artstation.com', 'deviantart.com', 'flickr.com', '500px.com', 'unsplash.com', 'pexels.com',
        'shutterstock.com', 'gettyimages.com', 'istockphoto.com', 'adobe.com', 'canva.com', 'crello.com',

        // Additional E-commerce & Marketplaces
        'alibaba.com', 'aliexpress.com', 'wish.com', 'overstock.com', 'wayfair.com', 'target.com',
        'walmart.com', 'bestbuy.com', 'homedepot.com', 'lowes.com', 'costco.com', 'samsclub.com',
        'macys.com', 'nordstrom.com', 'zappos.com', 'chewy.com', 'petco.com', 'petsmart.com',
        'newegg.com', 'tigerdirect.com', 'bhphotovideo.com', 'adorama.com', 'sweetwater.com',
        'guitarcenter.com', 'musiciansfriend.com', 'reverb.com', 'ebay.co.uk', 'amazon.co.uk',
        'amazon.de', 'amazon.fr', 'amazon.it', 'amazon.es', 'amazon.ca', 'amazon.au', 'amazon.jp',

        // International Social Media & Communication
        'weibo.com', 'wechat.com', 'qq.com', 'baidu.com', 'yandex.ru', 'vk.com', 'ok.ru',
        'mail.ru', 'rambler.ru', 'naver.com', 'kakao.com', 'line.me', 'viber.com', 'kik.com',
        'snapchat.com', 'tinder.com', 'bumble.com', 'hinge.co', 'okcupid.com', 'match.com',
        'eharmony.com', 'pof.com', 'zoosk.com', 'badoo.com', 'meetup.com', 'eventbrite.com',

        // Streaming & Entertainment Platforms
        'twitch.tv', 'mixer.com', 'dlive.tv', 'trovo.live', 'facebook.com', 'kick.com',
        'caffeine.tv', 'streamlabs.com', 'obs.live', 'restream.io', 'streamyard.com',
        'riverside.fm', 'anchor.fm', 'buzzsprout.com', 'libsyn.com', 'spreaker.com',
        'podbean.com', 'castbox.fm', 'overcast.fm', 'pocketcasts.com', 'stitcher.com',

        // Music & Audio Platforms
        'bandcamp.com', 'mixcloud.com', 'audiomack.com', 'reverbnation.com', 'last.fm',
        'genius.com', 'musixmatch.com', 'shazam.com', 'songkick.com', 'setlist.fm',
        'discogs.com', 'allmusic.com', 'pitchfork.com', 'rollingstone.com', 'billboard.com',
        'stereogum.com', 'consequence.net', 'spin.com', 'nme.com', 'musicradar.com',

        // Video & Live Streaming
        'ustream.tv', 'livestream.com', 'periscope.tv', 'younow.com', 'bigo.tv',
        'liveme.com', 'streamlabs.com', 'nightbot.com', 'moobot.tv', 'fossabot.com',
        'chatbot.com', 'botisimo.com', 'wizebot.tv', 'deepbot.tv', 'ankhbot.com',

        // Gaming Platforms & Communities
        'itch.io', 'gamejolt.com', 'kongregate.com', 'newgrounds.com', 'armor.com',
        'miniclip.com', 'pogo.com', 'bigfishgames.com', 'wildtangent.com', 'shockwave.com',
        'addictinggames.com', 'coolmathgames.com', 'friv.com', 'kizi.com', 'y8.com',
        'gameforge.com', 'nexon.com', 'ncsoft.com', 'perfectworld.com', 'webzen.com',
        'gamigo.com', 'aeriagames.com', 'ijji.com', 'gpotato.com', 'outspark.com',

        // Developer Tools & Platforms
        'stackoverflow.com', 'stackexchange.com', 'serverfault.com', 'superuser.com',
        'askubuntu.com', 'mathoverflow.net', 'codereview.stackexchange.com', 'programmers.stackexchange.com',
        'softwareengineering.stackexchange.com', 'dba.stackexchange.com', 'unix.stackexchange.com',
        'apple.stackexchange.com', 'android.stackexchange.com', 'gaming.stackexchange.com',
        'scifi.stackexchange.com', 'cooking.stackexchange.com', 'diy.stackexchange.com',

        // Code Repositories & Version Control
        'sourceforge.net', 'launchpad.net', 'codeplex.com', 'googlecode.com', 'assembla.com',
        'unfuddle.com', 'beanstalkapp.com', 'codebasehq.com', 'projectlocker.com', 'repositoryhosting.com',
        'xp-dev.com', 'deveo.com', 'kiln.com', 'planio.com', 'rhodecode.com',

        // Cloud Computing & Infrastructure
        'rackspace.com', 'godaddy.com', 'hostgator.com', 'bluehost.com', 'dreamhost.com',
        'siteground.com', 'a2hosting.com', 'inmotion.com', 'hostmonster.com', 'justhost.com',
        'fatcow.com', 'ipage.com', 'greengeeks.com', 'hostpapa.com', 'namecheap.com',
        'hover.com', 'gandi.net', 'enom.com', 'networksolutions.com', 'register.com',

        // Content Delivery & CDN
        'maxcdn.com', 'keycdn.com', 'bunnycdn.com', 'stackpath.com', 'fastly.com',
        'jsdelivr.com', 'unpkg.com', 'cdnjs.com', 'bootstrapcdn.com', 'fontawesome.com',
        'googlefonts.com', 'typekit.com', 'fonts.com', 'myfonts.com', 'fontspring.com',

        // Database & Backend Services
        'fauna.com', 'cockroachdb.com', 'yugabyte.com', 'timescale.com', 'influxdata.com',
        'questdb.io', 'clickhouse.tech', 'apache.org', 'mariadb.org', 'percona.com',
        'enterprisedb.com', 'citusdata.com', 'greenplum.org', 'vertica.com', 'snowflake.com',

        // API & Integration Platforms
        'mulesoft.com', 'apigee.com', 'kong.com', 'tyk.io', 'ambassador.com',
        'getambassador.io', 'istio.io', 'linkerd.io', 'consul.io', 'vault.io',
        'terraform.io', 'packer.io', 'vagrant.com', 'docker.com', 'kubernetes.io',

        // Monitoring & Analytics
        'mixpanel.com', 'amplitude.com', 'segment.com', 'fullstory.com', 'hotjar.com',
        'crazyegg.com', 'optimizely.com', 'vwo.com', 'unbounce.com', 'leadpages.com',
        'clickfunnels.com', 'kartra.com', 'builderall.com', 'systeme.io', 'getresponse.com',

        // Email Marketing & Automation
        'drip.com', 'klaviyo.com', 'omnisend.com', 'campaignmonitor.com', 'emma.com',
        'constantcontact.com', 'icontact.com', 'verticalresponse.com', 'benchmark.com', 'getresponse.com',
        'aweber.com', 'mailerlite.com', 'moosend.com', 'sendy.co', 'phplist.org',

        // Customer Support & Help Desk
        'freshdesk.com', 'helpscout.com', 'kayako.com', 'desk.com', 'uservoice.com',
        'groove.com', 'helpshift.com', 'livechat.com', 'olark.com', 'tawk.to',
        'crisp.chat', 'drift.com', 'intercom.com', 'zendesk.com', 'freshworks.com',

        // Project Management & Collaboration
        'notion.so', 'coda.io', 'airtable.com', 'smartsheet.com', 'monday.com',
        'clickup.com', 'asana.com', 'trello.com', 'basecamp.com', 'wrike.com',
        'teamwork.com', 'workfront.com', 'clarizen.com', 'liquidplanner.com', 'projectplace.com',

        // Design & Prototyping Tools
        'sketch.com', 'figma.com', 'adobe.com', 'canva.com', 'invisionapp.com',
        'marvelapp.com', 'proto.io', 'justinmind.com', 'axure.com', 'balsamiq.com',
        'mockplus.com', 'flinto.com', 'principle.design', 'framer.com', 'origami.design',

        // Website Builders & CMS
        'wordpress.org', 'wordpress.com', 'wix.com', 'squarespace.com', 'weebly.com',
        'webflow.com', 'bubble.io', 'carrd.co', 'strikingly.com', 'jimdo.com',
        'site123.com', 'zyro.com', 'godaddy.com', 'ionos.com', 'hostinger.com',

        // E-learning & Online Education
        'udacity.com', 'coursera.org', 'edx.org', 'futurelearn.com', 'swayam.gov.in',
        'nptel.ac.in', 'mitopencourseware.org', 'ocw.mit.edu', 'stanford.edu', 'harvard.edu',
        'yale.edu', 'princeton.edu', 'columbia.edu', 'upenn.edu', 'dartmouth.edu',

        // Language Learning
        'duolingo.com', 'babbel.com', 'rosettastone.com', 'busuu.com', 'lingoda.com',
        'italki.com', 'preply.com', 'cambly.com', 'verbling.com', 'rype.com',
        'fluentu.com', 'lingvist.com', 'mondly.com', 'memrise.com', 'anki.com',

        // Skill Development & Training
        'skillshare.com', 'masterclass.com', 'linkedin.com', 'pluralsight.com', 'treehouse.com',
        'codecademy.com', 'freecodecamp.org', 'khanacademy.org', 'brilliant.org', 'datacamp.com',
        'coursera.org', 'udemy.com', 'edx.org', 'futurelearn.com', 'mindvalley.com',

        // News & Media Platforms
        'cnn.com', 'bbc.com', 'reuters.com', 'ap.org', 'npr.org',
        'pbs.org', 'cbsnews.com', 'abcnews.go.com', 'nbcnews.com', 'foxnews.com',
        'msnbc.com', 'usatoday.com', 'wsj.com', 'nytimes.com', 'washingtonpost.com',

        // International News
        'aljazeera.com', 'rt.com', 'dw.com', 'france24.com', 'euronews.com',
        'sputniknews.com', 'xinhuanet.com', 'globaltimes.cn', 'japantimes.co.jp', 'koreatimes.co.kr',
        'timesofindia.com', 'hindustantimes.com', 'thehindu.com', 'dawn.com', 'arabnews.com',

        // Technology News & Blogs
        'techcrunch.com', 'theverge.com', 'wired.com', 'arstechnica.com', 'engadget.com',
        'gizmodo.com', 'mashable.com', 'recode.net', 'venturebeat.com', 'readwrite.com',
        'gigaom.com', 'pandodaily.com', 'allthingsd.com', 'techmeme.com', 'slashdot.org',

        // Business & Finance News
        'bloomberg.com', 'cnbc.com', 'marketwatch.com', 'fool.com', 'seekingalpha.com',
        'morningstar.com', 'barrons.com', 'forbes.com', 'fortune.com', 'businessinsider.com',
        'fastcompany.com', 'inc.com', 'entrepreneur.com', 'harvard.edu', 'wharton.upenn.edu',

        // Scientific & Academic Journals
        'nature.com', 'science.org', 'cell.com', 'nejm.org', 'thelancet.com',
        'bmj.com', 'jama.jamanetwork.com', 'plos.org', 'frontiersin.org', 'mdpi.com',
        'springer.com', 'elsevier.com', 'wiley.com', 'taylor.com', 'sage.com',

        // Research & Academic Platforms
        'researchgate.net', 'academia.edu', 'mendeley.com', 'zotero.org', 'orcid.org',
        'publons.com', 'scopus.com', 'webofscience.com', 'googlescholar.com', 'semanticscholar.org',
        'arxiv.org', 'biorxiv.org', 'medrxiv.org', 'psyarxiv.com', 'socarxiv.org',

        // Government & Public Services
        'usa.gov', 'whitehouse.gov', 'congress.gov', 'senate.gov', 'house.gov',
        'supremecourt.gov', 'irs.gov', 'ssa.gov', 'medicare.gov', 'medicaid.gov',
        'va.gov', 'dol.gov', 'ed.gov', 'hhs.gov', 'dhs.gov',

        // International Government Sites
        'gov.uk', 'canada.ca', 'australia.gov.au', 'germany.travel', 'france.fr',
        'italy.it', 'spain.info', 'japan.go.jp', 'korea.kr', 'india.gov.in',
        'china.org.cn', 'russia.ru', 'brazil.gov.br', 'mexico.gob.mx', 'argentina.gob.ar',

        // Legal & Law Resources
        'justia.com', 'findlaw.com', 'martindale.com', 'avvo.com', 'lawyers.com',
        'nolo.com', 'legalzoom.com', 'rocketlawyer.com', 'lawdepot.com', 'uslegal.com',
        'law.com', 'americanbar.org', 'abajournal.com', 'lawreview.org', 'scotusblog.com',

        // Real Estate & Property
        'zillow.com', 'realtor.com', 'redfin.com', 'trulia.com', 'homes.com',
        'homefinder.com', 'move.com', 'rent.com', 'apartments.com', 'apartmentlist.com',
        'padmapper.com', 'hotpads.com', 'rentals.com', 'forrent.com', 'apartmentguide.com',

        // Travel & Tourism
        'expedia.com', 'booking.com', 'priceline.com', 'kayak.com', 'orbitz.com',
        'travelocity.com', 'cheaptickets.com', 'onetravel.com', 'momondo.com', 'skyscanner.com',
        'tripadvisor.com', 'yelp.com', 'foursquare.com', 'zomato.com', 'opentable.com',

        // Food & Restaurant Platforms
        'grubhub.com', 'doordash.com', 'ubereats.com', 'postmates.com', 'seamless.com',
        'caviar.com', 'eat24.com', 'delivery.com', 'foodpanda.com', 'justeat.com',
        'deliveroo.com', 'swiggy.com', 'zomato.com', 'yelp.com', 'opentable.com',

        // Health & Medical Platforms
        'webmd.com', 'mayoclinic.org', 'healthline.com', 'medlineplus.gov', 'nih.gov',
        'cdc.gov', 'who.int', 'fda.gov', 'drugs.com', 'rxlist.com',
        'medscape.com', 'uptodate.com', 'pubmed.ncbi.nlm.nih.gov', 'cochrane.org', 'bmj.com',

        // Fitness & Wellness
        'myfitnesspal.com', 'loseit.com', 'cronometer.com', 'fitbit.com', 'garmin.com',
        'strava.com', 'runkeeper.com', 'mapmyrun.com', 'endomondo.com', 'runtastic.com',
        'nike.com', 'adidas.com', 'underarmour.com', 'lululemon.com', 'peloton.com',

        // Mental Health & Meditation
        'headspace.com', 'calm.com', 'insight.com', 'ten.com', 'waking.com',
        'meditation.com', 'mindfulness.com', 'buddhify.com', 'stopbreathethink.com', 'smilingmind.com.au',
        'betterhelp.com', 'talkspace.com', 'cerebral.com', 'mdlive.com', 'amwell.com',

        // Dating & Relationships
        'match.com', 'eharmony.com', 'okcupid.com', 'pof.com', 'zoosk.com',
        'tinder.com', 'bumble.com', 'hinge.co', 'coffeemeetsbagel.com', 'happn.com',
        'badoo.com', 'meetme.com', 'skout.com', 'tagged.com', 'mingle2.com',

        // Photography & Visual Content
        'instagram.com', 'pinterest.com', 'flickr.com', '500px.com', 'smugmug.com',
        'photobucket.com', 'imgur.com', 'giphy.com', 'tenor.com', 'gfycat.com',
        'unsplash.com', 'pexels.com', 'pixabay.com', 'freepik.com', 'shutterstock.com',

        // Video Creation & Editing
        'youtube.com', 'vimeo.com', 'wistia.com', 'brightcove.com', 'jwplayer.com',
        'vidyard.com', 'loom.com', 'screencast.com', 'camtasia.com', 'snagit.com',
        'obs.live', 'streamlabs.com', 'xsplit.com', 'nvidia.com', 'amd.com',

        // Productivity & Time Management
        'todoist.com', 'any.do', 'wunderlist.com', 'remember.com', 'evernote.com',
        'onenote.com', 'simplenote.com', 'bear.app', 'ulysses.app', 'ia.net',
        'typora.io', 'marktext.app', 'zettlr.com', 'obsidian.md', 'roamresearch.com',

        // File Storage & Sync
        'dropbox.com', 'box.com', 'onedrive.com', 'googledrive.com', 'icloud.com',
        'mega.nz', 'pcloud.com', 'sync.com', 'tresorit.com', 'spideroak.com',
        'backblaze.com', 'carbonite.com', 'crashplan.com', 'mozy.com', 'sugarsync.com',

        // Communication & Messaging
        'slack.com', 'discord.com', 'teams.microsoft.com', 'zoom.us', 'skype.com',
        'telegram.org', 'whatsapp.com', 'signal.org', 'wickr.com', 'threema.ch',
        'wire.com', 'element.io', 'riot.im', 'keybase.io', 'session.com'
    ];

    const isSPASite = spaSites.some(site => window.location.hostname.includes(site));

    if (!isSPASite) {
        return;
    }

    console.log(`Stickara: Setting up note URL update handling for SPA site: ${window.location.hostname}`);

    // Store original pushState and replaceState
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    // Override pushState
    history.pushState = function() {
        originalPushState.apply(history, arguments);
        handleNoteURLChange();
    };

    // Override replaceState
    history.replaceState = function() {
        originalReplaceState.apply(history, arguments);
        handleNoteURLChange();
    };

    // Handle popstate events
    window.addEventListener('popstate', handleNoteURLChange);

    // Handle hash changes (for hash-based routing)
    window.addEventListener('hashchange', handleNoteURLChange);

    // Handle page visibility changes (for PWA/service worker navigation)
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
            // Page became visible, check if URL changed while hidden
            setTimeout(handleNoteURLChange, 100);
        }
    });

    // Handle focus events (for multi-tab/window scenarios)
    window.addEventListener('focus', () => {
        setTimeout(handleNoteURLChange, 100);
    });

    // Site-specific navigation detection
    if (window.location.hostname.includes('youtube.com')) {
        // Listen for YouTube's custom navigation events
        document.addEventListener('yt-navigate-start', handleNoteURLChange);
        document.addEventListener('yt-navigate-finish', handleNoteURLChange);

        // Also monitor for changes in the video ID in the URL
        let lastVideoId = extractYouTubeVideoId(window.location.href);
        const checkVideoIdChange = () => {
            const currentVideoId = extractYouTubeVideoId(window.location.href);
            if (currentVideoId && currentVideoId !== lastVideoId) {
                lastVideoId = currentVideoId;
                handleNoteURLChange();
            }
        };

        // Check for video ID changes periodically (as a fallback)
        setInterval(checkVideoIdChange, 1000);
    } else if (window.location.hostname.includes('chatgpt.com') || window.location.hostname.includes('chat.openai.com')) {
        // ChatGPT-specific URL monitoring
        let lastChatUrl = window.location.href;
        const checkChatUrlChange = () => {
            const currentUrl = window.location.href;
            if (currentUrl !== lastChatUrl) {
                console.log(`Stickara: ChatGPT URL changed from ${lastChatUrl} to ${currentUrl}`);
                lastChatUrl = currentUrl;
                handleNoteURLChange();
            }
        };

        // Check for URL changes more frequently for ChatGPT
        setInterval(checkChatUrlChange, 500);

        // Also listen for DOM changes that might indicate navigation
        const observer = new MutationObserver(() => {
            checkChatUrlChange();
        });

        // Observe changes to the main content area
        const mainContent = document.querySelector('main') || document.body;
        if (mainContent) {
            observer.observe(mainContent, {
                childList: true,
                subtree: true,
                attributes: false
            });
        }
    }

    // Enhanced navigation detection for various scenarios
    setupEnhancedNavigationDetection();

    function handleNoteURLChange() {
        setTimeout(() => {
            console.log("Stickara: Note URL change detected");
            if (typeof updateNoteURLContext === 'function') {
                updateNoteURLContext();
            }
        }, 100);
    }

    /**
     * Sets up enhanced navigation detection for various edge cases
     */
    function setupEnhancedNavigationDetection() {
        // 1. Monitor iframe navigation (for iframe-heavy sites)
        monitorIframeNavigation();

        // 2. Monitor WebSocket connections (for real-time apps)
        monitorWebSocketActivity();

        // 3. Monitor service worker messages (for PWAs)
        monitorServiceWorkerNavigation();

        // 4. Monitor DOM title changes (fallback detection)
        monitorTitleChanges();

        // 5. Monitor meta tag changes (for dynamic metadata)
        monitorMetaChanges();
    }

    /**
     * Monitors iframe navigation changes
     */
    function monitorIframeNavigation() {
        // Watch for new iframes being added
        const iframeObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'IFRAME') {
                        try {
                            // Try to monitor iframe URL changes (if same-origin)
                            node.addEventListener('load', () => {
                                try {
                                    const iframeUrl = node.contentWindow?.location?.href;
                                    if (iframeUrl && iframeUrl !== 'about:blank') {
                                        console.log(`Stickara: Iframe navigation detected: ${iframeUrl}`);
                                        // For iframe navigation, we might want to update context
                                        // but keep the main page URL as primary
                                        setTimeout(handleNoteURLChange, 200);
                                    }
                                } catch (e) {
                                    // Cross-origin iframe, can't access URL
                                    console.log("Stickara: Cross-origin iframe detected, monitoring main page only");
                                }
                            });
                        } catch (e) {
                            console.log("Stickara: Error setting up iframe monitoring:", e);
                        }
                    }
                });
            });
        });

        iframeObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /**
     * Monitors WebSocket activity that might indicate navigation
     */
    function monitorWebSocketActivity() {
        // Override WebSocket constructor to monitor connections
        const originalWebSocket = window.WebSocket;
        if (originalWebSocket) {
            window.WebSocket = function(url, protocols) {
                const ws = new originalWebSocket(url, protocols);

                // Monitor messages that might indicate navigation
                ws.addEventListener('message', (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        // Look for navigation-related messages
                        if (data && (data.type === 'navigate' || data.type === 'route' || data.url || data.path)) {
                            console.log("Stickara: WebSocket navigation message detected");
                            setTimeout(handleNoteURLChange, 300);
                        }
                    } catch (e) {
                        // Not JSON or not navigation-related
                    }
                });

                return ws;
            };

            // Copy static properties
            Object.setPrototypeOf(window.WebSocket, originalWebSocket);
            Object.defineProperty(window.WebSocket, 'prototype', {
                value: originalWebSocket.prototype,
                writable: false
            });
        }
    }

    /**
     * Monitors service worker messages for navigation
     */
    function monitorServiceWorkerNavigation() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                if (event.data && (event.data.type === 'navigate' || event.data.type === 'route')) {
                    console.log("Stickara: Service worker navigation detected");
                    setTimeout(handleNoteURLChange, 200);
                }
            });
        }
    }

    /**
     * Monitors document title changes as fallback navigation detection
     */
    function monitorTitleChanges() {
        let lastTitle = document.title;

        const titleObserver = new MutationObserver(() => {
            if (document.title !== lastTitle) {
                console.log(`Stickara: Title change detected: "${lastTitle}" → "${document.title}"`);
                lastTitle = document.title;
                // Title changes often indicate navigation in SPAs
                setTimeout(handleNoteURLChange, 150);
            }
        });

        titleObserver.observe(document.querySelector('title') || document.head, {
            childList: true,
            characterData: true,
            subtree: true
        });
    }

    /**
     * Monitors meta tag changes for navigation detection
     */
    function monitorMetaChanges() {
        const metaObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                // Check for changes to canonical URL or og:url meta tags
                if (mutation.target.tagName === 'META') {
                    const name = mutation.target.getAttribute('name') || mutation.target.getAttribute('property');
                    if (name === 'canonical' || name === 'og:url' || name === 'twitter:url') {
                        console.log("Stickara: Meta URL change detected");
                        setTimeout(handleNoteURLChange, 200);
                    }
                }
            });
        });

        metaObserver.observe(document.head, {
            childList: true,
            attributes: true,
            attributeFilter: ['content', 'href'],
            subtree: true
        });
    }

    console.log("Stickara: Note URL update handling set up successfully");
}

/**
 * Extracts the YouTube video ID from a URL
 * @param {string} url - The YouTube URL
 * @returns {string|null} - The video ID or null if not found
 */
function extractYouTubeVideoId(url) {
    if (!url) return null;

    try {
        const urlObj = new URL(url);

        // Handle different YouTube URL formats
        if (urlObj.hostname.includes('youtube.com')) {
            // Standard watch URL: https://www.youtube.com/watch?v=VIDEO_ID
            if (urlObj.pathname === '/watch') {
                return urlObj.searchParams.get('v');
            }
            // Embed URL: https://www.youtube.com/embed/VIDEO_ID
            if (urlObj.pathname.startsWith('/embed/')) {
                return urlObj.pathname.split('/embed/')[1];
            }
        } else if (urlObj.hostname === 'youtu.be') {
            // Short URL: https://youtu.be/VIDEO_ID
            return urlObj.pathname.substring(1);
        }
    } catch (e) {
        console.warn('Stickara: Error parsing YouTube URL:', e);
    }

    return null;
}

console.log("Stickara: State Initialized (with init flag and noteTitleInput)"); // Updated log
// --- END OF FILE content-state.js ---