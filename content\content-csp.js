// --- START OF FILE content-csp.js ---

/**
 * Content Security Policy (CSP) Implementation for Stickara Content Scripts
 *
 * This module dynamically applies CSP protections to Stickara's injected content
 * by creating a sandboxed environment for user-generated content and enforcing
 * strict security policies.
 */

// Create a namespace to avoid global pollution
window.StickaraCSP = (function() {

    /**
     * CSP policy directives for Stickara content
     */
    const CSP_DIRECTIVES = {
        'default-src': ["'none'"],
        'script-src': ["'self'", "'unsafe-inline'"], // We need unsafe-inline for content scripts
        'style-src': ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],  // Allow Google Fonts
        'img-src': ["'self'", "data:", "https://*", "file://*"],    // Allow data:, https: and file: for images
        'font-src': ["'self'", "https://fonts.gstatic.com"],  // Allow Google Fonts
        'connect-src': ["'self'", "https://*.googleapis.com", "https://*"],  // Allow external connections
        'frame-src': ["'none'"],
        'object-src': ["'none'"],
        'base-uri': ["'none'"],
        'form-action': ["'none'"],
        'frame-ancestors': ["'none'"],
        'worker-src': ["'self'", "blob:"],
        'manifest-src': ["'none'"],
        'media-src': ["'none'"],
        'prefetch-src': ["'none'"],
        'child-src': ["'none'"],
        'upgrade-insecure-requests': []
    };

    /**
     * Builds a CSP policy string from the directives
     * @returns {string} The CSP policy string
     */
    function buildCSPString() {
        return Object.entries(CSP_DIRECTIVES)
            .map(([directive, sources]) => {
                if (sources.length === 0) {
                    return directive;
                }
                return `${directive} ${sources.join(' ')}`;
            })
            .join('; ');
    }

    /**
     * Applies CSP to a sandbox iframe
     * @param {HTMLIFrameElement} iframe - The iframe to apply CSP to
     */
    function applyCSPToIframe(iframe) {
        try {
            // Set sandbox attribute to restrict capabilities
            iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin');

            // Apply CSP to iframe content
            iframe.addEventListener('load', () => {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

                    // Create a meta tag with CSP
                    const meta = iframeDoc.createElement('meta');
                    meta.setAttribute('http-equiv', 'Content-Security-Policy');
                    meta.setAttribute('content', buildCSPString());

                    // Add it to the iframe's head
                    if (iframeDoc.head) {
                        iframeDoc.head.appendChild(meta);
                        console.log("Stickara: Applied CSP to sandbox iframe");
                    }
                } catch (e) {
                    console.error("Stickara: Error applying CSP to iframe:", e);
                }
            });
        } catch (e) {
            console.error("Stickara: Error setting up iframe CSP:", e);
        }
    }

    /**
     * Creates a sandboxed iframe for rendering user content
     * @param {string} content - The HTML content to render
     * @param {Object} styles - Optional CSS styles for the iframe
     * @returns {HTMLIFrameElement} The sandboxed iframe
     */
    function createSecureSandbox(content, styles = {}) {
        // Create a sandboxed iframe
        const iframe = document.createElement('iframe');

        // Apply sandbox restrictions
        iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin');

        // Apply styles
        iframe.style.border = 'none';
        iframe.style.background = 'transparent';
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.overflow = 'hidden';

        // Apply custom styles
        Object.entries(styles).forEach(([prop, value]) => {
            iframe.style[prop] = value;
        });

        // Append to document to initialize
        document.body.appendChild(iframe);

        // Get iframe document
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

        // Apply CSP
        const meta = iframeDoc.createElement('meta');
        meta.setAttribute('http-equiv', 'Content-Security-Policy');
        meta.setAttribute('content', buildCSPString());

        // Create HTML structure
        iframeDoc.open();
        iframeDoc.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body {
                        margin: 0;
                        padding: 0;
                        font-family: Arial, sans-serif;
                        overflow: hidden;
                    }
                    .content-container {
                        padding: 8px;
                    }
                </style>
            </head>
            <body>
                <div class="content-container">${content}</div>
                <script>
                    // Restrict dangerous APIs
                    window.eval = function() { throw new Error('Eval is disabled in Stickara sandbox'); };

                    // Communicate iframe height to parent
                    function updateHeight() {
                        const height = document.body.scrollHeight;
                        window.parent.postMessage({ type: 'resize', height: height }, '*');
                    }

                    // Update height on load and on resize
                    window.addEventListener('load', updateHeight);
                    window.addEventListener('resize', updateHeight);

                    // Intercept all link clicks
                    document.addEventListener('click', function(e) {
                        if (e.target.tagName === 'A') {
                            e.preventDefault();
                            window.parent.postMessage({
                                type: 'link',
                                href: e.target.href
                            }, '*');
                        }
                    });
                </script>
            </body>
            </html>
        `);
        iframeDoc.close();

        return iframe;
    }

    /**
     * Renders HTML content securely in a container
     * @param {HTMLElement} container - The container to render in
     * @param {string} content - The HTML content to render
     */
    function renderSecureContent(container, content) {
        // Clear the container
        while (container.firstChild) {
            container.removeChild(container.firstChild);
        }

        // Create a secure sandbox
        const sandbox = createSecureSandbox(content, {
            width: '100%',
            height: '100%'
        });

        // Add message listener for iframe communication
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'resize') {
                sandbox.style.height = event.data.height + 'px';
            } else if (event.data && event.data.type === 'link') {
                // Handle link clicks securely
                if (window.StickaraSecureURL && typeof window.StickaraSecureURL.isURLSafe === 'function') {
                    const url = event.data.href;
                    if (window.StickaraSecureURL.isURLSafe(url)) {
                        // Open link in a new tab if it's safe
                        window.open(url, '_blank', 'noopener,noreferrer');
                    } else {
                        console.warn("Stickara: Blocked navigation to unsafe URL:", url);
                    }
                }
            }
        });

        // Add the sandbox to the container
        container.appendChild(sandbox);
    }

    /**
     * Applies CSP to a specific element by creating a shadow DOM
     * @param {HTMLElement} element - The element to apply CSP to
     * @param {string} content - The HTML content to render
     */
    function applyCSPToElement(element, content) {
        try {
            // Create a shadow DOM
            const shadow = element.attachShadow({ mode: 'closed' });

            // Create a style element for the shadow DOM
            const style = document.createElement('style');
            style.textContent = `
                :host {
                    all: initial;
                    display: block;
                    contain: content;
                }
                .shadow-container {
                    font-family: Arial, sans-serif;
                    color: inherit;
                    padding: 8px;
                }
            `;

            // Create a container for the content
            const container = document.createElement('div');
            container.className = 'shadow-container';

            // Use our secure HTML rendering function
            if (typeof window.createSafeHTML === 'function') {
                const safeFragment = window.createSafeHTML(content);
                container.appendChild(safeFragment);
            } else {
                // Fallback if security utils aren't available
                container.textContent = content;
            }

            // Add style and container to shadow DOM
            shadow.appendChild(style);
            shadow.appendChild(container);

            console.log("Stickara: Applied CSP to element using shadow DOM");
        } catch (e) {
            console.error("Stickara: Error applying CSP to element:", e);

            // Fallback to secure text rendering
            element.textContent = content;
        }
    }

    // Return the public API
    return {
        applyCSPToIframe,
        createSecureSandbox,
        renderSecureContent,
        applyCSPToElement,
        buildCSPString
    };
})();

console.log("Stickara: Content Security Policy Module Loaded");
// --- END OF FILE content-csp.js ---
