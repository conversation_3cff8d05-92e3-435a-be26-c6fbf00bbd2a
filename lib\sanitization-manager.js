/**
 * Sanitization Manager
 * Manages HTML sanitization using DOMPurify
 */

// Create a namespace to avoid global pollution
window.StickaraSanitizationManager = (function() {
    /**
     * Sanitizes HTML content using DOMPurify
     * @param {string} html - The HTML content to sanitize
     * @returns {string} The sanitized HTML
     */
    function sanitize(html) {
        // If HTML is empty, return empty string
        if (!html) {
            return '';
        }

        return fallbackSanitize(html);
    }

    /**
     * Fallback sanitization function if the worker is not available
     * @param {string} html - The HTML content to sanitize
     * @returns {string} The sanitized HTML
     */
    function fallbackSanitize(html) {
        // If HTML is empty, return empty string
        if (!html) {
            return '';
        }

        try {
            // Use DOMPurify if available (preferred method)
            if (window.DOMPurify) {
                return window.DOMPurify.sanitize(html);
            }

            // Use StickaraDOMPurify if available
            if (window.StickaraDOMPurify) {
                return window.StickaraDOMPurify.sanitize(html);
            }

            // Use createSafeHTML if available
            if (typeof createSafeHTML === 'function') {
                try {
                    const fragment = createSafeHTML(html);
                    const div = document.createElement('div');
                    div.appendChild(fragment);
                    return div.innerHTML;
                } catch (e) {
                    console.error('Stickara: Error using createSafeHTML:', e);
                    // Continue to fallback
                }
            }
        } catch (e) {
            console.error('Stickara: Error in primary sanitization methods:', e);
            // Continue to fallback
        }

        // Simple sanitization using regex as a last resort
        console.log('Stickara: Using regex-based sanitization as fallback');
        return String(html)
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
            .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
            .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
            .replace(/javascript:/gi, 'removed:')
            .replace(/on\w+(\s*)=/gi, 'data-removed$1=');
    }

    // Return the public API
    return {
        sanitize,
        fallbackSanitize
    };
})();

console.log('Stickara: Sanitization Manager Loaded');
