/**
 * Stickara Database Error Handler
 * Provides robust error handling and recovery mechanisms for IndexedDB operations
 */

// Create a namespace to avoid global pollution
window.StickaraDBErrorHandler = (function() {
    // Error categories
    const ERROR_CATEGORIES = {
        CONNECTION: 'connection',       // Database connection errors
        TRANSACTION: 'transaction',     // Transaction errors
        QUOTA: 'quota',                 // Storage quota exceeded
        VERSION: 'version',             // Version change errors
        CONSTRAINT: 'constraint',       // Constraint violations
        UNKNOWN: 'unknown'              // Unknown errors
    };

    // Error recovery strategies
    const RECOVERY_STRATEGIES = {
        RETRY: 'retry',                 // Retry the operation
        FALLBACK: 'fallback',           // Use fallback storage
        CLEAR_DATA: 'clear_data',       // Clear some data to free space
        REBUILD: 'rebuild',             // Rebuild the database
        NONE: 'none'                    // No recovery possible
    };

    // Configuration
    const config = {
        maxRetries: 3,                  // Maximum number of retry attempts
        retryDelay: 1000,               // Delay between retries (ms)
        exponentialBackoff: true,       // Use exponential backoff for retries
        autoRecovery: true,             // Attempt automatic recovery
        quotaManagement: true,          // Manage storage quota
        debug: false                    // Debug mode
    };

    // Private variables
    const errorLog = [];                // Log of recent errors
    const retryCounters = {};           // Track retry attempts: { operationId: count }
    let recoveryInProgress = false;     // Flag to prevent multiple recovery attempts

    /**
     * Log messages when debug mode is enabled
     * @param {...any} args - Arguments to log
     */
    function debugLog(...args) {
        if (config.debug) {
            console.log('[StickaraDBErrorHandler]', ...args);
        }
    }

    /**
     * Categorizes an IndexedDB error
     * @param {Error} error - The error to categorize
     * @returns {string} The error category
     */
    function categorizeError(error) {
        if (!error) return ERROR_CATEGORIES.UNKNOWN;

        const errorName = error.name || '';
        const errorMessage = error.message || '';
        
        // Check for connection errors
        if (errorName === 'InvalidStateError' || 
            errorMessage.includes('connection') || 
            errorMessage.includes('open')) {
            return ERROR_CATEGORIES.CONNECTION;
        }
        
        // Check for transaction errors
        if (errorName === 'TransactionInactiveError' || 
            errorMessage.includes('transaction')) {
            return ERROR_CATEGORIES.TRANSACTION;
        }
        
        // Check for quota errors
        if (errorName === 'QuotaExceededError' || 
            errorMessage.includes('quota') || 
            errorMessage.includes('storage') || 
            errorMessage.includes('full')) {
            return ERROR_CATEGORIES.QUOTA;
        }
        
        // Check for version errors
        if (errorName === 'VersionError' || 
            errorMessage.includes('version')) {
            return ERROR_CATEGORIES.VERSION;
        }
        
        // Check for constraint errors
        if (errorName === 'ConstraintError' || 
            errorMessage.includes('constraint') || 
            errorMessage.includes('duplicate')) {
            return ERROR_CATEGORIES.CONSTRAINT;
        }
        
        return ERROR_CATEGORIES.UNKNOWN;
    }

    /**
     * Determines the appropriate recovery strategy for an error
     * @param {Error} error - The error to handle
     * @param {string} operation - The operation that caused the error
     * @returns {string} The recovery strategy
     */
    function determineRecoveryStrategy(error, operation) {
        const category = categorizeError(error);
        
        switch (category) {
            case ERROR_CATEGORIES.CONNECTION:
                return RECOVERY_STRATEGIES.RETRY;
                
            case ERROR_CATEGORIES.TRANSACTION:
                return RECOVERY_STRATEGIES.RETRY;
                
            case ERROR_CATEGORIES.QUOTA:
                return RECOVERY_STRATEGIES.CLEAR_DATA;
                
            case ERROR_CATEGORIES.VERSION:
                return RECOVERY_STRATEGIES.REBUILD;
                
            case ERROR_CATEGORIES.CONSTRAINT:
                return RECOVERY_STRATEGIES.NONE;
                
            default:
                return RECOVERY_STRATEGIES.FALLBACK;
        }
    }

    /**
     * Handles an IndexedDB error
     * @param {Error} error - The error to handle
     * @param {Object} options - Error handling options
     * @returns {Promise<Object>} A promise that resolves with the handling result
     */
    async function handleError(error, options = {}) {
        const {
            operation = 'unknown',
            operationId = `op-${Date.now()}`,
            data = null,
            context = {}
        } = options;
        
        // Log the error
        const errorInfo = {
            timestamp: Date.now(),
            error: {
                name: error.name,
                message: error.message,
                stack: error.stack
            },
            category: categorizeError(error),
            operation,
            operationId,
            context
        };
        
        errorLog.unshift(errorInfo);
        if (errorLog.length > 50) errorLog.pop(); // Keep only the last 50 errors
        
        debugLog(`Error in operation ${operation}:`, error.message);
        
        // Determine recovery strategy
        const strategy = determineRecoveryStrategy(error, operation);
        debugLog(`Recovery strategy for ${operation}: ${strategy}`);
        
        // Handle based on strategy
        let result = { success: false, strategy, error: errorInfo };
        
        if (config.autoRecovery) {
            switch (strategy) {
                case RECOVERY_STRATEGIES.RETRY:
                    result = await handleRetry(operationId, operation, data, context);
                    break;
                    
                case RECOVERY_STRATEGIES.FALLBACK:
                    result = await handleFallback(operation, data, context);
                    break;
                    
                case RECOVERY_STRATEGIES.CLEAR_DATA:
                    result = await handleQuotaExceeded();
                    break;
                    
                case RECOVERY_STRATEGIES.REBUILD:
                    result = await handleRebuild();
                    break;
                    
                default:
                    // No automatic recovery possible
                    break;
            }
        }
        
        return result;
    }

    /**
     * Handles retry strategy
     * @param {string} operationId - Unique ID for the operation
     * @param {string} operation - The operation to retry
     * @param {any} data - The data for the operation
     * @param {Object} context - Additional context
     * @returns {Promise<Object>} A promise that resolves with the retry result
     */
    async function handleRetry(operationId, operation, data, context) {
        // Check if we've exceeded the maximum retries
        const retryCount = retryCounters[operationId] || 0;
        if (retryCount >= config.maxRetries) {
            debugLog(`Maximum retries exceeded for ${operation}`);
            return { 
                success: false, 
                strategy: RECOVERY_STRATEGIES.RETRY,
                error: 'Maximum retries exceeded'
            };
        }
        
        // Increment retry counter
        retryCounters[operationId] = retryCount + 1;
        
        // Calculate delay with exponential backoff if enabled
        let delay = config.retryDelay;
        if (config.exponentialBackoff) {
            delay = config.retryDelay * Math.pow(2, retryCount);
        }
        
        debugLog(`Retrying ${operation} in ${delay}ms (attempt ${retryCount + 1}/${config.maxRetries})`);
        
        // Return a result indicating retry
        return { 
            success: true, 
            strategy: RECOVERY_STRATEGIES.RETRY,
            retryCount: retryCount + 1,
            delay,
            operationId
        };
    }

    /**
     * Handles fallback strategy
     * @param {string} operation - The operation that failed
     * @param {any} data - The data for the operation
     * @param {Object} context - Additional context
     * @returns {Promise<Object>} A promise that resolves with the fallback result
     */
    async function handleFallback(operation, data, context) {
        debugLog(`Using fallback storage for ${operation}`);
        
        // Return a result indicating fallback
        return { 
            success: true, 
            strategy: RECOVERY_STRATEGIES.FALLBACK,
            operation,
            useFallback: true
        };
    }

    /**
     * Handles quota exceeded errors
     * @returns {Promise<Object>} A promise that resolves with the handling result
     */
    async function handleQuotaExceeded() {
        if (recoveryInProgress) {
            return { 
                success: false, 
                strategy: RECOVERY_STRATEGIES.CLEAR_DATA,
                error: 'Recovery already in progress'
            };
        }
        
        recoveryInProgress = true;
        debugLog('Handling quota exceeded error');
        
        try {
            // Attempt to free up space
            let spaceFreed = false;
            
            // Clear expired cache items
            if (window.StickaraIndexedDB && typeof window.StickaraIndexedDB.clearExpiredCache === 'function') {
                const clearedCount = await window.StickaraIndexedDB.clearExpiredCache();
                spaceFreed = clearedCount > 0;
                debugLog(`Cleared ${clearedCount} expired cache items`);
            }
            
            // If we couldn't free space, try more aggressive measures
            if (!spaceFreed && window.StickaraMemoryOptimizer) {
                const result = await window.StickaraMemoryOptimizer.optimizeMemory('critical');
                spaceFreed = result.action !== 'none';
                debugLog('Performed aggressive memory optimization');
            }
            
            recoveryInProgress = false;
            return { 
                success: spaceFreed, 
                strategy: RECOVERY_STRATEGIES.CLEAR_DATA,
                spaceFreed
            };
        } catch (error) {
            recoveryInProgress = false;
            console.error('Stickara: Error handling quota exceeded:', error);
            return { 
                success: false, 
                strategy: RECOVERY_STRATEGIES.CLEAR_DATA,
                error: error.message
            };
        }
    }

    /**
     * Handles database rebuild
     * @returns {Promise<Object>} A promise that resolves with the rebuild result
     */
    async function handleRebuild() {
        if (recoveryInProgress) {
            return { 
                success: false, 
                strategy: RECOVERY_STRATEGIES.REBUILD,
                error: 'Recovery already in progress'
            };
        }
        
        recoveryInProgress = true;
        debugLog('Attempting database rebuild');
        
        try {
            // This is a placeholder for actual rebuild logic
            // In a real implementation, you would:
            // 1. Back up critical data to fallback storage
            // 2. Delete and recreate the database
            // 3. Restore the data
            
            // For now, we'll just simulate a rebuild
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            recoveryInProgress = false;
            return { 
                success: true, 
                strategy: RECOVERY_STRATEGIES.REBUILD,
                rebuilt: true
            };
        } catch (error) {
            recoveryInProgress = false;
            console.error('Stickara: Error rebuilding database:', error);
            return { 
                success: false, 
                strategy: RECOVERY_STRATEGIES.REBUILD,
                error: error.message
            };
        }
    }

    /**
     * Clears error logs and retry counters
     */
    function clearErrorState() {
        errorLog.length = 0;
        Object.keys(retryCounters).forEach(key => delete retryCounters[key]);
        debugLog('Cleared error state');
    }

    // Return the public API
    return {
        // Core error handling
        handleError,
        categorizeError,
        determineRecoveryStrategy,
        clearErrorState,

        // Constants
        ERROR_CATEGORIES,
        RECOVERY_STRATEGIES,

        // Access to error log
        getErrorLog: () => [...errorLog],

        // Configuration
        updateConfig: (newConfig) => {
            Object.assign(config, newConfig);
            return { ...config };
        },
        getConfig: () => ({ ...config })
    };
})();

console.log("Stickara: Database Error Handler Loaded");
