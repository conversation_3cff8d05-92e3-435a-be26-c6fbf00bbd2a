// --- START OF FILE content-templates.js ---

// Make templates available in the shared scope, either via window or relying on execution order.
// Relying on order is simpler if primarily used during UI setup. Let's rely on order.

const StickaraTemplates = {

  // --- NEW TEMPLATE ---
  'Page Context': '<h3>Page Context</h3><p><strong>Date Captured:</strong> {DATE}</p><p><strong>Page URL:</strong> {URL}</p><p><strong>Selected Text:</strong> </p><blockquote>{SELECTION}</blockquote><p><strong>My Notes:</strong> </p><br>',
  // --- END NEW TEMPLATE ---

  // --- Student Focused (Non-Table) ---
  'Lecture Notes': '<h3>Lecture Notes</h3><p><strong>Date:</strong> {DATE}</p><p><strong>Topic:</strong> </p><p><strong>Key Concepts:</strong> </p><ul><li>Concept 1: </li><li>Concept 2: </li></ul><p><strong>Examples:</strong> </p><p><strong>Questions:</strong> </p>',
  'Study Session Prep': '<h3>Study Prep</h3><p><strong>Subject/Exam:</strong> </p><p><strong>Date:</strong> {DATE}</p><p><strong>Topics to Cover:</strong> </p><ul><li>Topic 1</li><li>Topic 2</li></ul><p><strong>Key Terms/Formulas:</strong> </p><p><strong>Practice Problems/Focus Areas:</strong> </p>',
  'Assignment Planner': '<h3>Assignment Planner</h3><p><strong>Assignment:</strong> </p><p><strong>Course:</strong> </p><p><strong>Due Date:</strong> </p><p><strong>Date Assigned:</strong> {DATE}</p><p><strong>Tasks:</strong> </p><ul><li>[ ] Task 1</li><li>[ ] Task 2</li></ul><p><strong>Resources:</strong> </p><p><strong>Status:</strong> </p>',
  'Reading Summary': '<h3>Reading Summary</h3><p><strong>Date Read:</strong> {DATE}</p><p><strong>Source:</strong> [Citation/Title]</p><p><strong>Main Argument/Thesis:</strong> </p><p><strong>Key Points:</strong> </p><ul><li>Point 1</li></ul><p><strong>Critique/Questions:</strong> </p>',

  // --- Professor Focused (Non-Table) ---
  'Lecture Prep Outline': '<h3>Lecture Prep</h3><p><strong>Course:</strong> </p><p><strong>Topic:</strong> </p><p><strong>Date:</strong> {DATE}</p><p><strong>Learning Objectives:</strong> </p><ul><li>Objective 1</li></ul><p><strong>Outline/Key Points:</strong> </p><p><strong>Activities/Examples:</strong> </p><p><strong>Time Notes:</strong> </p>',
  'Student Meeting Log': '<h3>Student Meeting</h3><p><strong>Student:</strong> </p><p><strong>Date:</strong> {DATE}</p><p><strong>Discussion Points:</strong> </p><ul><li>Point 1</li></ul><p><strong>Action Items/Follow-up:</strong> </p>',
  'Research Idea Sketch': '<h3>Research Idea</h3><p><strong>Date:</strong> {DATE}</p><p><strong>Problem/Question:</strong> </p><p><strong>Hypothesis (Optional):</strong> </p><p><strong>Potential Approach:</strong> </p><p><strong>Significance/Impact:</strong> </p><p><strong>Next Steps:</strong> </p>',

  // --- Scientist Focused (Non-Table) ---
  'Experiment Outline': '<h3>Experiment Outline</h3><p><strong>Date:</strong> {DATE}</p><p><strong>Experiment ID/Title:</strong> </p><p><strong>Objective:</strong> </p><p><strong>Hypothesis:</strong> </p><p><strong>Materials (Key):</strong> </p><p><strong>Methods (Brief Outline):</strong> </p><p><strong>Expected Outcome/Metric:</strong> </p>',
  'Literature Review Snippet': '<h3>Lit Review Snippet</h3><p><strong>Date Added:</strong> {DATE}</p><p><strong>Paper:</strong> [Short Citation / DOI]</p><p><strong>Key Finding/Argument:</strong> </p><p><strong>Methods Used:</strong> </p><p><strong>Relevance to My Work:</strong> </p><p><strong>Critique/Limitations/Questions:</strong> </p>',
  'Data Analysis Plan': '<h3>Data Analysis Plan</h3><p><strong>Date Created:</strong> {DATE}</p><p><strong>Dataset:</strong> </p><p><strong>Research Question(s):</strong> </p><p><strong>Analysis Steps/Methods:</strong> </p><ul><li>Step 1: [e.g., Data Cleaning]</li><li>Step 2: [e.g., Statistical Test]</li></ul><p><strong>Expected Output/Visualization:</strong> </p>',
  'Lab Meeting Notes': '<h3>Lab Meeting</h3><p><strong>Date:</strong> {DATE}</p><p><strong>Attendees:</strong> </p><p><strong>Agenda/Topics Discussed:</strong> </p><ul><li>Topic 1</li></ul><p><strong>Decisions Made:</strong> </p><p><strong>Action Items:</strong> </p><ul><li>Item 1 (Owner, Due Date)</li></ul>',

  // --- General Reference / Code ---
  // (Date might not be as relevant here, skipping)
  'Code Snippet': '<h3>Code Snippet</h3><p><strong>Language/Context:</strong> [e.g., Python, CSS, Bash]</p><p><strong>Description:</strong> </p><pre><code>// Paste code here\n</code></pre>',
  'Quick Reference': '<h3>Quick Reference</h3><p><strong>Topic:</strong> [e.g., Git Commands, Lab Values, Keyboard Shortcuts]</p><ul><li>Item 1: Value/Description</li><li>Item 2: Value/Description</li></ul><p><strong>Notes:</strong> </p>',
  'Q/A Flashcards': 'Q: [Your Question Here]\nA: [Your Answer Here]\n\nQ: [Another Question]\nA: [Another Answer]',

  // --- General Purpose (Non-Table) ---
  'To-Do List': '<h3>To-Do List ({DATE})</h3><ul><li>[ ] Item 1</li><li>[ ] Item 2</li><li>[ ] Item 3</li></ul>',
  'Meeting Notes': '<h3>Meeting Notes</h3><p><strong>Date:</strong> {DATE}</p><p><strong>Attendees:</strong> </p><p><strong>Agenda:</strong> </p><p><strong>Notes:</strong> </p><p><strong>Action Items:</strong> </p>',
  'Brainstorming Session': '<h3>Brainstorming Session ({DATE})</h3><p><strong>Topic:</strong> </p><p><strong>Ideas:</strong> </p><ul><li>Idea 1</li><li>Idea 2</li></ul><p><strong>Next Steps:</strong> </p>',
  'Project Plan': '<h3>Project Plan ({DATE})</h3><p><strong>Project Name:</strong> </p><p><strong>Objectives:</strong> </p><p><strong>Tasks:</strong> </p><ul><li>Task 1</li><li>Task 2</li></ul><p><strong>Deadlines:</strong> </p>',
  'Daily Journal': '<h3>Daily Journal</h3><p><strong>Date:</strong> {DATE}</p><p><strong>Highlights:</strong> </p><p><strong>Challenges:</strong> </p><p><strong>Reflections:</strong> </p>',
  'Shopping List': '<h3>Shopping List ({DATE})</h3><ul><li>Item 1</li><li>Item 2</li><li>Item 3</li></ul>',
  'Event Planner': '<h3>Event Planner</h3><p><strong>Event Name:</strong> </p><p><strong>Date & Time:</strong> </p><p><strong>Location:</strong> </p><p><strong>Planning Date:</strong> {DATE}</p><p><strong>Tasks:</strong> </p><ul><li>Task 1</li><li>Task 2</li></ul>',
  'Pros and Cons List': '<h3>Pros and Cons ({DATE})</h3><p><strong>Decision:</strong> </p><p><strong>Pros:</strong> </p><ul><li>Pro 1</li><li>Pro 2</li></ul><p><strong>Cons:</strong> </p><ul><li>Con 1</li><li>Con 2</li></ul>',
  'Goal Tracker': '<h3>Goal Tracker ({DATE})</h3><p><strong>Goal:</strong> </p><p><strong>Start Date:</strong> </p><p><strong>Milestones:</strong> </p><ul><li>Milestone 1</li><li>Milestone 2</li></ul><p><strong>Progress:</strong> </p>',
  'Recipe Notes': '<h3>Recipe Notes</h3><p><strong>Recipe Name:</strong> </p><p><strong>Date Added:</strong> {DATE}</p><p><strong>Ingredients:</strong> </p><ul><li>Ingredient 1</li><li>Ingredient 2</li></ul><p><strong>Instructions:</strong> </p>',
  'Workout Log': '<h3>Workout Log</h3><p><strong>Date:</strong> {DATE}</p><p><strong>Exercises:</strong> </p><ul><li>Exercise 1: Sets/Reps</li><li>Exercise 2: Sets/Reps</li></ul><p><strong>Notes:</strong> </p>',

  // --- Table Formats - Student ---
  'Grade Tracker': `
      <h3>Grade Tracker ({DATE})</h3>
      <table border="1" style="width:100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th>Course</th>
            <th>Assignment</th>
            <th>Grade</th>
            <th>Weight (%)</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td> </td>
            <td> </td>
            <td> </td>
            <td> </td>
          </tr>
          <!-- Add more rows as needed -->
        </tbody>
      </table>
      <p><strong>Overall Grade (Estimate):</strong> </p>
  `,
  'Weekly Schedule': `
      <h3>Weekly Schedule (Week of {DATE})</h3>
      <table border="1" style="width:100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th style="width:15%;">Time</th>
            <th style="width:17%;">Mon</th>
            <th style="width:17%;">Tue</th>
            <th style="width:17%;">Wed</th>
            <th style="width:17%;">Thu</th>
            <th style="width:17%;">Fri</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>9:00 AM</td> <td> </td> <td> </td> <td> </td> <td> </td> <td> </td>
          </tr>
           <tr>
            <td>10:00 AM</td> <td> </td> <td> </td> <td> </td> <td> </td> <td> </td>
          </tr>
           <tr>
            <td>11:00 AM</td> <td> </td> <td> </td> <td> </td> <td> </td> <td> </td>
          </tr>
           <tr>
            <td>12:00 PM</td> <td> </td> <td> </td> <td> </td> <td> </td> <td> </td>
          </tr>
           <tr>
            <td>1:00 PM</td> <td> </td> <td> </td> <td> </td> <td> </td> <td> </td>
          </tr>
          <!-- Add more time slots as needed -->
        </tbody>
      </table>
  `,
   'Reading List': `
      <h3>Reading List / Textbooks ({DATE})</h3>
      <table border="1" style="width:100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th>Course</th>
            <th>Title / Author</th>
            <th>Status</th>
            <th>Notes / Chaps</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td> </td>
            <td> </td>
            <td>To Read</td>
            <td> </td>
          </tr>
           <tr>
            <td> </td>
            <td> </td>
            <td>Reading</td>
            <td> </td>
          </tr>
          <!-- Add more rows as needed -->
        </tbody>
      </table>
  `,

  // --- Table Formats - Professor ---
  'Office Hours Log': `
      <h3>Office Hours Log ({DATE})</h3>
      <table border="1" style="width:100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th>Date</th>
            <th>Student Name</th>
            <th>Topic / Course</th>
            <th>Follow-up Needed?</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>{DATE}</td>
            <td> </td>
            <td> </td>
            <td> </td>
          </tr>
          <!-- Add more rows as needed -->
        </tbody>
      </table>
  `,
  'Simple Rubric': `
      <h3>Grading Rubric ({DATE})</h3>
      <p><strong>Assignment:</strong> </p>
      <table border="1" style="width:100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th>Criteria</th>
            <th>Points Possible</th>
            <th>Points Earned</th>
            <th>Comments</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Clarity</td> <td>10</td> <td> </td> <td> </td>
          </tr>
          <tr>
            <td>Completeness</td> <td>10</td> <td> </td> <td> </td>
          </tr>
           <tr>
            <td>Accuracy</td> <td>10</td> <td> </td> <td> </td>
          </tr>
          <!-- Add/Edit criteria as needed -->
          <tr>
            <td><strong>Total</strong></td> <td><strong>30</strong></td> <td> </td> <td> </td>
          </tr>
        </tbody>
      </table>
  `,
   'Publication Tracker': `
      <h3>Publication Tracker ({DATE})</h3>
      <table border="1" style="width:100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th>Paper Title (Short)</th>
            <th>Target Journal/Conf.</th>
            <th>Status</th>
            <th>Date Updated</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td> </td>
            <td> </td>
            <td>Drafting</td>
            <td>{DATE}</td>
          </tr>
          <tr>
            <td> </td>
            <td> </td>
            <td>Submitted</td>
            <td> </td>
          </tr>
          <!-- Add more rows as needed -->
        </tbody>
      </table>
  `,

  // --- Table Formats - Scientist ---
  'Experiment Parameters': `
      <h3>Experiment Parameters ({DATE})</h3>
      <p><strong>Experiment ID:</strong> </p>
      <table border="1" style="width:100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th>Parameter</th>
            <th>Value</th>
            <th>Unit</th>
            <th>Notes</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Temperature</td> <td> </td> <td>°C</td> <td> </td>
          </tr>
          <tr>
            <td>Concentration</td> <td> </td> <td>mM</td> <td> </td>
          </tr>
          <!-- Add more rows as needed -->
        </tbody>
      </table>
  `,
  'Sample Inventory': `
      <h3>Sample Inventory ({DATE})</h3>
      <table border="1" style="width:100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th>Sample ID</th>
            <th>Type / Source</th>
            <th>Date Collected</th>
            <th>Storage Location</th>
            <th>Notes</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td> </td>
            <td> </td>
            <td>{DATE}</td>
            <td>Freezer A, Box 3</td>
            <td> </td>
          </tr>
          <!-- Add more rows as needed -->
        </tbody>
      </table>
  `,
  'Reagent Inventory': `
      <h3>Reagent Inventory ({DATE})</h3>
      <table border="1" style="width:100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th>Reagent Name</th>
            <th>Catalog #</th>
            <th>Lot #</th>
            <th>Expiry Date</th>
            <th>Location</th>
            <th>Quantity</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td> </td>
            <td> </td>
            <td> </td>
            <td> </td>
            <td>Chem Cabinet</td>
            <td> </td>
          </tr>
          <!-- Add more rows as needed -->
        </tbody>
      </table>
  `,
   'Protocol Checklist': `
      <h3>Protocol Steps ({DATE})</h3>
      <p><strong>Protocol Name:</strong> </p>
      <table border="1" style="width:100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th style="width:5%;">Step</th>
            <th style="width:50%;">Action</th>
            <th style="width:10%;">Done?</th>
            <th>Observations / Notes</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>1</td> <td>Prepare buffer A</td> <td>[ ]</td> <td> </td>
          </tr>
          <tr>
            <td>2</td> <td>Incubate at 37°C for 1 hour</td> <td>[ ]</td> <td> </td>
          </tr>
          <!-- Add more steps as needed -->
        </tbody>
      </table>
  `,

  // --- Table Formats - General ---
  'Comparison': `
      <h3>Comparison Table ({DATE})</h3>
      <table border="1" style="width:100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th>Feature</th>
            <th>Option 1</th>
            <th>Option 2</th>
            <!-- Add more option columns if needed -->
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Cost</td> <td> </td> <td> </td>
          </tr>
          <tr>
            <td>Ease of Use</td> <td> </td> <td> </td>
          </tr>
          <!-- Add more feature rows as needed -->
        </tbody>
      </table>
  `,
  'Task List': `
      <h3>Task List ({DATE})</h3>
      <table border="1" style="width:100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th style="width:50%;">Task Description</th>
            <th>Due Date</th>
            <th>Status</th>
            <th>Notes</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td> </td> <td> </td> <td>Not Started</td> <td> </td>
          </tr>
          <tr>
            <td> </td> <td> </td> <td>In Progress</td> <td> </td>
          </tr>
           <tr>
            <td> </td> <td> </td> <td>Completed</td> <td> </td>
          </tr>
          <!-- Add more rows as needed -->
        </tbody>
      </table>
  `,
   'Simple Schedule': `
      <h3>Simple Schedule ({DATE})</h3>
      <table border="1" style="width:100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th style="width:25%;">Time</th>
            <th>Activity/Event</th>
          </tr>
        </thead>
        <tbody>
          <tr> <td>9:00 AM</td> <td> </td> </tr>
          <tr> <td>10:00 AM</td> <td> </td> </tr>
          <tr> <td>11:00 AM</td> <td> </td> </tr>
          <!-- Add more time slots as needed -->
        </tbody>
      </table>
  `,
  'Contact List': `
      <h3>Contact List ({DATE})</h3>
      <table border="1" style="width:100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th>Name</th>
            <th>Email</th>
            <th>Phone</th>
            <th>Notes</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td> </td> <td> </td> <td> </td> <td> </td>
          </tr>
          <!-- Add more rows as needed -->
        </tbody>
      </table>
  `,
  'Simple Budget': `
      <h3>Simple Budget ({DATE})</h3>
      <table border="1" style="width:100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th>Category</th>
            <th>Planned</th>
            <th>Actual</th>
            <th>Difference</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Income</td> <td> </td> <td> </td> <td> </td>
          </tr>
           <tr>
            <td>Rent/Mortgage</td> <td> </td> <td> </td> <td> </td>
          </tr>
          <tr>
            <td>Groceries</td> <td> </td> <td> </td> <td> </td>
          </tr>
          <!-- Add more rows as needed -->
           <tr>
            <td><strong>Total Expenses</strong></td> <td> </td> <td> </td> <td> </td>
          </tr>
           <tr>
            <td><strong>Net</strong></td> <td> </td> <td> </td> <td> </td>
          </tr>
        </tbody>
      </table>
  `,

  // --- Smart Template Examples ---
  'Project Planning': `<h3>Project: {{Prompt:Enter Project Name}}</h3>
<p><strong>Date Created:</strong> {DATE}</p>
<p><strong>Priority:</strong> {{Prompt:Select Priority|High,Medium,Low}}</p>
<p><strong>Due Date:</strong> {{Prompt:Enter Due Date|{{DueDate+30days}}}}</p>
{{If:HasTags}}
<p><strong>Tags:</strong> {{Tags}}</p>
{{EndIf}}
<p><strong>Description:</strong> {{Prompt:Enter Project Description}}</p>
<p><strong>Tasks:</strong></p>
<ul>
  <li>[ ] {{Prompt:Enter First Task}}</li>
  <li>[ ] {{Prompt:Enter Second Task}}</li>
</ul>
{{If:ReminderSet}}
<p><strong>Reminder:</strong> {{ReminderDate}}</p>
{{EndIf}}`,

  'Meeting Notes': `<h3>Meeting: {{Prompt:Enter Meeting Title}}</h3>
<p><strong>Date:</strong> {DATE}</p>
<p><strong>Time:</strong> {{Prompt:Enter Meeting Time|{{CurrentTime}}}}</p>
<p><strong>Attendees:</strong> {{Prompt:Enter Attendees}}</p>
{{If:URLContains:zoom,teams,meet}}
<p><strong>Meeting Link:</strong> {URL}</p>
{{EndIf}}
<p><strong>Agenda:</strong></p>
<ul>
  <li>{{Prompt:Enter First Agenda Item}}</li>
  <li>{{Prompt:Enter Second Agenda Item}}</li>
</ul>
<p><strong>Notes:</strong></p>
<p>{{Prompt:Enter Initial Notes}}</p>
<p><strong>Action Items:</strong></p>
<ul>
  <li>[ ] {{Prompt:Enter First Action Item}} - Due: {{DueDate+7days}}</li>
</ul>`,

  'Study Session': `<h3>Study Session: {{Prompt:Enter Subject}}</h3>
<p><strong>Date:</strong> {DATE}</p>
<p><strong>Duration:</strong> {{Prompt:Enter Duration|2 hours}}</p>
{{If:DateRange:exam-season}}
<p><strong>Exam Preparation Mode</strong> 📚</p>
{{Else}}
<p><strong>Regular Study Session</strong> 📖</p>
{{EndIf}}
<p><strong>Topics to Cover:</strong></p>
<ul>
  <li>{{Prompt:Enter First Topic}}</li>
  <li>{{Prompt:Enter Second Topic}}</li>
</ul>
<p><strong>Study Method:</strong> {{Prompt:Select Study Method|Reading,Practice Problems,Flashcards,Group Study}}</p>
{{If:HasSelection}}
<p><strong>Selected Text:</strong></p>
<blockquote>{SELECTION}</blockquote>
{{EndIf}}
<p><strong>Progress:</strong></p>
<p>{{Prompt:Enter Current Progress}}</p>`,

  'Daily Journal': `<h3>Daily Journal - {{CurrentDate:MMMM Do, YYYY}}</h3>
<p><strong>Weather:</strong> {{Prompt:How's the weather?|Sunny}}</p>
<p><strong>Mood:</strong> {{Prompt:Rate your mood|😊,😐,😔,😴,🤔}}</p>
{{If:Weekend}}
<p><strong>Weekend Activities:</strong></p>
{{Else}}
<p><strong>Work/School Day:</strong></p>
{{EndIf}}
<p><strong>Highlights:</strong></p>
<ul>
  <li>{{Prompt:What was the best part of today?}}</li>
</ul>
<p><strong>Challenges:</strong></p>
<ul>
  <li>{{Prompt:Any challenges faced?|None}}</li>
</ul>
<p><strong>Tomorrow's Goals:</strong></p>
<ul>
  <li>{{Prompt:Main goal for tomorrow}}</li>
</ul>
<p><strong>Gratitude:</strong></p>
<p>{{Prompt:What are you grateful for today?}}</p>`,

  'Bug Report': `<h3>Bug Report: {{Prompt:Enter Bug Title}}</h3>
<p><strong>Date Reported:</strong> {DATE}</p>
<p><strong>Reporter:</strong> {{Prompt:Enter Your Name}}</p>
<p><strong>Severity:</strong> {{Prompt:Select Severity|Critical,High,Medium,Low}}</p>
{{If:URLContains:github,jira,bugzilla}}
<p><strong>Issue URL:</strong> {URL}</p>
{{EndIf}}
<p><strong>Environment:</strong></p>
<ul>
  <li>Browser: {{Prompt:Enter Browser|Chrome}}</li>
  <li>OS: {{Prompt:Enter OS|Windows}}</li>
  <li>Version: {{Prompt:Enter Version}}</li>
</ul>
<p><strong>Steps to Reproduce:</strong></p>
<ol>
  <li>{{Prompt:Enter First Step}}</li>
  <li>{{Prompt:Enter Second Step}}</li>
  <li>{{Prompt:Enter Third Step}}</li>
</ol>
<p><strong>Expected Result:</strong></p>
<p>{{Prompt:What should happen?}}</p>
<p><strong>Actual Result:</strong></p>
<p>{{Prompt:What actually happened?}}</p>
{{If:HasSelection}}
<p><strong>Error Message:</strong></p>
<pre>{SELECTION}</pre>
{{EndIf}}`,

};

console.log("Stickara: Categorized Templates Loaded (Date Added to Many)");

// --- END OF FILE content-templates.js ---