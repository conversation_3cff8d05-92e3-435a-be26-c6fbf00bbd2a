// --- START OF FILE content-main.js ---

/**
 * Checks if the current site is ChatGPT and applies specific handling
 * @returns {boolean} True if this is a ChatGPT site
 */
function isChatGPTSite() {
    const url = window.location.href;
    return url.includes('chatgpt.com') || url.includes('chat.openai.com');
}

/**
 * Waits for ChatGPT to finish loading before initializing Stickara
 * @returns {Promise} Promise that resolves when ChatGPT is ready
 */
function waitForChatGPTReady() {
    return new Promise((resolve) => {
        console.log("Stickara: Waiting for ChatGPT to finish loading...");

        // Check if ChatGPT's main container is ready
        const checkReady = () => {
            // Look for ChatGPT's main content area
            const mainContent = document.querySelector('main') ||
                               document.querySelector('[role="main"]') ||
                               document.querySelector('.flex.h-full.flex-col');

            if (mainContent && document.readyState === 'complete') {
                console.log("Stickara: ChatGPT appears to be ready");
                resolve();
            } else {
                setTimeout(checkReady, 500);
            }
        };

        // Start checking after a short delay to let React settle
        setTimeout(checkReady, 1000);
    });
}

/**
 * Applies ChatGPT-specific fixes and styling
 */
function applyChatGPTFixes() {
    console.log("Stickara: Applying ChatGPT-specific fixes");

    // Add ChatGPT-specific CSS
    const style = document.createElement('style');
    style.id = 'stickara-chatgpt-fixes';
    style.textContent = `
        /* ChatGPT specific fixes - only essential changes for compatibility */
        #Stickara-toggle {
            z-index: 2147483647 !important;
        }

        #Stickara-container {
            z-index: 2147483647 !important;
        }

        /* Ensure Stickara elements don't interfere with ChatGPT's React rendering */
        #Stickara-toggle,
        #Stickara-container,
        #Stickara-flashcard-modal {
            pointer-events: auto !important;
            isolation: isolate !important;
        }
    `;
    document.head.appendChild(style);
}

/**
 * Main initialization function for the Stickara content script.
 * Creates UI elements, loads data, and sets up observers/listeners.
 */
function initStickara() {
    console.log("Stickara: Initializing..."); // DEBUG

    // Check if this is ChatGPT and apply specific fixes
    if (isChatGPTSite()) {
        console.log("Stickara: ChatGPT site detected");
        applyChatGPTFixes();
    }

    // Standard initialization for all sites
    console.log("Stickara: Using standard initialization");

    // 1. Create core UI elements if they don't exist
    try {
        if (typeof createToggleButton === 'function') {
            createToggleButton(); // Defined in ui.js
        } else {
            console.error("Stickara: createToggleButton function not found!");
        }

        if (typeof createNoteUI === 'function') {
            createNoteUI(); // Defined in ui.js
        } else {
            console.error("Stickara: createNoteUI function not found!");
        }

        if (typeof createFlashcardModal === 'function') {
            createFlashcardModal(); // Defined in ui.js
        } else {
            console.error("Stickara: createFlashcardModal function not found!");
        }

    } catch (error) {
        console.error("Stickara: Error creating UI elements:", error);
    }

    // 2. Load saved state (notes data, visibility, current note)
    //    loadState will call loadCurrentNote internally.
    loadState(); // Defined in storage.js



    // Initialize enhanced voice module if available
    if (typeof initEnhancedVoice === 'function') {
        try {
            initEnhancedVoice(); // Defined in content-voice-enhanced.js
            console.log("Stickara: Enhanced voice module initialized");
        } catch (error) {
            console.error("Stickara: Failed to initialize enhanced voice module:", error);
        }
    }



    // 3. Load and apply highlights from storage with dynamic content change detection
    //    Wait for window.load AND an additional delay for dynamic content
    const applyHighlightsAfterLoad = () => {
         if (typeof highlightKey !== 'undefined') {
              console.log("Stickara: Applying highlights after load + delay with dynamic detection."); // DEBUG
              // Use enhanced version that includes dynamic content change detection
              if (typeof loadAndApplyHighlightsEnhanced === 'function') {
                  loadAndApplyHighlightsEnhanced(); // Enhanced version with dynamic detection
              } else {
                  loadAndApplyHighlights(); // Fallback to original version
              }
          } else {
              console.error("Stickara: highlightKey still not defined after delay."); // DEBUG
          }
    };

    // 4. Set up note URL update handling for SPA navigation (YouTube, etc.)
    if (typeof setupNoteURLUpdateHandling === 'function') {
        setupNoteURLUpdateHandling();
    }

    if (document.readyState === 'complete') {
         // If already complete, apply after a short delay
         console.log("Stickara: DOM already complete, scheduling highlight apply."); // DEBUG
         setTimeout(applyHighlightsAfterLoad, 500); // 500ms delay
     } else {
         // Wait for the 'load' event, then add a delay
         console.log("Stickara: Waiting for window.load to schedule highlight apply."); // DEBUG
         window.addEventListener('load', () => {
             console.log("Stickara: window.load fired, scheduling highlight apply."); // DEBUG
             setTimeout(applyHighlightsAfterLoad, 500); // 500ms delay after load
         }, { once: true });
     }


}


// --- Script Entry Point ---

// Add global error handler for extension context invalidation
window.addEventListener('error', (event) => {
    if (event.error && event.error.message && event.error.message.includes('Extension context invalidated')) {
        console.warn("Stickara: Extension context invalidated, stopping operations");
        // Could add cleanup logic here if needed
        return true; // Prevent default error handling
    }
});

// Use a self-executing function to avoid polluting the global scope unnecessarily,
(function() {
    // Check if the script has already run on this page (e.g., due to re-injection)
    if (window.StickaraInitialized) {
        console.log("Stickara: Content script already initialized. Skipping.");
        return;
    }
    window.StickaraInitialized = true; // Set flag
    console.log("Stickara: Content script running for the first time.");

    // Test element removed - script is working properly

    // Special handling for ChatGPT to avoid React conflicts
    if (isChatGPTSite()) {
        console.log("Stickara: ChatGPT detected, using delayed initialization");

        // Wait for ChatGPT to finish loading, then initialize
        const initWithDelay = async () => {
            try {
                await waitForChatGPTReady();
                console.log("Stickara: ChatGPT ready, initializing Stickara");
                initStickara();
            } catch (error) {
                console.error("Stickara: Error waiting for ChatGPT:", error);
                // Fallback to normal initialization
                setTimeout(initStickara, 2000);
            }
        };

        initWithDelay();
    } else {
        // Normal initialization for other sites
        // Wait for the DOM to be ready before initializing
        if (document.readyState === 'loading') {
             console.log("Stickara: DOM loading, waiting for DOMContentLoaded."); // DEBUG
            document.addEventListener('DOMContentLoaded', initStickara);
        } else {
            // DOM is already ready
             console.log("Stickara: DOM already ready, initializing now."); // DEBUG
            initStickara();
        }
    }
})();



// Listen for messages from the popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    // Check for extension context invalidation
    if (chrome.runtime.lastError) {
        console.warn("Stickara: Extension context invalidated:", chrome.runtime.lastError.message);
        return false;
    }

    // Validate the sender is from our extension
    if (sender.id !== chrome.runtime.id) {
        console.warn("Stickara: Rejected message from unauthorized sender:", sender.id);
        return false;
    }


    return true; // Keep the message channel open for async response
});

// Initialize rendering optimization module if available
if (typeof window.SB_RENDERING_OPTIMIZATION_LOADED === 'undefined') {
    // The module will set SB_RENDERING_OPTIMIZATION_LOADED to true when loaded
}

// Initialize URL utilities if available
if (typeof window.StickaraURLUtils === 'undefined') {
    // The module will be loaded from lib/url-utils.js
}

console.log("Stickara: Main Script Loaded (v1.9 - Rendering Pipeline Optimization)"); // Updated version
// --- END OF FILE content-main.js ---