/**
 * Stickara Memory Optimizer
 * Provides memory optimization techniques to reduce RAM usage
 */

// Create a namespace to avoid global pollution
window.StickaraMemoryOptimizer = (function() {
    // Constants
    const MEMORY_THRESHOLDS = {
        LOW: 10 * 1024 * 1024,     // 10 MB - Low memory pressure
        MEDIUM: 50 * 1024 * 1024,  // 50 MB - Medium memory pressure
        HIGH: 100 * 1024 * 1024    // 100 MB - High memory pressure
    };

    // Configuration
    const config = {
        enabled: true,                  // Enable memory optimization
        aggressiveMode: false,          // More aggressive optimization (sacrifices performance for memory)
        memoryCacheMaxSize: 5 * 1024 * 1024, // 5 MB max for memory cache
        checkInterval: 60 * 1000,       // Check memory usage every minute
        trimThreshold: MEMORY_THRESHOLDS.MEDIUM, // When to start trimming memory
        criticalThreshold: MEMORY_THRESHOLDS.HIGH, // Critical memory pressure
        debug: false                    // Debug mode
    };

    // Private variables
    let memoryUsageTimer = null;
    let lastMemoryPressure = 'normal';
    let memoryStats = {
        lastCheck: 0,
        estimatedUsage: 0,
        pressure: 'normal'
    };

    /**
     * Log messages when debug mode is enabled
     * @param {...any} args - Arguments to log
     */
    function debugLog(...args) {
        if (config.debug) {
            console.log('[StickaraMemoryOptimizer]', ...args);
        }
    }

    /**
     * Estimates the current memory usage
     * @returns {Object} Memory usage statistics
     */
    function estimateMemoryUsage() {
        try {
            // Try to use the performance API if available
            if (window.performance && window.performance.memory) {
                const memoryInfo = window.performance.memory;
                const usage = memoryInfo.usedJSHeapSize;
                const total = memoryInfo.totalJSHeapSize;
                const limit = memoryInfo.jsHeapSizeLimit;

                memoryStats.estimatedUsage = usage;
                memoryStats.totalAllocated = total;
                memoryStats.limit = limit;
                memoryStats.lastCheck = Date.now();

                // Determine memory pressure
                if (usage > config.criticalThreshold) {
                    memoryStats.pressure = 'critical';
                } else if (usage > config.trimThreshold) {
                    memoryStats.pressure = 'high';
                } else if (usage > MEMORY_THRESHOLDS.LOW) {
                    memoryStats.pressure = 'medium';
                } else {
                    memoryStats.pressure = 'normal';
                }

                debugLog(`Memory usage: ${formatBytes(usage)} / ${formatBytes(total)} (${formatBytes(limit)} limit), pressure: ${memoryStats.pressure}`);
                return memoryStats;
            }

            // Fallback: use a simple heuristic based on time since last GC request
            const now = Date.now();
            memoryStats.lastCheck = now;
            
            // Simple heuristic: assume memory pressure increases over time
            // and decreases after garbage collection
            if (now - memoryStats.lastGC > 300000) { // 5 minutes
                memoryStats.pressure = 'high';
            } else if (now - memoryStats.lastGC > 120000) { // 2 minutes
                memoryStats.pressure = 'medium';
            } else {
                memoryStats.pressure = 'normal';
            }

            debugLog(`Estimated memory pressure: ${memoryStats.pressure} (fallback method)`);
            return memoryStats;
        } catch (error) {
            console.error('Stickara: Error estimating memory usage:', error);
            return { pressure: 'unknown', error: error.message };
        }
    }

    /**
     * Formats bytes into a human-readable string
     * @param {number} bytes - The number of bytes
     * @returns {string} Formatted string (e.g., "4.2 MB")
     */
    function formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Optimizes memory usage based on current pressure
     * @param {string} [forcePressure] - Force a specific pressure level
     * @returns {Object} Result of optimization
     */
    function optimizeMemory(forcePressure) {
        if (!config.enabled) return { success: true, action: 'none', reason: 'disabled' };

        // Get current memory pressure
        const pressure = forcePressure || estimateMemoryUsage().pressure;
        
        // Track if pressure changed
        const pressureChanged = pressure !== lastMemoryPressure;
        lastMemoryPressure = pressure;

        // Take action based on memory pressure
        let action = 'none';
        let details = {};

        switch (pressure) {
            case 'critical':
                // Critical pressure: aggressive cleanup
                action = 'aggressive-cleanup';
                details = performAggressiveCleanup();
                break;

            case 'high':
                // High pressure: trim caches
                action = 'trim-caches';
                details = trimCaches();
                break;

            case 'medium':
                // Medium pressure: light optimization
                if (pressureChanged || config.aggressiveMode) {
                    action = 'light-optimization';
                    details = performLightOptimization();
                }
                break;

            default:
                // Normal pressure: no action needed
                break;
        }

        debugLog(`Memory optimization: ${action} (pressure: ${pressure})`);
        return { success: true, action, pressure, details };
    }

    /**
     * Performs aggressive memory cleanup
     * @returns {Object} Cleanup results
     */
    function performAggressiveCleanup() {
        const results = {
            cacheCleared: false,
            gcRequested: false
        };

        // Clear all memory caches
        if (window.StickaraIndexedDB && typeof window.StickaraIndexedDB.clearMemoryCache === 'function') {
            window.StickaraIndexedDB.clearMemoryCache();
            results.cacheCleared = true;
        }

        // Request garbage collection (will only work in some environments)
        try {
            if (window.gc) {
                window.gc();
                results.gcRequested = true;
                memoryStats.lastGC = Date.now();
            }
        } catch (e) {
            // Ignore errors
        }

        return results;
    }

    /**
     * Trims memory caches to reduce memory usage
     * @returns {Object} Trim results
     */
    function trimCaches() {
        const results = {
            memoryCacheTrimmed: false,
            oldItemsRemoved: 0
        };

        // Trim memory cache if available
        if (window.StickaraIndexedDB && typeof window.StickaraIndexedDB.trimMemoryCache === 'function') {
            const trimResult = window.StickaraIndexedDB.trimMemoryCache(0.5); // Trim 50%
            results.memoryCacheTrimmed = true;
            results.oldItemsRemoved = trimResult.removedCount;
        }

        return results;
    }

    /**
     * Performs light memory optimization
     * @returns {Object} Optimization results
     */
    function performLightOptimization() {
        const results = {
            oldItemsRemoved: 0
        };

        // Remove old items from memory cache
        if (window.StickaraIndexedDB && typeof window.StickaraIndexedDB.trimMemoryCache === 'function') {
            const trimResult = window.StickaraIndexedDB.trimMemoryCache(0.2); // Trim 20%
            results.oldItemsRemoved = trimResult.removedCount;
        }

        return results;
    }

    /**
     * Starts periodic memory usage monitoring
     */
    function startMonitoring() {
        if (memoryUsageTimer) {
            clearInterval(memoryUsageTimer);
        }

        memoryUsageTimer = setInterval(() => {
            const memoryStatus = estimateMemoryUsage();
            
            // Optimize memory if needed
            if (memoryStatus.pressure !== 'normal') {
                optimizeMemory(memoryStatus.pressure);
            }
        }, config.checkInterval);

        debugLog(`Started memory monitoring (interval: ${config.checkInterval}ms)`);
    }

    /**
     * Stops memory usage monitoring
     */
    function stopMonitoring() {
        if (memoryUsageTimer) {
            clearInterval(memoryUsageTimer);
            memoryUsageTimer = null;
            debugLog('Stopped memory monitoring');
        }
    }

    // Initialize when the module loads
    if (config.enabled) {
        startMonitoring();
    }

    // Return the public API
    return {
        // Core operations
        estimateMemoryUsage,
        optimizeMemory,
        startMonitoring,
        stopMonitoring,

        // Constants
        MEMORY_THRESHOLDS,

        // Configuration
        updateConfig: (newConfig) => {
            const oldEnabled = config.enabled;
            Object.assign(config, newConfig);
            
            // Handle monitoring state changes
            if (!oldEnabled && config.enabled) {
                startMonitoring();
            } else if (oldEnabled && !config.enabled) {
                stopMonitoring();
            }
            
            return { ...config };
        },
        getConfig: () => ({ ...config }),
        getMemoryStats: () => ({ ...memoryStats })
    };
})();

console.log("Stickara: Memory Optimizer Loaded");
