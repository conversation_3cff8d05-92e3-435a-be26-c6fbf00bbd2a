/**
 * Stickara Background Optimizer
 * Provides automatic data cleanup and optimization during idle periods
 */

// Create a namespace to avoid global pollution
window.StickaraBackgroundOptimizer = (function() {
    // Task types
    const TASK_TYPES = {
        CACHE_CLEANUP: 'cache_cleanup',
        STORAGE_OPTIMIZATION: 'storage_optimization',
        DATA_COMPRESSION: 'data_compression',
        INTEGRITY_CHECK: 'integrity_check',
        BACKUP: 'backup'
    };

    // Task priorities
    const PRIORITIES = {
        HIGH: 1,
        MEDIUM: 2,
        LOW: 3
    };

    // Configuration
    const config = {
        enabled: true,                  // Enable background optimization
        idleThreshold: 3000,            // Idle time threshold in milliseconds
        maxTaskDuration: 50,            // Maximum task duration in milliseconds
        taskInterval: 100,              // Interval between tasks in milliseconds
        maxConcurrentTasks: 1,          // Maximum number of concurrent tasks
        compressionThreshold: 10240,    // Minimum size for compression in bytes
        cleanupInterval: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
        optimizationInterval: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
        debug: false                    // Debug mode
    };

    // Private variables
    let isRunning = false;
    let idleDetectorId = null;
    let taskQueue = [];
    let activeTasks = 0;
    let lastCleanup = 0;
    let lastOptimization = 0;
    let lastBackup = 0;
    let stats = {
        tasksCompleted: 0,
        tasksSkipped: 0,
        itemsProcessed: 0,
        bytesReclaimed: 0,
        lastRun: null
    };

    /**
     * Log messages when debug mode is enabled
     * @param {...any} args - Arguments to log
     */
    function debugLog(...args) {
        if (config.debug) {
            console.log('[StickaraBackgroundOptimizer]', ...args);
        }
    }

    /**
     * Initializes the background optimizer
     * @returns {Promise<boolean>} A promise that resolves with true if initialization was successful
     */
    async function init() {
        try {
            // Set up idle detector
            setupIdleDetector();

            // Schedule initial tasks
            scheduleInitialTasks();

            // Load stats from storage
            await loadStats();

            return true;
        } catch (error) {
            console.error('Stickara: Error initializing background optimizer:', error);
            return false;
        }
    }

    /**
     * Sets up the idle detector
     */
    function setupIdleDetector() {
        // Clear any existing detector
        if (idleDetectorId) {
            clearInterval(idleDetectorId);
        }

        // Set up detector
        let lastActivityTime = Date.now();

        // Track user activity
        const activityEvents = ['mousedown', 'mousemove', 'keydown', 'scroll', 'touchstart'];

        activityEvents.forEach(eventType => {
            window.addEventListener(eventType, () => {
                lastActivityTime = Date.now();

                // If we're running tasks, pause them
                if (isRunning) {
                    pauseTasks();
                }
            }, { passive: true });
        });

        // Check for idle time
        idleDetectorId = setInterval(() => {
            const idleTime = Date.now() - lastActivityTime;

            if (idleTime >= config.idleThreshold && !isRunning && config.enabled) {
                // User is idle, start processing tasks
                startTasks();
            }
        }, 1000);
    }

    /**
     * Schedules initial tasks
     */
    function scheduleInitialTasks() {
        // Add cache cleanup task
        addTask({
            type: TASK_TYPES.CACHE_CLEANUP,
            priority: PRIORITIES.HIGH,
            execute: cleanupCache
        });

        // Add storage optimization task
        addTask({
            type: TASK_TYPES.STORAGE_OPTIMIZATION,
            priority: PRIORITIES.MEDIUM,
            execute: optimizeStorage
        });

        // Add data compression task
        addTask({
            type: TASK_TYPES.DATA_COMPRESSION,
            priority: PRIORITIES.LOW,
            execute: compressData
        });

        // Add integrity check task
        addTask({
            type: TASK_TYPES.INTEGRITY_CHECK,
            priority: PRIORITIES.LOW,
            execute: checkDataIntegrity
        });

        // Add backup task
        addTask({
            type: TASK_TYPES.BACKUP,
            priority: PRIORITIES.MEDIUM,
            execute: createBackup
        });
    }

    /**
     * Loads stats from storage
     * @returns {Promise<void>}
     */
    async function loadStats() {
        try {
            if (window.StickaraIndexedDB) {
                const storedStats = await window.StickaraIndexedDB.getMetadata('background_optimizer_stats');

                if (storedStats) {
                    stats = { ...stats, ...storedStats };
                    lastCleanup = storedStats.lastCleanup || 0;
                    lastOptimization = storedStats.lastOptimization || 0;
                    lastBackup = storedStats.lastBackup || 0;
                }
            }
        } catch (error) {
            console.error('Stickara: Error loading background optimizer stats:', error);
        }
    }

    /**
     * Saves stats to storage
     * @returns {Promise<void>}
     */
    async function saveStats() {
        try {
            if (window.StickaraIndexedDB) {
                const statsToSave = {
                    ...stats,
                    lastCleanup,
                    lastOptimization,
                    lastBackup
                };

                await window.StickaraIndexedDB.setMetadata({
                    key: 'background_optimizer_stats',
                    data: statsToSave,
                    lastModified: Date.now()
                });
            }
        } catch (error) {
            console.error('Stickara: Error saving background optimizer stats:', error);
        }
    }

    /**
     * Adds a task to the queue
     * @param {Object} task - The task to add
     */
    function addTask(task) {
        // Add timestamp and id
        const taskWithMeta = {
            ...task,
            id: `task-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
            timestamp: Date.now(),
            status: 'pending'
        };

        // Add to queue
        taskQueue.push(taskWithMeta);

        // Sort queue by priority
        taskQueue.sort((a, b) => a.priority - b.priority);

        debugLog(`Added task: ${task.type} (priority: ${task.priority})`);
    }

    /**
     * Starts processing tasks
     */
    function startTasks() {
        if (isRunning || !config.enabled) return;

        isRunning = true;
        activeTasks = 0;

        debugLog('Starting background tasks');

        // Process tasks
        processNextTask();
    }

    /**
     * Pauses task processing
     */
    function pauseTasks() {
        isRunning = false;
        debugLog('Paused background tasks');
    }

    /**
     * Processes the next task in the queue
     */
    function processNextTask() {
        if (!isRunning || activeTasks >= config.maxConcurrentTasks) return;

        // Check if there are tasks in the queue
        if (taskQueue.length === 0) {
            isRunning = false;
            debugLog('No more tasks to process');
            return;
        }

        // Get the next task
        const task = taskQueue.shift();
        activeTasks++;

        // Update task status
        task.status = 'running';
        task.startTime = Date.now();

        debugLog(`Processing task: ${task.type}`);

        // Execute the task with a time limit
        executeWithTimeLimit(task.execute, config.maxTaskDuration)
            .then(result => {
                // Task completed successfully
                task.status = 'completed';
                task.endTime = Date.now();
                task.result = result;

                // Update stats
                stats.tasksCompleted++;
                stats.lastRun = Date.now();

                if (result) {
                    stats.itemsProcessed += result.itemsProcessed || 0;
                    stats.bytesReclaimed += result.bytesReclaimed || 0;
                }

                debugLog(`Task completed: ${task.type}`, result);
            })
            .catch(error => {
                // Task failed
                task.status = 'failed';
                task.endTime = Date.now();
                task.error = error.message;

                console.error(`Stickara: Error in background task ${task.type}:`, error);
            })
            .finally(() => {
                // Decrement active tasks
                activeTasks--;

                // Save stats periodically
                if (stats.tasksCompleted % 5 === 0) {
                    saveStats();
                }

                // Schedule next task
                setTimeout(() => {
                    if (isRunning) {
                        processNextTask();
                    }
                }, config.taskInterval);
            });
    }

    /**
     * Executes a function with a time limit
     * @param {Function} fn - The function to execute
     * @param {number} timeLimit - The time limit in milliseconds
     * @returns {Promise<any>} A promise that resolves with the function result
     */
    function executeWithTimeLimit(fn, timeLimit) {
        return new Promise((resolve, reject) => {
            // Create a timeout
            const timeoutId = setTimeout(() => {
                reject(new Error(`Task timed out after ${timeLimit}ms`));
            }, timeLimit);

            // Execute the function
            Promise.resolve(fn())
                .then(result => {
                    clearTimeout(timeoutId);
                    resolve(result);
                })
                .catch(error => {
                    clearTimeout(timeoutId);
                    reject(error);
                });
        });
    }

    /**
     * Cleans up expired cache items
     * @returns {Promise<Object>} A promise that resolves with the cleanup result
     */
    async function cleanupCache() {
        try {
            // Check if it's time for cleanup
            const now = Date.now();
            if (now - lastCleanup < config.cleanupInterval) {
                debugLog('Skipping cache cleanup - not due yet');
                return { skipped: true, reason: 'not_due' };
            }

            // Perform cleanup
            let itemsRemoved = 0;
            let bytesReclaimed = 0;

            if (window.StickaraIndexedDB) {
                // Clear expired cache items
                const result = await window.StickaraIndexedDB.clearExpiredCache();
                itemsRemoved += result || 0;

                // Estimate bytes reclaimed (rough estimate)
                bytesReclaimed += itemsRemoved * 1024; // Assume 1KB per item
            }

            // Update last cleanup time
            lastCleanup = now;

            return {
                success: true,
                itemsProcessed: itemsRemoved,
                bytesReclaimed: bytesReclaimed
            };
        } catch (error) {
            console.error('Stickara: Error in cache cleanup task:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Optimizes storage by compacting and defragmenting
     * @returns {Promise<Object>} A promise that resolves with the optimization result
     */
    async function optimizeStorage() {
        try {
            // Check if it's time for optimization
            const now = Date.now();
            if (now - lastOptimization < config.optimizationInterval) {
                debugLog('Skipping storage optimization - not due yet');
                return { skipped: true, reason: 'not_due' };
            }

            // Perform optimization
            let itemsProcessed = 0;
            let bytesReclaimed = 0;

            if (window.StickaraIndexedDB) {
                // Trim memory cache
                const trimResult = window.StickaraIndexedDB.trimMemoryCache(0.3); // Trim 30%
                itemsProcessed += trimResult.removedCount || 0;

                // Advanced optimization techniques can be added here in future versions
            }

            // Update last optimization time
            lastOptimization = now;

            return {
                success: true,
                itemsProcessed: itemsProcessed,
                bytesReclaimed: bytesReclaimed
            };
        } catch (error) {
            console.error('Stickara: Error in storage optimization task:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Compresses uncompressed data in storage
     * @returns {Promise<Object>} A promise that resolves with the compression result
     */
    async function compressData() {
        try {
            // Check if compression is available
            if (!window.StickaraAdvancedCompression && !window.StickaraCompression) {
                return { skipped: true, reason: 'compression_unavailable' };
            }

            const compressionModule = window.StickaraAdvancedCompression || window.StickaraCompression;

            // Get items to compress
            let itemsProcessed = 0;
            let bytesReclaimed = 0;

            if (window.StickaraIndexedDB) {
                // Process highlights
                const highlights = await window.StickaraIndexedDB.getHighlights({
                    limit: 50 // Process in batches
                });

                for (const highlight of highlights) {
                    // Skip already compressed items
                    if (highlight.compressed) continue;

                    // Check if item is large enough to compress
                    const highlightString = JSON.stringify(highlight);
                    if (highlightString.length < config.compressionThreshold) continue;

                    try {
                        // Compress the item
                        const compressedResult = await compressionModule.compress(highlight, {
                            cacheKey: `highlight-${highlight.key}`
                        });

                        if (compressedResult.compressed) {
                            // Update the item with compressed data
                            await window.StickaraIndexedDB.setHighlight({
                                ...highlight,
                                data: compressedResult.data,
                                compressed: true,
                                algorithm: compressedResult.algorithm,
                                originalSize: compressedResult.originalSize,
                                compressedSize: compressedResult.compressedSize
                            });

                            itemsProcessed++;
                            bytesReclaimed += (compressedResult.originalSize - compressedResult.compressedSize);
                        }
                    } catch (error) {
                        console.warn(`Stickara: Error compressing highlight ${highlight.key}:`, error);
                    }

                    // Check if we should pause (user activity)
                    if (!isRunning) break;
                }

                // Process notes (similar to highlights)
                if (isRunning) {
                    const notes = await window.StickaraIndexedDB.getNotes({
                        limit: 50
                    });

                    for (const note of notes) {
                        // Skip already compressed items
                        if (note.compressed) continue;

                        // Check if item is large enough to compress
                        const noteString = JSON.stringify(note);
                        if (noteString.length < config.compressionThreshold) continue;

                        try {
                            // Compress the item
                            const compressedResult = await compressionModule.compress(note, {
                                cacheKey: `note-${note.key}`
                            });

                            if (compressedResult.compressed) {
                                // Update the item with compressed data
                                await window.StickaraIndexedDB.setNote({
                                    ...note,
                                    data: compressedResult.data,
                                    compressed: true,
                                    algorithm: compressedResult.algorithm,
                                    originalSize: compressedResult.originalSize,
                                    compressedSize: compressedResult.compressedSize
                                });

                                itemsProcessed++;
                                bytesReclaimed += (compressedResult.originalSize - compressedResult.compressedSize);
                            }
                        } catch (error) {
                            console.warn(`Stickara: Error compressing note ${note.key}:`, error);
                        }

                        // Check if we should pause (user activity)
                        if (!isRunning) break;
                    }
                }
            }

            return {
                success: true,
                itemsProcessed: itemsProcessed,
                bytesReclaimed: bytesReclaimed
            };
        } catch (error) {
            console.error('Stickara: Error in data compression task:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Checks data integrity and repairs issues
     * @returns {Promise<Object>} A promise that resolves with the integrity check result
     */
    async function checkDataIntegrity() {
        try {
            let itemsProcessed = 0;
            let issuesFixed = 0;

            if (window.StickaraIndexedDB) {
                // Check highlights
                const highlights = await window.StickaraIndexedDB.getHighlights({
                    limit: 100
                });

                for (const highlight of highlights) {
                    itemsProcessed++;

                    // Check for required fields
                    const requiredFields = ['key', 'url', 'text', 'timestamp'];
                    const missingFields = requiredFields.filter(field => !highlight[field]);

                    if (missingFields.length > 0) {
                        debugLog(`Highlight ${highlight.key} missing fields: ${missingFields.join(', ')}`);

                        // Try to repair
                        const fixedHighlight = { ...highlight };

                        // Add missing fields with default values
                        if (!fixedHighlight.timestamp) fixedHighlight.timestamp = Date.now();
                        if (!fixedHighlight.lastModified) fixedHighlight.lastModified = Date.now();

                        // Save fixed highlight
                        if (missingFields.filter(f => f !== 'url' && f !== 'text').length === 0) {
                            await window.StickaraIndexedDB.setHighlight(fixedHighlight);
                            issuesFixed++;
                        }
                    }

                    // Check if we should pause (user activity)
                    if (!isRunning) break;
                }

                // Check notes (similar to highlights)
                if (isRunning) {
                    const notes = await window.StickaraIndexedDB.getNotes({
                        limit: 100
                    });

                    for (const note of notes) {
                        itemsProcessed++;

                        // Check for required fields
                        const requiredFields = ['key', 'text', 'timestamp'];
                        const missingFields = requiredFields.filter(field => !note[field]);

                        if (missingFields.length > 0) {
                            debugLog(`Note ${note.key} missing fields: ${missingFields.join(', ')}`);

                            // Try to repair
                            const fixedNote = { ...note };

                            // Add missing fields with default values
                            if (!fixedNote.timestamp) fixedNote.timestamp = Date.now();
                            if (!fixedNote.lastModified) fixedNote.lastModified = Date.now();

                            // Save fixed note
                            if (missingFields.filter(f => f !== 'text').length === 0) {
                                await window.StickaraIndexedDB.setNote(fixedNote);
                                issuesFixed++;
                            }
                        }

                        // Check if we should pause (user activity)
                        if (!isRunning) break;
                    }
                }
            }

            return {
                success: true,
                itemsProcessed: itemsProcessed,
                issuesFixed: issuesFixed
            };
        } catch (error) {
            console.error('Stickara: Error in data integrity check task:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Creates a backup of important data
     * @returns {Promise<Object>} A promise that resolves with the backup result
     */
    async function createBackup() {
        try {
            // Check if it's time for backup
            const now = Date.now();
            if (now - lastBackup < config.cleanupInterval) { // Use same interval as cleanup
                debugLog('Skipping backup - not due yet');
                return { skipped: true, reason: 'not_due' };
            }

            let itemsProcessed = 0;

            if (window.StickaraIndexedDB) {
                // Backup settings
                const settings = await window.StickaraIndexedDB.getMetadata('settings');
                if (settings) {
                    await window.StickaraIndexedDB.createBackup({
                        type: 'settings',
                        data: settings,
                        timestamp: now
                    });
                    itemsProcessed++;
                }

                // Backup notebooks
                const notebooks = await window.StickaraIndexedDB.getMetadata('notebooks');
                if (notebooks) {
                    await window.StickaraIndexedDB.createBackup({
                        type: 'notebooks',
                        data: notebooks,
                        timestamp: now
                    });
                    itemsProcessed++;
                }

                // Get highlight count
                const highlightCount = await window.StickaraIndexedDB.countHighlights();

                // Only backup highlights if there's a reasonable number
                if (highlightCount && highlightCount < 1000) {
                    const highlights = await window.StickaraIndexedDB.getHighlights();
                    await window.StickaraIndexedDB.createBackup({
                        type: 'highlights',
                        data: highlights,
                        timestamp: now,
                        count: highlights.length
                    });
                    itemsProcessed++;
                }

                // Get note count
                const noteCount = await window.StickaraIndexedDB.countNotes();

                // Only backup notes if there's a reasonable number
                if (noteCount && noteCount < 1000) {
                    const notes = await window.StickaraIndexedDB.getNotes();
                    await window.StickaraIndexedDB.createBackup({
                        type: 'notes',
                        data: notes,
                        timestamp: now,
                        count: notes.length
                    });
                    itemsProcessed++;
                }
            }

            // Update last backup time
            lastBackup = now;

            return {
                success: true,
                itemsProcessed: itemsProcessed
            };
        } catch (error) {
            console.error('Stickara: Error in backup task:', error);
            return { success: false, error: error.message };
        }
    }

    // Initialize the background optimizer when loaded
    init().catch(error => {
        console.error('Stickara: Failed to initialize background optimizer:', error);
    });

    // Return the public API
    return {
        // Core operations
        init,
        addTask,
        startTasks,
        pauseTasks,

        // Task functions
        cleanupCache,
        optimizeStorage,
        compressData,
        checkDataIntegrity,
        createBackup,

        // Constants
        TASK_TYPES,
        PRIORITIES,

        // Status information
        getStats: () => ({ ...stats }),
        getTaskQueue: () => [...taskQueue],
        isRunning: () => isRunning,

        // Configuration
        updateConfig: (newConfig) => {
            Object.assign(config, newConfig);
            return { ...config };
        },
        getConfig: () => ({ ...config })
    };
})();

console.log("Stickara: Background Optimizer Loaded");
