/**
 * Stickara Advanced Compression Module
 * Provides high-efficiency compression algorithms for data storage
 * with support for LZMA, Brotli, and other advanced compression methods
 */

// Create a namespace to avoid global pollution
window.StickaraAdvancedCompression = (function() {
    // Compression algorithms
    const ALGORITHMS = {
        LZMA: 'lzma',               // LZMA compression (high compression ratio)
        BROTLI: 'brotli',           // Brotli compression (Google's algorithm)
        ZSTD: 'zstd',               // Zstandard compression (Facebook's algorithm)
        LZ4: 'lz4',                 // LZ4 compression (fast compression)
        DEFLATE: 'deflate',         // DEFLATE compression (zlib)
        LZ_STRING: 'lz-string',     // LZ-based string compression (fallback)
        NONE: 'none'                // No compression
    };

    // Algorithm capabilities
    const CAPABILITIES = {
        [ALGORITHMS.LZMA]: { ratio: 5, speed: 1, browser: false },      // High ratio, slow speed
        [ALGORITHMS.BROTLI]: { ratio: 4, speed: 3, browser: false },    // Good ratio, decent speed
        [ALGORITHMS.ZSTD]: { ratio: 3, speed: 4, browser: false },      // Balanced ratio/speed
        [ALGORITHMS.LZ4]: { ratio: 2, speed: 5, browser: false },       // Fast, lower ratio
        [ALGORITHMS.DEFLATE]: { ratio: 3, speed: 3, browser: true },    // Balanced, widely supported
        [ALGORITHMS.LZ_STRING]: { ratio: 2, speed: 4, browser: true }   // Decent, pure JS implementation
    };

    // Compression levels
    const LEVELS = {
        FASTEST: 1,     // Prioritize speed over compression ratio
        FAST: 3,        // Fast compression with decent ratio
        BALANCED: 5,    // Balance between speed and ratio
        HIGH: 7,        // Higher compression ratio
        MAX: 9          // Maximum compression ratio
    };

    // Configuration
    const config = {
        defaultAlgorithm: ALGORITHMS.DEFLATE,    // Default compression algorithm
        fallbackAlgorithm: ALGORITHMS.LZ_STRING, // Fallback algorithm if preferred is unavailable
        compressionLevel: LEVELS.BALANCED,       // Default compression level
        adaptiveCompression: true,               // Adapt algorithm based on data characteristics
        compressionThreshold: 1024,              // Minimum size in bytes to compress
        useWorker: true,                         // Use web worker for compression
        cacheCompressedData: true,               // Cache compressed data to avoid recompression
        debug: false                             // Debug mode (disabled for production)
    };

    // Private variables
    const algorithmLibraries = {};               // Loaded compression libraries
    const compressionCache = new Map();          // Cache for compressed data
    const MAX_CACHE_SIZE = 50;                   // Maximum number of cached items
    let isInitialized = false;                   // Whether the module is initialized

    /**
     * Log messages when debug mode is enabled
     * @param {...any} args - Arguments to log
     */
    function debugLog(...args) {
        if (config.debug) {
            console.log('[StickaraAdvancedCompression]', ...args);
        }
    }

    /**
     * Initializes the compression module
     * @returns {Promise<boolean>} A promise that resolves with true if initialization was successful
     */
    async function init() {
        if (isInitialized) return true;

        try {
            // Try to load compression libraries
            await Promise.all([
                loadLZMALibrary(),
                loadBrotliLibrary(),
                loadZstdLibrary(),
                loadLZ4Library(),
                loadDeflateLibrary(),
                loadLZStringLibrary()
            ]);

            // Determine available algorithms
            const available = Object.keys(ALGORITHMS).filter(algo =>
                algo === ALGORITHMS.NONE || algorithmLibraries[ALGORITHMS[algo]]
            );

            debugLog('Available compression algorithms:', available);

            // Set initialized flag
            isInitialized = true;
            return true;
        } catch (error) {
            console.error('Stickara: Error initializing advanced compression module:', error);
            return false;
        }
    }

    /**
     * Loads the LZMA library
     * @returns {Promise<boolean>} A promise that resolves with true if successful
     */
    async function loadLZMALibrary() {
        try {
            if (window.LZMA) {
                algorithmLibraries[ALGORITHMS.LZMA] = window.LZMA;
                debugLog('LZMA library already loaded');
                return true;
            }

            // Try to dynamically load LZMA
            await loadScript('https://cdn.jsdelivr.net/npm/lzma@2.3.2/src/lzma.min.js');

            if (window.LZMA) {
                algorithmLibraries[ALGORITHMS.LZMA] = window.LZMA;
                debugLog('LZMA library loaded dynamically');
                return true;
            }

            return false;
        } catch (e) {
            debugLog('Failed to load LZMA library:', e);
            return false;
        }
    }

    /**
     * Loads the Brotli library
     * @returns {Promise<boolean>} A promise that resolves with true if successful
     */
    async function loadBrotliLibrary() {
        try {
            if (window.Brotli) {
                algorithmLibraries[ALGORITHMS.BROTLI] = window.Brotli;
                debugLog('Brotli library already loaded');
                return true;
            }

            // Try to dynamically load Brotli
            await loadScript('https://cdn.jsdelivr.net/npm/brotli/build/encode.min.js');
            await loadScript('https://cdn.jsdelivr.net/npm/brotli/build/decode.min.js');

            if (window.Brotli) {
                algorithmLibraries[ALGORITHMS.BROTLI] = window.Brotli;
                debugLog('Brotli library loaded dynamically');
                return true;
            }

            return false;
        } catch (e) {
            debugLog('Failed to load Brotli library:', e);
            return false;
        }
    }

    /**
     * Loads the Zstandard library
     * @returns {Promise<boolean>} A promise that resolves with true if successful
     */
    async function loadZstdLibrary() {
        // Zstandard is not widely available in browser environments
        // This is a placeholder for future implementation
        return false;
    }

    /**
     * Loads the LZ4 library
     * @returns {Promise<boolean>} A promise that resolves with true if successful
     */
    async function loadLZ4Library() {
        try {
            if (window.LZ4) {
                algorithmLibraries[ALGORITHMS.LZ4] = window.LZ4;
                debugLog('LZ4 library already loaded');
                return true;
            }

            // Try to dynamically load LZ4
            await loadScript('https://cdn.jsdelivr.net/npm/lz4js/build/lz4.min.js');

            if (window.LZ4) {
                algorithmLibraries[ALGORITHMS.LZ4] = window.LZ4;
                debugLog('LZ4 library loaded dynamically');
                return true;
            }

            return false;
        } catch (e) {
            debugLog('Failed to load LZ4 library:', e);
            return false;
        }
    }

    /**
     * Loads the DEFLATE library (pako)
     * @returns {Promise<boolean>} A promise that resolves with true if successful
     */
    async function loadDeflateLibrary() {
        try {
            if (window.pako) {
                algorithmLibraries[ALGORITHMS.DEFLATE] = window.pako;
                debugLog('DEFLATE library (pako) already loaded');
                return true;
            }

            // Try to dynamically load pako
            await loadScript('https://cdn.jsdelivr.net/npm/pako@2.1.0/dist/pako.min.js');

            if (window.pako) {
                algorithmLibraries[ALGORITHMS.DEFLATE] = window.pako;
                debugLog('DEFLATE library (pako) loaded dynamically');
                return true;
            }

            return false;
        } catch (e) {
            debugLog('Failed to load DEFLATE library (pako):', e);
            return false;
        }
    }

    /**
     * Loads the LZ-String library
     * @returns {Promise<boolean>} A promise that resolves with true if successful
     */
    async function loadLZStringLibrary() {
        try {
            if (window.LZString) {
                algorithmLibraries[ALGORITHMS.LZ_STRING] = window.LZString;
                debugLog('LZ-String library already loaded');
                return true;
            }

            // Try to dynamically load LZ-String
            await loadScript('https://cdn.jsdelivr.net/npm/lz-string@1.4.4/libs/lz-string.min.js');

            if (window.LZString) {
                algorithmLibraries[ALGORITHMS.LZ_STRING] = window.LZString;
                debugLog('LZ-String library loaded dynamically');
                return true;
            }

            return false;
        } catch (e) {
            debugLog('Failed to load LZ-String library:', e);
            return false;
        }
    }

    /**
     * Loads a script dynamically
     * @param {string} url - The URL of the script to load
     * @returns {Promise<void>} A promise that resolves when the script is loaded
     */
    function loadScript(url) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = url;
            script.onload = () => resolve();
            script.onerror = (error) => reject(new Error(`Failed to load script: ${url}`));
            document.head.appendChild(script);
        });
    }

    /**
     * Selects the best compression algorithm for the given data
     * @param {string} data - The data to compress
     * @param {Object} options - Selection options
     * @returns {string} The selected algorithm
     */
    function selectAlgorithm(data, options = {}) {
        const {
            prioritizeSpeed = false,
            prioritizeRatio = false,
            forceAlgorithm = null
        } = options;

        // If a specific algorithm is forced, use it if available
        if (forceAlgorithm && algorithmLibraries[forceAlgorithm]) {
            return forceAlgorithm;
        }

        // If data is small, use a fast algorithm or no compression
        if (data.length < config.compressionThreshold) {
            return ALGORITHMS.NONE;
        }

        // Get available algorithms
        const available = Object.keys(ALGORITHMS)
            .filter(algo => algo !== ALGORITHMS.NONE && algorithmLibraries[ALGORITHMS[algo]])
            .map(algo => ALGORITHMS[algo]);

        if (available.length === 0) {
            return ALGORITHMS.NONE;
        }

        // If only one algorithm is available, use it
        if (available.length === 1) {
            return available[0];
        }

        // Select based on priorities
        if (prioritizeSpeed) {
            // Sort by speed (descending)
            available.sort((a, b) => CAPABILITIES[b].speed - CAPABILITIES[a].speed);
        } else if (prioritizeRatio) {
            // Sort by compression ratio (descending)
            available.sort((a, b) => CAPABILITIES[b].ratio - CAPABILITIES[a].ratio);
        } else {
            // Balance speed and ratio
            available.sort((a, b) => {
                const scoreA = CAPABILITIES[a].speed * CAPABILITIES[a].ratio;
                const scoreB = CAPABILITIES[b].speed * CAPABILITIES[b].ratio;
                return scoreB - scoreA;
            });
        }

        return available[0];
    }

    // Initialize the compression module when loaded
    init().catch(error => {
        console.error('Stickara: Failed to initialize advanced compression module:', error);
    });

    /**
     * Compresses data using the specified or best available algorithm
     * @param {any} data - The data to compress
     * @param {Object} options - Compression options
     * @returns {Promise<Object>} A promise that resolves with the compressed data
     */
    async function compress(data, options = {}) {
        await init();

        const {
            algorithm = null,
            level = config.compressionLevel,
            useWorker = config.useWorker,
            cacheKey = null,
            metadata = {}
        } = options;

        // Convert data to string if it's not already
        const dataString = typeof data === 'string' ? data : JSON.stringify(data);

        // Check if data is large enough to compress
        if (dataString.length < config.compressionThreshold) {
            debugLog(`Data too small to compress (${dataString.length} bytes)`);
            return {
                compressed: false,
                data: data,
                algorithm: ALGORITHMS.NONE,
                originalSize: dataString.length,
                compressedSize: dataString.length,
                metadata
            };
        }

        // Check cache if enabled and a cache key is provided
        if (config.cacheCompressedData && cacheKey && compressionCache.has(cacheKey)) {
            const cachedResult = compressionCache.get(cacheKey);
            debugLog(`Using cached compressed data for key: ${cacheKey}`);
            return cachedResult;
        }

        // Select the best algorithm if none specified
        const selectedAlgorithm = algorithm || selectAlgorithm(dataString, options);

        // If no compression is selected, return uncompressed data
        if (selectedAlgorithm === ALGORITHMS.NONE) {
            return {
                compressed: false,
                data: data,
                algorithm: ALGORITHMS.NONE,
                originalSize: dataString.length,
                compressedSize: dataString.length,
                metadata
            };
        }

        // Try to use web worker if enabled
        if (useWorker && window.StickaraWorkerManager) {
            try {
                const result = await window.StickaraWorkerManager.runTask(
                    'workers/compression-worker.js',
                    'compressData',
                    {
                        input: dataString,
                        algorithm: selectedAlgorithm,
                        level,
                        options
                    }
                );

                if (result && result.compressed) {
                    debugLog(`Compressed data with ${selectedAlgorithm} using worker (${result.originalSize} → ${result.compressedSize} bytes)`);

                    const compressedResult = {
                        compressed: true,
                        data: result.compressed,
                        algorithm: selectedAlgorithm,
                        originalSize: result.originalSize,
                        compressedSize: result.compressedSize,
                        compressionRatio: result.compressionRatio,
                        metadata
                    };

                    // Cache the result if caching is enabled
                    if (config.cacheCompressedData && cacheKey) {
                        cacheCompressedData(cacheKey, compressedResult);
                    }

                    return compressedResult;
                }
            } catch (error) {
                console.warn(`Stickara: Error compressing data with worker (${selectedAlgorithm}):`, error);
                // Fall back to in-thread compression
            }
        }

        // Compress in the main thread
        try {
            let compressed;
            let compressedSize;

            // Get the library for the selected algorithm
            const library = algorithmLibraries[selectedAlgorithm];

            if (!library) {
                throw new Error(`Compression library not available for algorithm: ${selectedAlgorithm}`);
            }

            switch (selectedAlgorithm) {
                case ALGORITHMS.LZMA:
                    compressed = await compressWithLZMA(dataString, level);
                    compressedSize = compressed.length;
                    break;

                case ALGORITHMS.BROTLI:
                    compressed = await compressWithBrotli(dataString, level);
                    compressedSize = compressed.length;
                    break;

                case ALGORITHMS.ZSTD:
                    compressed = await compressWithZstd(dataString, level);
                    compressedSize = compressed.length;
                    break;

                case ALGORITHMS.LZ4:
                    compressed = await compressWithLZ4(dataString);
                    compressedSize = compressed.length;
                    break;

                case ALGORITHMS.DEFLATE:
                    compressed = await compressWithDeflate(dataString, level);
                    compressedSize = compressed.length;
                    break;

                case ALGORITHMS.LZ_STRING:
                    compressed = library.compressToUTF16(dataString);
                    compressedSize = compressed.length * 2; // UTF-16 uses 2 bytes per character
                    break;

                default:
                    throw new Error(`Unknown compression algorithm: ${selectedAlgorithm}`);
            }

            const originalSize = dataString.length;
            const compressionRatio = originalSize / compressedSize;

            debugLog(`Compressed data with ${selectedAlgorithm} (${originalSize} → ${compressedSize} bytes, ratio: ${compressionRatio.toFixed(2)})`);

            const compressedResult = {
                compressed: true,
                data: compressed,
                algorithm: selectedAlgorithm,
                originalSize,
                compressedSize,
                compressionRatio,
                metadata
            };

            // Cache the result if caching is enabled
            if (config.cacheCompressedData && cacheKey) {
                cacheCompressedData(cacheKey, compressedResult);
            }

            return compressedResult;
        } catch (error) {
            console.error(`Stickara: Error compressing data with ${selectedAlgorithm}:`, error);

            // Try fallback algorithm if different from the selected one
            if (config.fallbackAlgorithm && config.fallbackAlgorithm !== selectedAlgorithm) {
                try {
                    debugLog(`Trying fallback algorithm: ${config.fallbackAlgorithm}`);
                    return await compress(data, {
                        ...options,
                        algorithm: config.fallbackAlgorithm
                    });
                } catch (fallbackError) {
                    console.error(`Stickara: Fallback compression also failed:`, fallbackError);
                }
            }

            // Return uncompressed data as last resort
            return {
                compressed: false,
                data: data,
                algorithm: ALGORITHMS.NONE,
                originalSize: dataString.length,
                compressedSize: dataString.length,
                error: error.message,
                metadata
            };
        }
    }

    /**
     * Decompresses data using the specified algorithm
     * @param {Object} compressedData - The compressed data object
     * @param {Object} options - Decompression options
     * @returns {Promise<any>} A promise that resolves with the decompressed data
     */
    async function decompress(compressedData, options = {}) {
        await init();

        const { useWorker = config.useWorker } = options;

        // If data is not compressed, return as is
        if (!compressedData.compressed) {
            return compressedData.data;
        }

        const { data, algorithm } = compressedData;

        // Ensure the algorithm is available
        if (algorithm !== ALGORITHMS.NONE && !algorithmLibraries[algorithm]) {
            throw new Error(`Decompression algorithm not available: ${algorithm}`);
        }

        // Try to use web worker if enabled
        if (useWorker && window.StickaraWorkerManager) {
            try {
                const result = await window.StickaraWorkerManager.runTask(
                    'workers/compression-worker.js',
                    'decompressData',
                    { input: data, algorithm, options }
                );

                if (result && result.decompressed) {
                    debugLog(`Decompressed data with ${algorithm} using worker`);

                    // Parse JSON if the result is a string that looks like JSON
                    if (typeof result.decompressed === 'string' &&
                        (result.decompressed.startsWith('{') || result.decompressed.startsWith('['))) {
                        try {
                            return JSON.parse(result.decompressed);
                        } catch (e) {
                            // If parsing fails, return the string
                            return result.decompressed;
                        }
                    }

                    return result.decompressed;
                }
            } catch (error) {
                console.warn(`Stickara: Error decompressing data with worker (${algorithm}):`, error);
                // Fall back to in-thread decompression
            }
        }

        // Decompress in the main thread
        try {
            let decompressed;

            // Get the library for the algorithm
            const library = algorithmLibraries[algorithm];

            switch (algorithm) {
                case ALGORITHMS.LZMA:
                    decompressed = await decompressWithLZMA(data);
                    break;

                case ALGORITHMS.BROTLI:
                    decompressed = await decompressWithBrotli(data);
                    break;

                case ALGORITHMS.ZSTD:
                    decompressed = await decompressWithZstd(data);
                    break;

                case ALGORITHMS.LZ4:
                    decompressed = await decompressWithLZ4(data);
                    break;

                case ALGORITHMS.DEFLATE:
                    decompressed = await decompressWithDeflate(data);
                    break;

                case ALGORITHMS.LZ_STRING:
                    decompressed = library.decompressFromUTF16(data);
                    break;

                default:
                    throw new Error(`Unknown decompression algorithm: ${algorithm}`);
            }

            debugLog(`Decompressed data with ${algorithm}`);

            // Parse JSON if the result is a string that looks like JSON
            if (typeof decompressed === 'string' &&
                (decompressed.startsWith('{') || decompressed.startsWith('['))) {
                try {
                    return JSON.parse(decompressed);
                } catch (e) {
                    // If parsing fails, return the string
                    return decompressed;
                }
            }

            return decompressed;
        } catch (error) {
            console.error(`Stickara: Error decompressing data with ${algorithm}:`, error);

            // Return compressed data as fallback
            return compressedData.data;
        }
    }

    /**
     * Compresses data using LZMA
     * @param {string} data - The data to compress
     * @param {number} level - Compression level
     * @returns {Promise<string>} A promise that resolves with the compressed data
     */
    function compressWithLZMA(data, level) {
        return new Promise((resolve, reject) => {
            try {
                const library = algorithmLibraries[ALGORITHMS.LZMA];

                // Map our level (1-9) to LZMA level (1-9)
                const lzmaLevel = Math.max(1, Math.min(9, level));

                library.compress(data, lzmaLevel, (result, error) => {
                    if (error) {
                        reject(new Error(`LZMA compression error: ${error}`));
                    } else {
                        // Convert Uint8Array to Base64 string for storage
                        const base64 = uint8ArrayToBase64(result);
                        resolve(base64);
                    }
                });
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Decompresses data using LZMA
     * @param {string} data - The compressed data (Base64 string)
     * @returns {Promise<string>} A promise that resolves with the decompressed data
     */
    function decompressWithLZMA(data) {
        return new Promise((resolve, reject) => {
            try {
                const library = algorithmLibraries[ALGORITHMS.LZMA];

                // Convert Base64 string back to Uint8Array
                const uint8Array = base64ToUint8Array(data);

                library.decompress(uint8Array, (result, error) => {
                    if (error) {
                        reject(new Error(`LZMA decompression error: ${error}`));
                    } else {
                        resolve(result);
                    }
                });
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Compresses data using Brotli
     * @param {string} data - The data to compress
     * @param {number} level - Compression level
     * @returns {Promise<string>} A promise that resolves with the compressed data
     */
    function compressWithBrotli(data, level) {
        return new Promise((resolve, reject) => {
            try {
                const library = algorithmLibraries[ALGORITHMS.BROTLI];

                // Map our level (1-9) to Brotli quality (0-11)
                const quality = Math.floor((level / 9) * 11);

                // Convert string to Uint8Array
                const encoder = new TextEncoder();
                const uint8Array = encoder.encode(data);

                // Compress with Brotli
                const compressed = library.compress(uint8Array, { quality });

                // Convert Uint8Array to Base64 string for storage
                const base64 = uint8ArrayToBase64(compressed);
                resolve(base64);
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Decompresses data using Brotli
     * @param {string} data - The compressed data (Base64 string)
     * @returns {Promise<string>} A promise that resolves with the decompressed data
     */
    function decompressWithBrotli(data) {
        return new Promise((resolve, reject) => {
            try {
                const library = algorithmLibraries[ALGORITHMS.BROTLI];

                // Convert Base64 string back to Uint8Array
                const uint8Array = base64ToUint8Array(data);

                // Decompress with Brotli
                const decompressed = library.decompress(uint8Array);

                // Convert Uint8Array back to string
                const decoder = new TextDecoder();
                const result = decoder.decode(decompressed);

                resolve(result);
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Compresses data using Zstandard (placeholder)
     * @param {string} data - The data to compress
     * @param {number} level - Compression level
     * @returns {Promise<string>} A promise that resolves with the compressed data
     */
    function compressWithZstd(data, level) {
        return Promise.reject(new Error('Zstandard compression not implemented'));
    }

    /**
     * Decompresses data using Zstandard (placeholder)
     * @param {string} data - The compressed data
     * @returns {Promise<string>} A promise that resolves with the decompressed data
     */
    function decompressWithZstd(data) {
        return Promise.reject(new Error('Zstandard decompression not implemented'));
    }

    /**
     * Compresses data using LZ4
     * @param {string} data - The data to compress
     * @returns {Promise<string>} A promise that resolves with the compressed data
     */
    function compressWithLZ4(data) {
        return new Promise((resolve, reject) => {
            try {
                const library = algorithmLibraries[ALGORITHMS.LZ4];

                // Convert string to Uint8Array
                const encoder = new TextEncoder();
                const uint8Array = encoder.encode(data);

                // Compress with LZ4
                const compressed = library.compress(uint8Array);

                // Convert Uint8Array to Base64 string for storage
                const base64 = uint8ArrayToBase64(compressed);
                resolve(base64);
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Decompresses data using LZ4
     * @param {string} data - The compressed data (Base64 string)
     * @returns {Promise<string>} A promise that resolves with the decompressed data
     */
    function decompressWithLZ4(data) {
        return new Promise((resolve, reject) => {
            try {
                const library = algorithmLibraries[ALGORITHMS.LZ4];

                // Convert Base64 string back to Uint8Array
                const uint8Array = base64ToUint8Array(data);

                // Decompress with LZ4
                const decompressed = library.decompress(uint8Array);

                // Convert Uint8Array back to string
                const decoder = new TextDecoder();
                const result = decoder.decode(decompressed);

                resolve(result);
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Compresses data using DEFLATE (pako)
     * @param {string} data - The data to compress
     * @param {number} level - Compression level
     * @returns {Promise<string>} A promise that resolves with the compressed data
     */
    function compressWithDeflate(data, level) {
        return new Promise((resolve, reject) => {
            try {
                const library = algorithmLibraries[ALGORITHMS.DEFLATE];

                // Map our level (1-9) to pako level (1-9)
                const pakoLevel = Math.max(1, Math.min(9, level));

                // Compress with pako
                const compressed = library.deflate(data, { level: pakoLevel });

                // Convert Uint8Array to Base64 string for storage
                const base64 = uint8ArrayToBase64(compressed);
                resolve(base64);
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Decompresses data using DEFLATE (pako)
     * @param {string} data - The compressed data (Base64 string)
     * @returns {Promise<string>} A promise that resolves with the decompressed data
     */
    function decompressWithDeflate(data) {
        return new Promise((resolve, reject) => {
            try {
                const library = algorithmLibraries[ALGORITHMS.DEFLATE];

                // Convert Base64 string back to Uint8Array
                const uint8Array = base64ToUint8Array(data);

                // Decompress with pako
                const decompressed = library.inflate(uint8Array, { to: 'string' });
                resolve(decompressed);
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Converts a Uint8Array to a Base64 string
     * @param {Uint8Array} buffer - The buffer to convert
     * @returns {string} The Base64 string
     */
    function uint8ArrayToBase64(buffer) {
        let binary = '';
        const len = buffer.length;
        for (let i = 0; i < len; i++) {
            binary += String.fromCharCode(buffer[i]);
        }
        return btoa(binary);
    }

    /**
     * Converts a Base64 string to a Uint8Array
     * @param {string} base64 - The Base64 string to convert
     * @returns {Uint8Array} The Uint8Array
     */
    function base64ToUint8Array(base64) {
        const binary = atob(base64);
        const len = binary.length;
        const bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
            bytes[i] = binary.charCodeAt(i);
        }
        return bytes;
    }

    /**
     * Caches compressed data
     * @param {string} key - The cache key
     * @param {Object} data - The compressed data
     */
    function cacheCompressedData(key, data) {
        // Limit cache size
        if (compressionCache.size >= MAX_CACHE_SIZE) {
            // Remove oldest entry
            const oldestKey = compressionCache.keys().next().value;
            compressionCache.delete(oldestKey);
        }

        // Add to cache
        compressionCache.set(key, data);
    }

    /**
     * Determines if data should be compressed
     * @param {any} data - The data to check
     * @param {string} key - The data key
     * @returns {boolean} True if the data should be compressed
     */
    function shouldCompress(data, key) {
        // Skip compression for small data
        if (typeof data === 'string' && data.length < config.compressionThreshold) {
            return false;
        }

        // Skip compression for objects that are likely to be small
        if (typeof data === 'object' && data !== null) {
            const json = JSON.stringify(data);
            if (json.length < config.compressionThreshold) {
                return false;
            }
        }

        return true;
    }

    // Return the public API
    return {
        // Core compression operations
        init,
        compress,
        decompress,
        selectAlgorithm,
        shouldCompress,

        // Constants
        ALGORITHMS,
        LEVELS,
        CAPABILITIES,

        // Configuration
        updateConfig: (newConfig) => {
            Object.assign(config, newConfig);
            return { ...config };
        },
        getConfig: () => ({ ...config }),

        // Status information
        getAvailableAlgorithms: () => Object.keys(ALGORITHMS).filter(algo =>
            algo === ALGORITHMS.NONE || algorithmLibraries[ALGORITHMS[algo]]
        ),
        isAlgorithmAvailable: (algorithm) =>
            algorithm === ALGORITHMS.NONE || !!algorithmLibraries[algorithm]
    };
})();

console.log("Stickara: Advanced Compression Module Loaded");
