// --- START OF FILE threat-detection.js ---

/**
 * Advanced Threat Detection and Prevention for Stickara
 *
 * This module provides runtime security monitoring, detecting and preventing
 * various types of attacks and suspicious activities.
 */

// Create a namespace to avoid global pollution
window.StickaraThreatDetection = (function() {

    /**
     * Threat detection configuration
     */
    const CONFIG = {
        // How often to run periodic checks (in milliseconds) - increased to reduce false positives
        checkInterval: 60000, // Increased from 30 seconds to 60 seconds

        // Maximum number of events to store in history
        maxEventHistory: 100,

        // Thresholds for various threat types - increased to reduce false positives from normal Stickara operations
        thresholds: {
            xssAttempts: 10, // Increased from 3 to 10
            domManipulation: 25, // Increased from 5 to 25
            apiAbuse: 20, // Increased from 10 to 20
            storageManipulation: 15 // Increased from 5 to 15
        }
    };

    /**
     * History of detected security events
     */
    const securityEventHistory = [];

    /**
     * Interval ID for periodic checks
     */
    let checkIntervalId = null;

    /**
     * Flag to indicate if threat detection is paused
     */
    let isPaused = false;

    /**
     * Whitelist of Stickara operations that should not trigger threat detection
     */
    const STICKARA_WHITELIST = {
        // DOM elements that <PERSON><PERSON> is allowed to create/modify
        allowedElements: ['mark', 'div', 'button', 'span', 'textarea', 'input', 'select', 'option'],

        // CSS classes that indicate Stickara operations
        allowedClasses: [
            'Stickara-highlight', 'Stickara-delete-highlight-btn', 'Stickara-note-highlight-btn',
            'Stickara-highlight-note-display', 'Stickara-note-drag-handle', 'cross-paragraph-segment',
            'Stickara-container', 'Stickara-text', 'Stickara-note-editor-overlay',
            'Stickara-close-btn', 'Stickara-btn-primary', 'Stickara-btn-secondary', 'Stickara-option-group',
            'Stickara-copy-btn', 'Stickara-notification'
        ],

        // Storage keys that Stickara is allowed to access
        allowedStorageKeys: [
            'Stickara_highlights_', 'Stickara_', 'stickara_', 'voiceApiKeys', 'encryptedVoiceApiKeys',
            'Stickara_auth_token', 'Stickara_refresh_token', 'Stickara_user_id'
        ],

        // URLs that are safe for extension operations
        allowedUrls: [
            'https://stickara.com/', 'https://api.stickara.com/', 'file://', 'localhost', '127.0.0.1'
        ]
    };

    /**
     * Original values of critical functions to detect tampering
     */
    const originalFunctions = {
        // DOM manipulation
        createElement: Document.prototype.createElement,
        appendChild: Node.prototype.appendChild,
        removeChild: Node.prototype.removeChild,
        replaceChild: Node.prototype.replaceChild,
        insertBefore: Node.prototype.insertBefore,
        setAttribute: Element.prototype.setAttribute,

        // Storage
        setItem: Storage.prototype.setItem,
        getItem: Storage.prototype.getItem,
        removeItem: Storage.prototype.removeItem,

        // Chrome APIs
        sendMessage: chrome.runtime.sendMessage,

        // Eval and Function constructor
        eval: window.eval,
        Function: window.Function
    };

    /**
     * Patterns for detecting XSS attempts
     */
    const XSS_PATTERNS = [
        /<script\b[^>]*>([\s\S]*?)<\/script>/gi,
        /javascript:/gi,
        /eval\s*\(/gi,
        /on\w+\s*=\s*["']?[^"']*["']?/gi,
        /setTimeout\s*\(\s*["']/gi,
        /setInterval\s*\(\s*["']/gi,
        /document\.write\s*\(/gi,
        /document\.execCommand\s*\(\s*["']insertHTML/gi,
        /Function\s*\(\s*["']/gi,
        /fromCharCode/gi,
        /data:text\/html/gi
    ];

    /**
     * Checks if an operation is from Stickara and should be whitelisted
     * @param {Object} context - Context information about the operation
     * @returns {boolean} - True if the operation should be whitelisted
     */
    function isStickaraOperation(context) {
        try {
            // Check if paused (all operations allowed when paused)
            if (isPaused) {
                return true;
            }

            // Check DOM operations
            if (context.element) {
                const element = context.element;

                // Check if element has Stickara classes
                if (element.className && typeof element.className === 'string') {
                    for (const allowedClass of STICKARA_WHITELIST.allowedClasses) {
                        if (element.className.includes(allowedClass)) {
                            return true;
                        }
                    }
                }

                // Check if element is within Stickara container
                if (element.closest && (
                    element.closest('.Stickara-highlight') ||
                    element.closest('#Stickara-container') ||
                    element.closest('.Stickara-note-editor-overlay') ||
                    element.closest('.Stickara-sharing-panel') ||
                    element.closest('.Stickara-notification')
                )) {
                    return true;
                }

                // Check if element type is allowed for Stickara
                if (STICKARA_WHITELIST.allowedElements.includes(element.tagName?.toLowerCase())) {
                    return true;
                }
            }

            // Check storage operations
            if (context.storageKey) {
                for (const allowedKey of STICKARA_WHITELIST.allowedStorageKeys) {
                    if (context.storageKey.includes(allowedKey)) {
                        return true;
                    }
                }
            }

            // Check call stack for Stickara functions
            if (context.stack && typeof context.stack === 'string') {
                if (context.stack.includes('Stickara') ||
                    context.stack.includes('handleHighlight') ||
                    context.stack.includes('applyHighlight') ||
                    context.stack.includes('positionNote')) {
                    return true;
                }
            }

            return false;
        } catch (error) {
            // If there's an error checking, err on the side of caution and allow the operation
            return true;
        }
    }

    /**
     * Initializes the threat detection system
     */
    function initialize() {
        try {
            // Check if extension context is valid before initializing
            if (!isExtensionContextValid()) {
                console.log("Stickara: Extension context invalidated, skipping threat detection initialization");
                return;
            }

            // Check if we're in a content script or background page
            const isContentScript = window.location.href.startsWith('http');

            // In content scripts, use a more limited set of protections
            if (isContentScript) {
                // Only monitor DOM manipulation in content scripts
                monitorDOMManipulation();

                console.log("Stickara: Threat Detection initialized in content script mode");
            } else {
                // In background/popup pages, use full protection
                monitorDOMManipulation();
                monitorStorageAccess();
                monitorEvalUsage();
                monitorChromeAPIs();

                // Start periodic checks
                startPeriodicChecks();

                console.log("Stickara: Threat Detection initialized in full mode");
            }
        } catch (e) {
            // Check if the error is related to extension context invalidation
            if (e.message && e.message.includes('Extension context invalidated')) {
                console.log("Stickara: Extension context invalidated during threat detection initialization");
            } else {
                console.error("Stickara: Error initializing Threat Detection:", e);
            }
        }
    }

    /**
     * Starts periodic security checks
     */
    function startPeriodicChecks() {
        if (checkIntervalId) {
            clearInterval(checkIntervalId);
        }

        checkIntervalId = setInterval(() => {
            try {
                // Check if extension context is still valid before running security checks
                if (!isExtensionContextValid()) {
                    console.log("Stickara: Extension context invalidated, pausing threat detection");
                    pause();
                    return;
                }

                checkForTampering();
                checkForSuspiciousContent();
                checkForAbnormalBehavior();
            } catch (e) {
                // Check if the error is related to extension context invalidation
                if (e.message && e.message.includes('Extension context invalidated')) {
                    console.log("Stickara: Extension context invalidated during security check, pausing threat detection");
                    pause();
                } else {
                    console.error("Stickara: Error in periodic security check:", e);
                }
            }
        }, CONFIG.checkInterval);
    }

    /**
     * Checks for tampering with critical functions
     */
    function checkForTampering() {
        if (isPaused) {
            return; // Skip check if paused
        }

        const tamperedFunctions = [];

        // Check each original function
        for (const [name, originalFn] of Object.entries(originalFunctions)) {
            let currentFn;

            // Get the current function
            if (name === 'createElement') {
                currentFn = Document.prototype.createElement;
            } else if (name === 'appendChild' || name === 'removeChild' || name === 'replaceChild' || name === 'insertBefore') {
                currentFn = Node.prototype[name];
            } else if (name === 'setAttribute') {
                currentFn = Element.prototype.setAttribute;
            } else if (name === 'setItem' || name === 'getItem' || name === 'removeItem') {
                currentFn = Storage.prototype[name];
            } else if (name === 'sendMessage') {
                currentFn = chrome.runtime.sendMessage;
            } else if (name === 'eval') {
                currentFn = window.eval;
            } else if (name === 'Function') {
                currentFn = window.Function;
            }

            // Check if the function has been tampered with
            if (currentFn !== originalFn) {
                tamperedFunctions.push(name);
            }
        }

        // If any functions have been tampered with, log a security event
        if (tamperedFunctions.length > 0) {
            logSecurityEvent('functionTampering', {
                tamperedFunctions,
                severity: 'high'
            });

            // Attempt to restore original functions
            restoreOriginalFunctions(tamperedFunctions);
        }
    }

    /**
     * Restores original functions that have been tampered with
     * @param {Array<string>} tamperedFunctions - Names of tampered functions
     */
    function restoreOriginalFunctions(tamperedFunctions) {
        for (const name of tamperedFunctions) {
            try {
                if (name === 'createElement') {
                    Document.prototype.createElement = originalFunctions.createElement;
                } else if (name === 'appendChild') {
                    Node.prototype.appendChild = originalFunctions.appendChild;
                } else if (name === 'removeChild') {
                    Node.prototype.removeChild = originalFunctions.removeChild;
                } else if (name === 'replaceChild') {
                    Node.prototype.replaceChild = originalFunctions.replaceChild;
                } else if (name === 'insertBefore') {
                    Node.prototype.insertBefore = originalFunctions.insertBefore;
                } else if (name === 'setAttribute') {
                    Element.prototype.setAttribute = originalFunctions.setAttribute;
                } else if (name === 'setItem') {
                    Storage.prototype.setItem = originalFunctions.setItem;
                } else if (name === 'getItem') {
                    Storage.prototype.getItem = originalFunctions.getItem;
                } else if (name === 'removeItem') {
                    Storage.prototype.removeItem = originalFunctions.removeItem;
                } else if (name === 'sendMessage') {
                    chrome.runtime.sendMessage = originalFunctions.sendMessage;
                } else if (name === 'eval') {
                    window.eval = originalFunctions.eval;
                } else if (name === 'Function') {
                    window.Function = originalFunctions.Function;
                }

                console.log(`Stickara: Restored original ${name} function`);
            } catch (e) {
                console.error(`Stickara: Error restoring original ${name} function:`, e);
            }
        }
    }

    /**
     * Checks for suspicious content in the DOM
     */
    function checkForSuspiciousContent() {
        if (isPaused) {
            return; // Skip check if paused
        }

        try {
            // Get all text nodes in the document
            const textNodes = [];
            const walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            while (walker.nextNode()) {
                textNodes.push(walker.currentNode);
            }

            // Check each text node for XSS patterns
            let xssAttempts = 0;

            for (const node of textNodes) {
                const text = node.textContent;

                // Skip if this is within a Stickara component
                if (node.parentElement && isStickaraOperation({ element: node.parentElement })) {
                    continue;
                }

                for (const pattern of XSS_PATTERNS) {
                    if (pattern.test(text)) {
                        xssAttempts++;

                        // Log the security event
                        logSecurityEvent('xssAttempt', {
                            pattern: pattern.toString(),
                            text: text.substring(0, 100), // Limit text length
                            nodeInfo: getNodeInfo(node),
                            severity: 'high'
                        });

                        // Sanitize the node
                        node.textContent = sanitizeText(text);
                    }
                }
            }

            // Check attributes for XSS patterns
            const elements = document.querySelectorAll('*');

            for (const element of elements) {
                for (const attr of element.attributes) {
                    const name = attr.name;
                    const value = attr.value;

                    // Check for event handlers
                    if (name.startsWith('on')) {
                        xssAttempts++;

                        // Log the security event
                        logSecurityEvent('xssAttempt', {
                            type: 'eventHandler',
                            attributeName: name,
                            attributeValue: value.substring(0, 100), // Limit value length
                            elementInfo: getElementInfo(element),
                            severity: 'high'
                        });

                        // Remove the attribute
                        element.removeAttribute(name);
                    }

                    // Check for javascript: URLs
                    if ((name === 'href' || name === 'src') && value.toLowerCase().includes('javascript:')) {
                        xssAttempts++;

                        // Log the security event
                        logSecurityEvent('xssAttempt', {
                            type: 'javascriptUrl',
                            attributeName: name,
                            attributeValue: value.substring(0, 100), // Limit value length
                            elementInfo: getElementInfo(element),
                            severity: 'high'
                        });

                        // Remove the attribute
                        element.removeAttribute(name);
                    }

                    // Check for other XSS patterns in attributes
                    for (const pattern of XSS_PATTERNS) {
                        if (pattern.test(value)) {
                            xssAttempts++;

                            // Log the security event
                            logSecurityEvent('xssAttempt', {
                                type: 'attributeValue',
                                pattern: pattern.toString(),
                                attributeName: name,
                                attributeValue: value.substring(0, 100), // Limit value length
                                elementInfo: getElementInfo(element),
                                severity: 'high'
                            });

                            // Sanitize the attribute
                            element.setAttribute(name, sanitizeText(value));
                        }
                    }
                }
            }

            // If too many XSS attempts are detected, take action
            if (xssAttempts >= CONFIG.thresholds.xssAttempts) {
                handleThreat('xssAttempts', {
                    count: xssAttempts,
                    action: 'sanitize'
                });
            }
        } catch (e) {
            console.error("Stickara: Error checking for suspicious content:", e);
        }
    }

    /**
     * Checks for abnormal behavior patterns
     */
    function checkForAbnormalBehavior() {
        if (isPaused) {
            return; // Skip check if paused
        }

        try {
            // Count recent security events by type
            const eventCounts = {};

            for (const event of securityEventHistory) {
                const type = event.type;
                eventCounts[type] = (eventCounts[type] || 0) + 1;
            }

            // Check for abnormal patterns
            if (eventCounts.domManipulation >= CONFIG.thresholds.domManipulation) {
                handleThreat('domManipulation', {
                    count: eventCounts.domManipulation,
                    action: 'restrict'
                });
            }

            if (eventCounts.apiAbuse >= CONFIG.thresholds.apiAbuse) {
                handleThreat('apiAbuse', {
                    count: eventCounts.apiAbuse,
                    action: 'block'
                });
            }

            if (eventCounts.storageManipulation >= CONFIG.thresholds.storageManipulation) {
                handleThreat('storageManipulation', {
                    count: eventCounts.storageManipulation,
                    action: 'protect'
                });
            }
        } catch (e) {
            console.error("Stickara: Error checking for abnormal behavior:", e);
        }
    }

    /**
     * Monitors DOM manipulation
     */
    function monitorDOMManipulation() {
        try {
            // Monitor createElement
            Document.prototype.createElement = function(tagName) {
                // Use original function with all arguments
                const element = originalFunctions.createElement.apply(this, arguments);

                // Check for suspicious elements
                if (tagName.toLowerCase() === 'script' || tagName.toLowerCase() === 'iframe') {
                    logSecurityEvent('domManipulation', {
                        action: 'createElement',
                        tagName,
                        stack: new Error().stack,
                        severity: 'medium'
                    });
                }

                return element;
            };

            // Monitor appendChild
            Node.prototype.appendChild = function(node) {
                // Check for suspicious nodes
                if (node.nodeName === 'SCRIPT' || node.nodeName === 'IFRAME') {
                    logSecurityEvent('domManipulation', {
                        action: 'appendChild',
                        nodeName: node.nodeName,
                        stack: new Error().stack,
                        severity: 'medium'
                    });
                }

                return originalFunctions.appendChild.apply(this, arguments);
            };

            // Monitor setAttribute
            Element.prototype.setAttribute = function(name, value) {
                // Check for suspicious attributes
                if (name.startsWith('on') ||
                    (name === 'src' && value.includes('javascript:')) ||
                    (name === 'href' && value.includes('javascript:'))) {

                    logSecurityEvent('domManipulation', {
                        action: 'setAttribute',
                        attributeName: name,
                        attributeValue: String(value).substring(0, 100), // Limit value length
                        stack: new Error().stack,
                        severity: 'high'
                    });

                    // Block dangerous attributes
                    if (name.startsWith('on') || value.includes('javascript:')) {
                        console.warn(`Stickara: Blocked setting of dangerous attribute ${name}`);
                        return;
                    }
                }

                return originalFunctions.setAttribute.apply(this, arguments);
            };
        } catch (e) {
            console.error("Stickara: Error setting up DOM manipulation monitoring:", e);
        }
    }

    /**
     * Monitors storage access
     */
    function monitorStorageAccess() {
        try {
            // Monitor localStorage setItem
            Storage.prototype.setItem = function(key, value) {
                // Check if this is a Stickara operation
                if (!isStickaraOperation({ storageKey: key, stack: new Error().stack })) {
                    // Only log if it's not a whitelisted Stickara operation
                    if (key.startsWith('Stickara') || key.includes('Stickara')) {
                        logSecurityEvent('storageManipulation', {
                            action: 'setItem',
                            storageType: this === localStorage ? 'localStorage' : 'sessionStorage',
                            key,
                            valuePreview: String(value).substring(0, 100), // Limit value length
                            stack: new Error().stack,
                            severity: 'medium'
                        });
                    }
                }

                return originalFunctions.setItem.apply(this, arguments);
            };

            // Monitor localStorage removeItem
            Storage.prototype.removeItem = function(key) {
                // Check if this is a Stickara operation
                if (!isStickaraOperation({ storageKey: key, stack: new Error().stack })) {
                    // Only log if it's not a whitelisted Stickara operation
                    if (key.startsWith('Stickara') || key.includes('Stickara')) {
                        logSecurityEvent('storageManipulation', {
                            action: 'removeItem',
                            storageType: this === localStorage ? 'localStorage' : 'sessionStorage',
                            key,
                            stack: new Error().stack,
                            severity: 'medium'
                        });
                    }
                }

                return originalFunctions.removeItem.apply(this, arguments);
            };
        } catch (e) {
            console.error("Stickara: Error setting up storage access monitoring:", e);
        }
    }

    /**
     * Monitors eval usage
     */
    function monitorEvalUsage() {
        try {
            // Monitor eval
            window.eval = function(code) {
                logSecurityEvent('evalUsage', {
                    codePreview: String(code).substring(0, 100), // Limit code length
                    stack: new Error().stack,
                    severity: 'high'
                });

                return originalFunctions.eval.apply(this, arguments);
            };

            // Monitor Function constructor
            window.Function = function() {
                logSecurityEvent('evalUsage', {
                    type: 'Function constructor',
                    args: Array.from(arguments).map(arg => String(arg).substring(0, 50)).join(', '),
                    stack: new Error().stack,
                    severity: 'high'
                });

                return originalFunctions.Function.apply(this, arguments);
            };
        } catch (e) {
            console.error("Stickara: Error setting up eval usage monitoring:", e);
        }
    }

    /**
     * Monitors Chrome API usage
     */
    function monitorChromeAPIs() {
        try {
            // Monitor sendMessage
            chrome.runtime.sendMessage = function() {
                const message = arguments[0];

                // Check for suspicious messages
                if (message && typeof message === 'object') {
                    // Log API usage
                    logSecurityEvent('apiUsage', {
                        api: 'chrome.runtime.sendMessage',
                        messagePreview: JSON.stringify(message).substring(0, 100), // Limit message length
                        stack: new Error().stack,
                        severity: 'low'
                    });
                }

                return originalFunctions.sendMessage.apply(this, arguments);
            };
        } catch (e) {
            console.error("Stickara: Error setting up Chrome API monitoring:", e);
        }
    }

    /**
     * Logs a security event
     * @param {string} type - The type of security event
     * @param {Object} details - Details about the event
     */
    function logSecurityEvent(type, details) {
        try {
            // Skip certain common events to reduce noise
            if (type === 'apiUsage') {
                // Only log API usage for suspicious APIs
                const suspiciousApis = ['chrome.tabs.executeScript', 'chrome.tabs.insertCSS'];
                const api = details.api || '';
                if (!suspiciousApis.some(suspiciousApi => api.includes(suspiciousApi))) {
                    return; // Skip logging for non-suspicious APIs
                }
            }

            // Skip DOM manipulation events for normal operations
            if (type === 'domManipulation') {
                const action = details.action || '';
                const tagName = details.tagName || '';
                const nodeName = details.nodeName || '';

                // Skip normal DOM operations
                if (action === 'createElement' &&
                    !['script', 'iframe', 'object', 'embed'].includes(tagName.toLowerCase())) {
                    return;
                }

                if (action === 'appendChild' &&
                    !['SCRIPT', 'IFRAME', 'OBJECT', 'EMBED'].includes(nodeName)) {
                    return;
                }
            }

            // Create the event object
            const event = {
                type,
                timestamp: Date.now(),
                details,
                url: window.location.href
            };

            // Add to history
            securityEventHistory.push(event);

            // Trim history if needed
            if (securityEventHistory.length > CONFIG.maxEventHistory) {
                securityEventHistory.shift();
            }

            // Log to console - use log instead of warn to reduce noise
            console.log(`Stickara Security Event: ${type}`, details);

            // Report to background script if it's a high severity event
            if (details.severity === 'high') {
                // Check extension context before reporting
                if (isExtensionContextValid()) {
                    reportToBackground(event);
                } else {
                    console.log("Stickara: Extension context invalidated, skipping high severity event report");
                }
            }
        } catch (e) {
            console.log("Stickara: Error logging security event:", e);
        }
    }

    /**
     * Checks if the extension context is still valid
     * @returns {boolean} True if the extension context is valid
     */
    function isExtensionContextValid() {
        try {
            // Check if chrome.runtime is available
            if (!chrome || !chrome.runtime) {
                return false;
            }

            // Check if extension ID is still accessible
            if (!chrome.runtime.id) {
                return false;
            }

            // Check for lastError which indicates context invalidation
            if (chrome.runtime.lastError) {
                return false;
            }

            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * Reports a security event to the background script
     * @param {Object} event - The security event
     */
    function reportToBackground(event) {
        // Check if extension context is still valid before attempting to send message
        if (!isExtensionContextValid()) {
            console.log("Stickara: Extension context invalidated, skipping security event report");
            return;
        }

        try {
            chrome.runtime.sendMessage({
                action: 'securityEvent',
                event
            }, (response) => {
                // Handle response or error
                if (chrome.runtime.lastError) {
                    console.log("Stickara: Extension context invalidated while reporting security event:", chrome.runtime.lastError.message);
                }
            });
        } catch (e) {
            // Only log as warning if it's not an extension context error
            if (e.message && e.message.includes('Extension context invalidated')) {
                console.log("Stickara: Extension context invalidated during security event reporting");
            } else {
                console.error("Stickara: Error reporting security event to background:", e);
            }
        }
    }

    /**
     * Handles a detected threat
     * @param {string} threatType - The type of threat
     * @param {Object} details - Details about the threat
     */
    function handleThreat(threatType, details) {
        if (isPaused) {
            return; // Skip threat handling if paused
        }

        try {
            console.error(`Stickara: Detected ${threatType} threat`, details);

            // Report to background script with error handling
            if (isExtensionContextValid()) {
                try {
                    chrome.runtime.sendMessage({
                        action: 'threatDetected',
                        threatType,
                        details
                    }, (response) => {
                        if (chrome.runtime.lastError) {
                            console.log("Stickara: Extension context invalidated while reporting threat:", chrome.runtime.lastError.message);
                        }
                    });
                } catch (e) {
                    if (e.message && e.message.includes('Extension context invalidated')) {
                        console.log("Stickara: Extension context invalidated during threat reporting");
                    } else {
                        console.warn("Stickara: Could not report threat to background script:", e.message);
                    }
                }
            } else {
                console.log("Stickara: Extension context invalidated, skipping threat report");
            }

            // Take action based on threat type
            switch (threatType) {
                case 'xssAttempts':
                    // Already sanitized in checkForSuspiciousContent
                    break;

                case 'domManipulation':
                    // Restrict DOM manipulation
                    restrictDOMManipulation();
                    break;

                case 'apiAbuse':
                    // Block API access
                    blockAPIAccess();
                    break;

                case 'storageManipulation':
                    // Protect storage
                    protectStorage();
                    break;
            }
        } catch (e) {
            console.error("Stickara: Error handling threat:", e);
        }
    }

    /**
     * Restricts DOM manipulation
     */
    function restrictDOMManipulation() {
        try {
            // Replace DOM manipulation functions with restricted versions
            Document.prototype.createElement = function(tagName) {
                // Block script and iframe creation
                if (tagName.toLowerCase() === 'script' || tagName.toLowerCase() === 'iframe') {
                    console.warn(`Stickara: Blocked creation of ${tagName} element`);
                    return document.createComment(`Blocked ${tagName} creation for security reasons`);
                }

                return originalFunctions.createElement.apply(this, arguments);
            };

            Node.prototype.appendChild = function(node) {
                // Block script and iframe appending
                if (node.nodeName === 'SCRIPT' || node.nodeName === 'IFRAME') {
                    console.warn(`Stickara: Blocked appending of ${node.nodeName} element`);
                    return node;
                }

                return originalFunctions.appendChild.apply(this, arguments);
            };

            Element.prototype.setAttribute = function(name, value) {
                // Block dangerous attributes
                if (name.startsWith('on') ||
                    (name === 'src' && value.includes('javascript:')) ||
                    (name === 'href' && value.includes('javascript:'))) {

                    console.warn(`Stickara: Blocked setting of dangerous attribute ${name}`);
                    return;
                }

                return originalFunctions.setAttribute.apply(this, arguments);
            };
        } catch (e) {
            console.error("Stickara: Error restricting DOM manipulation:", e);
        }
    }

    /**
     * Blocks API access
     */
    function blockAPIAccess() {
        try {
            // Replace API functions with blocked versions
            chrome.runtime.sendMessage = function() {
                console.warn("Stickara: Blocked chrome.runtime.sendMessage due to API abuse");
                return;
            };
        } catch (e) {
            console.error("Stickara: Error blocking API access:", e);
        }
    }

    /**
     * Protects storage
     */
    function protectStorage() {
        try {
            // Replace storage functions with protected versions
            Storage.prototype.setItem = function(key) {
                // Block modifications to Stickara keys
                if (key.startsWith('Stickara') || key.includes('Stickara')) {
                    console.warn(`Stickara: Blocked modification of ${key} in storage`);
                    return;
                }

                return originalFunctions.setItem.apply(this, arguments);
            };

            Storage.prototype.removeItem = function(key) {
                // Block removal of Stickara keys
                if (key.startsWith('Stickara') || key.includes('Stickara')) {
                    console.warn(`Stickara: Blocked removal of ${key} from storage`);
                    return;
                }

                return originalFunctions.removeItem.apply(this, arguments);
            };
        } catch (e) {
            console.error("Stickara: Error protecting storage:", e);
        }
    }

    /**
     * Gets information about a node
     * @param {Node} node - The node to get information about
     * @returns {Object} Information about the node
     */
    function getNodeInfo(node) {
        try {
            return {
                nodeType: node.nodeType,
                parentNodeName: node.parentNode ? node.parentNode.nodeName : null,
                parentId: node.parentNode ? node.parentNode.id : null,
                parentClass: node.parentNode ? node.parentNode.className : null
            };
        } catch (e) {
            return { error: e.message };
        }
    }

    /**
     * Gets information about an element
     * @param {Element} element - The element to get information about
     * @returns {Object} Information about the element
     */
    function getElementInfo(element) {
        try {
            return {
                nodeName: element.nodeName,
                id: element.id,
                className: element.className,
                parentNodeName: element.parentNode ? element.parentNode.nodeName : null,
                parentId: element.parentNode ? element.parentNode.id : null,
                parentClass: element.parentNode ? element.parentNode.className : null
            };
        } catch (e) {
            return { error: e.message };
        }
    }

    /**
     * Sanitizes text to remove potentially malicious content
     * @param {string} text - The text to sanitize
     * @returns {string} The sanitized text
     */
    function sanitizeText(text) {
        if (!text) return '';

        let sanitized = String(text);

        // Replace script tags
        sanitized = sanitized.replace(/<script\b[^>]*>([\s\S]*?)<\/script>/gi, '');

        // Replace javascript: URLs
        sanitized = sanitized.replace(/javascript:/gi, 'blocked:');

        // Replace other potentially dangerous patterns
        sanitized = sanitized.replace(/eval\s*\(/gi, 'blocked(');
        sanitized = sanitized.replace(/on\w+\s*=\s*["']?[^"']*["']?/gi, '');
        sanitized = sanitized.replace(/setTimeout\s*\(\s*["']/gi, 'blockedTimeout("');
        sanitized = sanitized.replace(/setInterval\s*\(\s*["']/gi, 'blockedInterval("');
        sanitized = sanitized.replace(/document\.write\s*\(/gi, 'blockedWrite(');
        sanitized = sanitized.replace(/document\.execCommand\s*\(\s*["']insertHTML/gi, 'blockedExecCommand("insertHTML');
        sanitized = sanitized.replace(/Function\s*\(\s*["']/gi, 'blockedFunction("');
        sanitized = sanitized.replace(/data:text\/html/gi, 'data:blocked/html');

        return sanitized;
    }

    /**
     * Gets the security event history
     * @returns {Array<Object>} The security event history
     */
    function getSecurityEventHistory() {
        return [...securityEventHistory];
    }

    /**
     * Pauses threat detection temporarily
     */
    function pause() {
        isPaused = true;
        if (checkIntervalId) {
            clearInterval(checkIntervalId);
            checkIntervalId = null;
        }
        console.log("Stickara: Threat detection paused");
    }

    /**
     * Resumes threat detection
     */
    function resume() {
        isPaused = false;
        if (!checkIntervalId) {
            checkIntervalId = setInterval(() => {
                try {
                    // Check if extension context is still valid before running security checks
                    if (!isExtensionContextValid()) {
                        console.log("Stickara: Extension context invalidated, pausing threat detection");
                        pause();
                        return;
                    }

                    checkForTampering();
                    checkForSuspiciousContent();
                    checkForAbnormalBehavior();
                } catch (e) {
                    // Check if the error is related to extension context invalidation
                    if (e.message && e.message.includes('Extension context invalidated')) {
                        console.log("Stickara: Extension context invalidated during security check, pausing threat detection");
                        pause();
                    } else {
                        console.error("Stickara: Error in periodic security check:", e);
                    }
                }
            }, CONFIG.checkInterval);
        }
        console.log("Stickara: Threat detection resumed");
    }

    /**
     * Checks if threat detection is currently active
     * @returns {boolean} True if active, false if paused
     */
    function isActive() {
        return !isPaused && checkIntervalId !== null;
    }

    // Return the public API
    return {
        initialize,
        getSecurityEventHistory,
        sanitizeText,
        pause,
        resume,
        isActive
    };
})();

// Initialize the threat detection system
window.StickaraThreatDetection.initialize();

console.log("Stickara: Threat Detection System Loaded");
// --- END OF FILE threat-detection.js ---
