<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Stickara Control</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <!-- Header -->
    <h3><span class="popup-icon">📋</span> Stickara</h3>
    <p class="description">Quick search & page actions.</p>

    <!-- Global Search Area -->
    <div class="search-area">
        <input type="text" id="search-query" placeholder='Search (e.g., text tag:proj url:site)...'>
        <button id="search-button"><span class="popup-icon">🔍</span> Search</button>
    </div>

    <!-- Global Notes Section -->
    <div id="global-notes-results">
        <h3><span class="popup-icon">⭐</span> Important Notes</h3>
        <div class="results-content">
            <p class="no-results">Loading important notes...</p> <!-- Updated initial message -->
        </div>
    </div>

    <!-- Standard Search Results Area -->
    <div id="search-results">
        <p class="no-results">Enter search query above.</p>
    </div>

    <!-- Common Export Format Selection -->
    <div class="action-buttons common-export-controls">
         <label for="common-export-format-select" class="export-format-label">Export Format:</label>
         <select id="common-export-format-select" title="Select export format for notes and highlights">
             <option value="txt">Text (.txt)</option>
             <option value="md">Markdown (.md)</option>
             <option value="html">HTML (.html)</option>
             <option value="pdf">PDF (.pdf)</option>
         </select>
    </div>

    <!-- Export Action Buttons -->
    <div class="action-buttons export-actions">
        <button id="export-notes" title="Export Notes from Current Page"><span class="popup-icon">📄</span> Export Notes</button>
        <button id="export-highlights" title="Export Highlights from Current Page"><span class="popup-icon">✨</span> Export Highlights</button>
    </div>

    <!-- Other Page Actions Row -->
    <div class="action-buttons other-page-actions">
        <button id="clear-note" class="danger" title="Clear Notes & Highlights on Current Page"><span class="popup-icon">🗑️</span> Clear Page Data</button>
    </div>

    <!-- Google Drive Sync Actions -->
    <div class="action-buttons sync-action">
        <button id="connect-drive-button" title="Connect to Google Drive"><span class="popup-icon">🔗</span> Connect Drive</button>
        <button id="disconnect-drive-button" style="display: none;" title="Disconnect from Google Drive"><span class="popup-icon">❌</span> Disconnect</button>
        <span id="sync-status-indicator" title="Sync Status"></span>
    </div>

    <!-- Dashboard Link/Button -->
    <div class="action-buttons dashboard-action">
         <button id="view-dashboard-button" title="View All Notes"><span class="popup-icon">📊</span> View Dashboard</button>
    </div>

    <!-- Settings Button with Dropdown -->
    <div class="action-buttons settings-actions">
        <!-- Main Settings Button -->
        <div class="settings-dropdown">
            <button id="main-settings-button" title="Settings"><span class="popup-icon">⚙️</span> Settings</button>
            <div id="settings-dropdown-menu" class="dropdown-menu">
                <!-- Voice Settings Option -->
                <div class="dropdown-item" id="voice-settings-option">
                    <span class="popup-icon">🎤</span> Voice Settings
                </div>

                <!-- Note Settings Option -->
                <div class="dropdown-item" id="note-settings-option">
                    <span class="popup-icon">📝</span> Note Settings
                </div>



                <!-- Screenshot Settings Option -->
                <div class="dropdown-item" id="screenshot-settings-option">
                    <span class="popup-icon">📸</span> Screenshot Settings
                </div>
            </div>
        </div>
    </div>

    <!-- Voice Settings Panel (hidden by default) -->
    <div id="voice-settings-panel" class="settings-panel" style="display: none;">
        <div class="panel-header">
            <h3><span class="popup-icon">🎤</span> Voice Transcription Settings</h3>
            <button id="close-voice-settings" class="close-button" title="Close Settings">×</button>
        </div>
        <div class="settings-container">
            <!-- Provider Section -->
            <div class="settings-section">
                <h4>Speech Recognition Provider</h4>
                <div class="form-group">
                    <label for="voice-provider">Select Provider:</label>
                    <select id="voice-provider">
                        <option value="browser">Browser Speech API (Default)</option>
                        <option value="google">Google Speech-to-Text</option>
                        <option value="azure">Azure Speech Service</option>
                        <option value="assembly">AssemblyAI</option>
                    </select>
                </div>

                <div id="api-key-container" class="form-group" style="display: none;">
                    <label for="voice-api-key">API Key (optional):</label>
                    <div class="api-key-input-container">
                        <input type="password" id="voice-api-key" placeholder="Enter API key if available">
                        <button id="toggle-password" class="toggle-password" title="Show/Hide Password">
                            <span class="popup-icon">👁️</span> <!-- Use an appropriate icon -->
                        </button>
                    </div>
                </div>

                <!-- Provider Info Text (Example structure, text filled by JS) -->
                 <div id="provider-info" class="info-text">
                    Select a provider to see details.
                </div>
            </div>

            <!-- Language Section -->
            <div class="settings-section">
                <h4>Language Settings</h4>
                <div class="form-group">
                    <label for="voice-language">Recognition Language:</label>
                    <select id="voice-language">
                        <!-- Populated by JavaScript -->
                        <option>Loading languages...</option>
                    </select>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="voice-auto-detect">
                    <label for="voice-auto-detect">Auto-detect language</label>
                </div>
            </div>

            <!-- Options Section -->
            <div class="settings-section">
                <h4>Transcription Options</h4>
                <div class="checkbox-group">
                    <input type="checkbox" id="voice-punctuation" checked>
                    <label for="voice-punctuation">Automatic punctuation</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="voice-capitalization" checked>
                    <label for="voice-capitalization">Automatic capitalization</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="voice-profanity-filter">
                    <label for="voice-profanity-filter">Filter profanity</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="voice-interim-results" checked>
                    <label for="voice-interim-results">Show interim results</label>
                </div>

                <div class="form-group">
                    <label for="voice-silence-threshold">Stop after silence:</label>
                    <div class="slider-container">
                        <input type="range" min="5" max="60" value="15" class="slider" id="voice-silence-threshold">
                        <span id="silence-value">15 seconds</span>
                    </div>
                </div>
            </div>

            <!-- Advanced Section -->
            <div class="settings-section">
                <h4>Advanced Features (Provider Dependent)</h4>
                <div class="form-group">
                    <label for="voice-quality">Transcription Quality:</label>
                    <select id="voice-quality">
                        <option value="standard">Standard</option>
                        <option value="enhanced">Enhanced</option>
                        <option value="high">High</option>
                        <option value="ultra">Ultra (Highest accuracy)</option>
                    </select>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="voice-background-noise-reduction">
                    <label for="voice-background-noise-reduction">Background noise reduction</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="voice-speaker-diarization">
                    <label for="voice-speaker-diarization">Speaker identification</label>
                </div>
            </div>

            <!-- Status Message for Voice Settings -->
            <div id="status-message" class="status-message" role="status" aria-live="polite">
                <span id="status-text"></span>
            </div>

            <!-- Action Buttons -->
            <div class="settings-actions">
                <button id="reset-settings" class="secondary-button">Reset to Defaults</button>
                <button id="save-voice-settings" class="primary-button">Save Settings</button>
            </div>
        </div>
    </div>

    <!-- Note Settings Panel (hidden by default) -->
    <div id="note-settings-panel" class="settings-panel" style="display: none;">
        <div class="panel-header">
            <h3><span class="popup-icon">⚙️</span> Note Settings</h3>
            <button id="close-note-settings" class="close-button" title="Close Settings">×</button>
        </div>
        <div class="settings-container">


            <!-- Default Note Size Section -->
            <div class="settings-section">
                <h4><span class="settings-icon">📏</span> Default Note Size</h4>
                <p class="setting-description">Set the default size for new notes.</p>
                <div class="form-group">
                    <label for="default-width">
                        <span class="settings-icon">↔️</span> Width:
                    </label>
                    <div class="input-with-unit">
                        <input type="number" id="default-width" min="200" max="800" step="10">
                        <span class="unit">px</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="default-height">
                        <span class="settings-icon">↕️</span> Height:
                    </label>
                    <div class="input-with-unit">
                        <input type="number" id="default-height" min="150" max="800" step="10">
                        <span class="unit">px</span>
                    </div>
                </div>
            </div>

            <!-- Default Note Position Section -->
            <div class="settings-section">
                <h4><span class="settings-icon">📍</span> Default Note Position</h4>
                <p class="setting-description">Set where new notes appear on the page.</p>
                <div class="form-group">
                    <label for="default-top">
                        <span class="settings-icon">⬆️</span> Top:
                    </label>
                    <div class="input-with-unit">
                        <input type="number" id="default-top" min="0" max="500" step="10">
                        <span class="unit">px</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="default-right">
                        <span class="settings-icon">⬅️</span> Right:
                    </label>
                    <div class="input-with-unit">
                        <input type="number" id="default-right" min="0" max="500" step="10">
                        <span class="unit">px</span>
                    </div>
                </div>
            </div>



            <!-- Status Message for Note Settings -->
            <div id="note-status-message" class="status-message" role="status" aria-live="polite">
                <span id="note-status-text"></span>
            </div>

            <!-- Action Buttons -->
            <div class="settings-actions">
                <button id="reset-note-settings" class="secondary-button">
                    Reset to Defaults
                </button>
                <button id="save-note-settings" class="primary-button">
                    Save Settings
                </button>
            </div>
        </div>
    </div>



    <!-- Screenshot Settings Panel (hidden by default) -->
    <div id="screenshot-settings-panel" class="settings-panel" style="display: none;">
        <div class="panel-header">
            <h3><span class="popup-icon">📸</span> Screenshot Settings</h3>
            <button id="close-screenshot-settings" class="close-button" title="Close Settings">×</button>
        </div>
        <div class="settings-container">
            <!-- Storage Location Section -->
            <div class="settings-section">
                <h4><span class="settings-icon">💾</span> Storage Location</h4>
                <div class="form-group storage-location-group">
                    <label>Where to save screenshots:</label>
                    <div class="storage-options">
                        <div class="storage-option">
                            <input type="radio" id="storage-local" name="storage-location" value="local">
                            <label for="storage-local">
                                <span class="storage-icon">💻</span>
                                <span class="storage-label">Local Only</span>
                                <span class="storage-description">Save to your computer only</span>
                            </label>
                        </div>
                        <div class="storage-option">
                            <input type="radio" id="storage-drive" name="storage-location" value="drive">
                            <label for="storage-drive">
                                <span class="storage-icon">☁️</span>
                                <span class="storage-label">Google Drive Only</span>
                                <span class="storage-description">Save to Google Drive only</span>
                            </label>
                        </div>
                        <div class="storage-option">
                            <input type="radio" id="storage-both" name="storage-location" value="both">
                            <label for="storage-both">
                                <span class="storage-icon">🔄</span>
                                <span class="storage-label">Both</span>
                                <span class="storage-description">Save to both computer and Drive</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google Drive Settings Section -->
            <div class="settings-section" id="drive-settings-section">
                <h4><span class="settings-icon">☁️</span> Google Drive Settings</h4>
                <div class="form-group">
                    <label for="drive-folder-name">Folder Name:</label>
                    <input type="text" id="drive-folder-name" placeholder="Stickara Screenshots">
                    <p class="setting-description">Name of the folder where screenshots will be saved in Google Drive.</p>
                </div>

                <div class="form-group privacy-setting">
                    <label>Privacy Setting:</label>
                    <div class="privacy-options">
                        <div class="privacy-option">
                            <input type="radio" id="privacy-private" name="privacy-setting" value="private">
                            <label for="privacy-private">
                                <span class="privacy-icon">🔒</span>
                                <span class="privacy-label">Private</span>
                                <span class="privacy-description">Only visible to this extension</span>
                            </label>
                        </div>
                        <div class="privacy-option">
                            <input type="radio" id="privacy-visible" name="privacy-setting" value="visible">
                            <label for="privacy-visible">
                                <span class="privacy-icon">👁️</span>
                                <span class="privacy-label">Visible</span>
                                <span class="privacy-description">Visible in your Google Drive</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="auto-delete-after">Auto-Delete After:</label>
                    <div class="auto-delete-container">
                        <input type="range" id="auto-delete-after" min="0" max="90" step="1" value="0">
                        <div class="auto-delete-value">
                            <span id="auto-delete-display">Never</span>
                        </div>
                    </div>
                    <div class="auto-delete-timeline" id="auto-delete-timeline">
                        <div class="timeline-now">Now</div>
                        <div class="timeline-bar">
                            <div class="timeline-marker"></div>
                        </div>
                        <div class="timeline-deletion">Deletion</div>
                    </div>
                    <p class="setting-description">Screenshots will be automatically deleted from Google Drive after the specified period. Set to 0 to keep them forever.</p>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="ask-before-upload" checked>
                    <label for="ask-before-upload">Ask for confirmation before uploading to Google Drive</label>
                    <p class="setting-description">When enabled, you'll be asked to confirm before each screenshot is uploaded to Google Drive.</p>
                </div>
            </div>

            <!-- Image Quality Section -->
            <div class="settings-section">
                <h4><span class="settings-icon">✨</span> Image Quality</h4>
                <div class="form-group">
                    <label for="image-quality">Screenshot Quality:</label>
                    <select id="image-quality">
                        <option value="standard">Standard (Good balance of quality and size)</option>
                        <option value="high">High (Better quality, larger file size)</option>
                        <option value="maximum">Maximum (Best quality, largest file size)</option>
                    </select>
                    <p class="setting-description">Higher quality settings produce better images but result in larger file sizes.</p>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="enable-high-dpi" checked>
                    <label for="enable-high-dpi">Enable high-DPI support for retina/high-resolution displays</label>
                    <p class="setting-description">Creates sharper screenshots on high-resolution displays. May increase file size.</p>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="enable-anti-aliasing" checked>
                    <label for="enable-anti-aliasing">Enable anti-aliasing for smoother lines and text</label>
                    <p class="setting-description">Improves the appearance of text and diagonal lines in screenshots.</p>
                </div>


            </div>

            <!-- Status Message for Screenshot Settings -->
            <div id="screenshot-status-message" class="status-message" role="status" aria-live="polite">
                <span id="screenshot-status-text"></span>
            </div>

            <!-- Action Buttons -->
            <div class="settings-actions">
                <button id="reset-screenshot-settings" class="secondary-button">
                    Reset to Defaults
                </button>
                <button id="save-screenshot-settings" class="primary-button">
                    Save Settings
                </button>
            </div>
        </div>
    </div>

    <!-- Main status message for popup -->
    <div id="main-status-message" class="status-message" role="status" aria-live="polite"></div>

    <!-- PDF Libraries -->
    <script src="lib/jspdf.umd.min.js"></script>
    <script src="lib/html2canvas.min.js"></script>
    <!-- URL Utilities -->
    <script src="lib/url-utils.js"></script>
    <!-- Settings Script -->
    <script src="settings.js"></script>
    <!-- Main Popup Script -->
    <script src="popup.js"></script>
</body>
</html>