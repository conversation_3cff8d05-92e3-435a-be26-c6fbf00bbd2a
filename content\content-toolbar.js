// --- START OF FILE content-toolbar.js ---

/**
 * Creates a standard tool button used in dropdowns or the header.
 * @param {string} icon - Emoji or icon character.
 * @param {string} text - Button text label. If empty, the text span might be removed by the caller (e.g., for diagram tools).
 * @param {string} title - Tooltip and aria-label text.
 * @param {function} onClick - The function to call when clicked.
 * @param {string|null} [id=null] - Optional ID for the button element.
 * @returns {HTMLButtonElement} The created button element.
 */
function createToolButton(icon, text, title, onClick, id = null) {
    const btn = document.createElement('button');
    btn.classList.add('Stickara-tool-btn'); // General class for styling
    // Include the text span even if text is empty, allows caller to decide removal
    btn.innerHTML = `<span class="Stickara-icon">${icon}</span><span class="Stickara-text">${text}</span>`;
    btn.title = title;
    btn.setAttribute('aria-label', title);
    // Add robust check for onClick being a function before adding listener
    if (typeof onClick === 'function') {
        btn.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent event bubbling (e.g., closing dropdowns unintentionally)
            onClick(e); // Execute the provided handler
        });
    } else {
        console.warn(`Stickara: Invalid onClick handler provided for tool button "${title}". Button will be inactive.`);
        btn.disabled = true; // Disable button if handler is invalid
    }
    if (id) {
        btn.id = id; // Assign ID if provided
    }
    return btn;
}


/**
 * Creates a formatting button (Bold, Italic, etc.).
 * @param {string} cmd - The document.execCommand command string (e.g., 'bold', 'formatBlock').
 * @param {string} html - The HTML content for the button (e.g., '<b>B</b>', '“').
 * @param {string|null} [value=null] - Optional value for commands like 'formatBlock' (e.g., 'BLOCKQUOTE', 'H2').
 * @param {string|null} [titleOverride=null] - Optional title override.
 * @returns {HTMLButtonElement} The created button element.
 */
function createFormattingButton(cmd, html, value = null, titleOverride = null) {
    const btn = document.createElement('button');
    btn.classList.add('Stickara-format-btn');
    btn.innerHTML = html;
    btn.dataset.cmd = cmd;
    if (value) {
        btn.dataset.value = value;
    }
    const actionTitle = titleOverride || `Apply ${value || cmd}`;
    btn.title = actionTitle;
    btn.setAttribute('aria-label', actionTitle);
    btn.addEventListener('click', (e) => {
        e.stopPropagation();
        // Ensure required functions exist before calling
        if (typeof applyFormatting === 'function' && typeof handleFormattingState === 'function') {
            applyFormatting(cmd, value);
            handleFormattingState();
        } else {
            console.error("Stickara: Required formatting functions (applyFormatting/handleFormattingState) not found for button:", actionTitle);
            alert("Formatting action failed. See console for details.");
        }
    });
    return btn;
}

/**
 * Creates a button for changing text size.
 * @param {string} text - The text for the button (e.g., 'A+').
 * @param {number} delta - The amount to change font size by (e.g., 4 or -4).
 * @returns {HTMLButtonElement} The created button element.
 */
function createTextSizeButton(text, delta) {
    const btn = document.createElement('button');
    btn.classList.add('Stickara-text-size-btn');
    btn.innerHTML = text;
    const action = delta > 0 ? 'Increase' : 'Decrease';
    btn.title = `${action} text size`;
    btn.setAttribute('aria-label', `${action} text size`);
    btn.addEventListener('click', (e) => {
        e.stopPropagation();
        // Ensure required function exists
        if (typeof changeTextSize === 'function') {
            changeTextSize(delta);
        } else {
            console.error("Stickara: changeTextSize function not found.");
            alert("Text size action failed. See console for details.");
        }
    });
    return btn;
}

/**
 * Creates a global font size control with a slider and display.
 * @returns {HTMLDivElement} The created control container.
 */
function createGlobalFontSizeControl() {
    const container = document.createElement('div');
    container.classList.add('Stickara-global-font-size-control');
    container.style.display = 'flex';
    container.style.flexDirection = 'column';
    container.style.gap = '4px';
    container.style.padding = '8px';
    container.style.minWidth = '180px';

    // Label
    const label = document.createElement('label');
    label.textContent = 'Base Font Size:';
    label.style.fontSize = '11px';
    label.style.fontWeight = '500';
    label.style.color = 'var(--Stickara-text-light)';
    container.appendChild(label);

    // Slider container
    const sliderContainer = document.createElement('div');
    sliderContainer.style.display = 'flex';
    sliderContainer.style.alignItems = 'center';
    sliderContainer.style.gap = '8px';

    // Slider
    const slider = document.createElement('input');
    slider.type = 'range';
    slider.min = '10';
    slider.max = '24';
    slider.step = '1';
    slider.value = '14'; // Default font size
    slider.classList.add('Stickara-font-size-slider');
    slider.style.flex = '1';

    // Value display
    const valueDisplay = document.createElement('span');
    valueDisplay.textContent = '14px';
    valueDisplay.style.fontSize = '11px';
    valueDisplay.style.fontWeight = '500';
    valueDisplay.style.minWidth = '30px';
    valueDisplay.style.textAlign = 'center';

    // Event listeners for slider to prevent drag interference
    slider.addEventListener('mousedown', (e) => {
        e.stopPropagation(); // Prevent note dragging
    });

    slider.addEventListener('mousemove', (e) => {
        e.stopPropagation(); // Prevent note dragging during slider interaction
    });

    slider.addEventListener('input', (e) => {
        e.stopPropagation(); // Prevent note dragging
        const newSize = parseInt(e.target.value);
        valueDisplay.textContent = newSize + 'px';

        // Apply global font size
        if (typeof setGlobalFontSize === 'function') {
            setGlobalFontSize(newSize);
        } else {
            console.error("Stickara: setGlobalFontSize function not found.");
        }
    });

    slider.addEventListener('change', (e) => {
        e.stopPropagation(); // Prevent note dragging
    });

    sliderContainer.appendChild(slider);
    sliderContainer.appendChild(valueDisplay);
    container.appendChild(sliderContainer);

    // Prevent drag events on the entire container
    container.addEventListener('mousedown', (e) => {
        e.stopPropagation(); // Prevent note dragging when clicking anywhere in the control
    });

    container.addEventListener('mousemove', (e) => {
        e.stopPropagation(); // Prevent note dragging when moving mouse over the control
    });

    return container;
}

/**
 * Creates a font family selector dropdown.
 * @returns {HTMLSelectElement} The created select element.
 */
function createFontFamilySelector() {
    const fontFamilySelect = document.createElement('select');
    fontFamilySelect.classList.add('Stickara-font-family-select');
    fontFamilySelect.title = "Change font family";

    // Define font groups
    const fontGroups = {
        'default': [
            { value: 'default', label: 'Default Font' }
        ],
        'Sans-serif': [
            { value: 'Arial, sans-serif', label: 'Arial' },
            { value: 'Helvetica, sans-serif', label: 'Helvetica' },
            { value: 'Verdana, sans-serif', label: 'Verdana' },
            { value: 'Tahoma, sans-serif', label: 'Tahoma' },
            { value: 'Trebuchet MS, sans-serif', label: 'Trebuchet MS' },
            { value: 'Calibri, sans-serif', label: 'Calibri' },
            { value: 'Segoe UI, sans-serif', label: 'Segoe UI' },
            { value: 'Roboto, sans-serif', label: 'Roboto' },
            { value: 'Open Sans, sans-serif', label: 'Open Sans' }
        ],
        'Serif': [
            { value: 'Times New Roman, serif', label: 'Times New Roman' },
            { value: 'Georgia, serif', label: 'Georgia' },
            { value: 'Garamond, serif', label: 'Garamond' },
            { value: 'Baskerville, serif', label: 'Baskerville' },
            { value: 'Cambria, serif', label: 'Cambria' },
            { value: 'Palatino, serif', label: 'Palatino' }
        ],
        'Monospace': [
            { value: 'Courier New, monospace', label: 'Courier New' },
            { value: 'Consolas, monospace', label: 'Consolas' },
            { value: 'Monaco, monospace', label: 'Monaco' },
            { value: 'Lucida Console, monospace', label: 'Lucida Console' }
        ],
        'Decorative': [
            { value: 'Comic Sans MS, cursive', label: 'Comic Sans MS' },
            { value: 'Impact, fantasy', label: 'Impact' },
            { value: 'Copperplate, fantasy', label: 'Copperplate' }
        ]
    };

    // Create option groups and options
    Object.entries(fontGroups).forEach(([groupName, fonts]) => {
        // Skip creating optgroup for default option
        if (groupName === 'default') {
            fonts.forEach(font => {
                const option = document.createElement('option');
                option.value = font.value;
                option.textContent = font.label;
                option.style.fontFamily = font.value;
                fontFamilySelect.appendChild(option);
            });
            return;
        }

        // Create optgroup for other font categories
        const optgroup = document.createElement('optgroup');
        optgroup.label = groupName;

        // Add fonts to the optgroup
        fonts.forEach(font => {
            const option = document.createElement('option');
            option.value = font.value;
            option.textContent = font.label;
            option.style.fontFamily = font.value;
            optgroup.appendChild(option);
        });

        fontFamilySelect.appendChild(optgroup);
    });

    // Add change event listener
    fontFamilySelect.addEventListener('change', function(e) {
        e.stopPropagation();
        const selectedFont = this.value;

        // Ensure required function exists
        if (typeof setFontFamily === 'function') {
            setFontFamily(selectedFont);
        } else {
            console.error("Stickara: setFontFamily function not found.");
            alert("Font family change failed. See console for details.");
        }
    });

    return fontFamilySelect;
}

/**
 * Creates color swatch buttons for changing the note theme.
 * Assumes `changeColor` function is defined elsewhere.
 * @returns {HTMLButtonElement[]} An array of color swatch button elements.
 */
function createColorSwatches() {
    // Expanded color palette with new theme colors
    const colors = [
        'yellow', 'blue', 'green', 'pink', 'purple',
        'orange', 'teal', 'red', 'brown', 'gray'
    ];

    return colors.map(color => {
        const swatch = document.createElement('button');
        swatch.classList.add('Stickara-color-swatch', `color-${color}`);
        swatch.dataset.color = color;
        swatch.title = `Change theme to ${color}`;
        swatch.setAttribute('aria-label', `Change theme to ${color}`);
        swatch.addEventListener('click', (e) => {
            e.stopPropagation();
            // Ensure required function exists
            if (typeof changeColor === 'function') {
                 changeColor(swatch); // Assumes changeColor handles setting active state
            } else {
                console.error("Stickara: changeColor function not found.");
                alert("Theme change failed. See console for details.");
            }
        });
        return swatch;
    });
}

/**
 * Creates buttons for changing the note opacity.
 * Assumes `changeOpacity` function is defined elsewhere.
 * @returns {HTMLButtonElement[]} An array of opacity button elements.
 */
function createOpacityButtons() {
    return [1.0, 0.8, 0.6, 0.4].map(opacity => {
        const btn = document.createElement('button');
        btn.classList.add('Stickara-opacity-btn');
        btn.innerText = `${Math.round(opacity * 100)}%`;
        btn.dataset.opacity = String(opacity);
        const title = `Set opacity to ${Math.round(opacity * 100)}%`;
        btn.title = title;
        btn.setAttribute('aria-label', title);
        btn.addEventListener('click', (e) => {
            e.stopPropagation();
            // Ensure required function exists
            if (typeof changeOpacity === 'function') {
                changeOpacity(btn); // Assumes changeOpacity handles setting active state
            } else {
                console.error("Stickara: changeOpacity function not found.");
                 alert("Opacity change failed. See console for details.");
            }
        });
        return btn;
    });
}

/**
 * Creates a text shadow toggle button.
 * Assumes `toggleTextShadow` function is defined elsewhere.
 * @returns {HTMLButtonElement} The text shadow toggle button element.
 */
function createTextShadowButton() {
    const btn = document.createElement('button');
    btn.classList.add('Stickara-text-shadow-btn');
    btn.innerHTML = '🔲'; // Shadow/depth emoji - more appropriate for text shadow
    btn.dataset.textShadow = 'false'; // Default state
    const title = 'Toggle text shadow';
    btn.title = title;
    btn.setAttribute('aria-label', title);
    btn.addEventListener('click', (e) => {
        e.stopPropagation();
        // Ensure required function exists
        if (typeof toggleTextShadow === 'function') {
            toggleTextShadow(btn);
        } else {
            console.error("Stickara: toggleTextShadow function not found.");
            alert("Text shadow toggle failed. See console for details.");
        }
    });
    return btn;
}

/**
 * Creates the template selector dropdown.
 * Now integrates with the template manager for both built-in and custom templates.
 * @returns {HTMLSelectElement} The created select element.
 */
function createTemplateSelector() {
    const templateSelect = document.createElement('select');
    templateSelect.classList.add('Stickara-template-select');
    templateSelect.title = "Apply a pre-defined template";
    templateSelect.innerHTML = '<option value="">Apply Template...</option>'; // Default option

    // Populate with templates using template manager if available
    if (typeof templateManager !== 'undefined') {
        templateManager.populateTemplateSelector(templateSelect);
    } else {
        // Fallback to old method for built-in templates
        if (typeof StickaraTemplates === 'object' && StickaraTemplates !== null) {
            for (const name of Object.keys(StickaraTemplates)) {
                const option = document.createElement('option');
                option.value = name;
                option.textContent = name;
                templateSelect.appendChild(option);
            }
        } else {
            console.warn("Stickara: StickaraTemplates object not found during selector creation.");
            // Optionally add a disabled placeholder option
            const disabledOption = document.createElement('option');
            disabledOption.value = "";
            disabledOption.textContent = "No templates loaded";
            disabledOption.disabled = true;
            templateSelect.insertBefore(disabledOption, templateSelect.firstChild.nextSibling);
        }
    }

    templateSelect.addEventListener('change', async function (e) {
        e.stopPropagation();
        const selectedTemplateName = this.value;

        if (!selectedTemplateName) return;

        // Check if necessary functions exist
        if (typeof processPlaceholders !== 'function' ||
            typeof insertHtmlAtCursor !== 'function' ||
            typeof closeAllDropdowns !== 'function') {
            console.error("Stickara: Cannot insert template. Missing required functions.");
            showStatus("Error applying template. See console for details.", 'error');
            this.value = '';
            return;
        }

        try {
            let rawTemplateContent = null;

            // Try to get template from template manager first
            if (typeof templateManager !== 'undefined') {
                const template = templateManager.getTemplate(selectedTemplateName);
                if (template) {
                    rawTemplateContent = template.content;
                    // Track usage
                    templateManager.trackTemplateUsage(selectedTemplateName);
                }
            }

            // Fallback to built-in templates
            if (!rawTemplateContent && typeof StickaraTemplates === 'object' && StickaraTemplates[selectedTemplateName]) {
                rawTemplateContent = StickaraTemplates[selectedTemplateName];
            }

            if (!rawTemplateContent) {
                throw new Error(`Template "${selectedTemplateName}" not found`);
            }

            console.log(`Template selected: ${selectedTemplateName}. Processing template...`);

            // Show loading indicator for smart templates
            const isSmartTemplate = /\{\{[^}]+\}\}/.test(rawTemplateContent);
            if (isSmartTemplate && typeof showStatus === 'function') {
                showStatus('Processing smart template...', 'info');
            }

            // Process placeholders (may return Promise for smart templates)
            let processedContent = processPlaceholders(rawTemplateContent);

            // Handle both sync and async results
            if (processedContent instanceof Promise) {
                processedContent = await processedContent;
            }

            // --- WRAP the processed content in a div with a class ---
            const wrappedContent = `<div class="Stickara-template-content">${processedContent}</div><br>`;

            insertHtmlAtCursor(wrappedContent);

            // Enhance any tables that were just inserted - Multiple attempts for reliability
            setTimeout(() => {
                if (typeof enhanceAllTablesInNote === 'function') {
                    enhanceAllTablesInNote();
                    console.log('Stickara: Template tables enhanced (first attempt)');
                }
            }, 50);

            setTimeout(() => {
                if (typeof enhanceAllTablesInNote === 'function') {
                    enhanceAllTablesInNote();
                    console.log('Stickara: Template tables enhanced (second attempt)');
                }
            }, 200);

            // Show success message
            if (typeof showStatus === 'function') {
                showStatus('Template applied successfully!', 'success');
            }
            closeAllDropdowns();
        } catch (error) {
            console.error("Stickara: Error processing template:", error);
            if (typeof showStatus === 'function') {
                showStatus('Error processing template: ' + error.message, 'error');
            }
        }
        this.value = ''; // Reset select visually after action
    });

    // Listen for template updates to refresh the dropdown
    window.addEventListener('stickaraTemplatesUpdated', () => {
        if (typeof templateManager !== 'undefined') {
            templateManager.populateTemplateSelector(templateSelect);
        }
    });

    return templateSelect;
}


/**
 * Creates a container div for grouping items within a dropdown.
 * @param {string|null} [title=null] - Optional title for the group.
 * @returns {HTMLDivElement} The created group div.
 */
function createDropdownGroup(title = null) {
    const groupDiv = document.createElement('div');
    groupDiv.classList.add('Stickara-dropdown-group');
    if (title) {
        const titleSpan = document.createElement('span');
        titleSpan.classList.add('Stickara-dropdown-group-title');
        titleSpan.textContent = title;
        groupDiv.appendChild(titleSpan);
    }
    return groupDiv;
}

/**
 * Creates a dropdown menu component.
 * Assumes `toggleDropdown` function is defined elsewhere.
 * @param {string} label - The text label for the dropdown trigger button.
 * @param {HTMLElement[]} children - An array of elements (buttons, groups) to put inside the dropdown content.
 * @param {object} [options={}] - Optional configuration: { icon: '...', id: '...' }.
 * @returns {HTMLDivElement} The main dropdown container div.
 */
function createDropdown(label, children, options = {}) {
    const dropdown = document.createElement('div');
    dropdown.classList.add('Stickara-dropdown');

    const dropdownButton = document.createElement('button');
    dropdownButton.classList.add('Stickara-dropdown-button');
    // Use textContent for label security, innerHTML only for trusted icon HTML
    dropdownButton.innerHTML = (options.icon ? `<span class="Stickara-icon">${options.icon}</span> ` : '');
    dropdownButton.appendChild(document.createTextNode(label)); // Append text safely
    if (options.id) {
        dropdownButton.id = options.id;
    }
    dropdownButton.dataset.dropdownId = options.id || label.toLowerCase().replace(/\s+/g, '-'); // For potential targeting

    dropdownButton.addEventListener('click', (e) => {
        e.stopPropagation();
        // Ensure required function exists
        if (typeof toggleDropdown === 'function') {
            toggleDropdown(dropdown);
        } else {
            console.error("Stickara: toggleDropdown function not found.");
            alert("Dropdown cannot be opened. See console for details.");
        }
    });

    const dropdownContent = document.createElement('div');
    dropdownContent.classList.add('Stickara-dropdown-content');
    // Append children safely
    children.forEach(child => {
        if (child instanceof HTMLElement) { // Basic check
            dropdownContent.appendChild(child);
        }
    });

    dropdown.appendChild(dropdownButton);
    dropdown.appendChild(dropdownContent);
    return dropdown;
}


/**
 * Creates the main header controls container with all its dropdowns.
 * Checks for existence of handler functions before creating buttons.
 * Includes Screenshot button and options within the Insert dropdown.
 * @returns {HTMLDivElement} The header controls container div.
 */
function createHeaderControls() {
    const controlsWrapper = document.createElement('div');
    // Use constant safely
    const controlsId = typeof HEADER_CONTROLS_ID !== 'undefined' ? HEADER_CONTROLS_ID : 'Stickara-header-controls';
    controlsWrapper.id = controlsId;

    // --- Check for ALL necessary functions FIRST ---
    const coreFormattingFuncs = typeof applyFormatting === 'function' && typeof handleFormattingState === 'function';
    const textEditingFuncs = typeof changeTextSize === 'function';
    // Ensure processPlaceholders is checked for Insert dropdown
    const insertFuncs = typeof handleImageSelectClick === 'function' && typeof insertEquation === 'function' && typeof openDiagramEditor === 'function' && typeof handleCaptureScreenshot === 'function' && typeof processPlaceholders === 'function';
    const toolFuncs = typeof openFlashcardModal === 'function' && typeof toggleRecording === 'function' && typeof handleConvertSelectionToChecklist === 'function';
    const viewControlFuncs = typeof togglePin === 'function' && typeof toggleGlobalPin === 'function' && typeof copyNoteText === 'function' && typeof hideNote === 'function';
    const colorOpacityFuncs = typeof changeColor === 'function' && typeof changeOpacity === 'function' && typeof toggleTextShadow === 'function';
    const dropdownFuncs = typeof toggleDropdown === 'function' && typeof closeAllDropdowns === 'function';

    const allFuncsAvailable = coreFormattingFuncs && textEditingFuncs && insertFuncs && toolFuncs && viewControlFuncs && colorOpacityFuncs && dropdownFuncs;

     if (!allFuncsAvailable) {
         // Log which groups might be missing functions
         let missingGroups = [];
         if (!coreFormattingFuncs) missingGroups.push("Formatting"); if (!textEditingFuncs) missingGroups.push("Text Size");
         if (!insertFuncs) missingGroups.push("Insert Tools/Templates"); if (!toolFuncs) missingGroups.push("Tools");
         if (!viewControlFuncs) missingGroups.push("View Controls"); if (!colorOpacityFuncs) missingGroups.push("Appearance");
         if (!dropdownFuncs) missingGroups.push("Dropdown Logic");
         console.error(`Stickara CRITICAL: Functions required by createHeaderControls missing! Groups: ${missingGroups.join(', ')}`);
         controlsWrapper.innerHTML = '<span style="color: red; font-size: 10px;">Err</span>'; // Minimal error
         return controlsWrapper;
     }
     // --- End Function Checks ---


    // --- Formatting Dropdown ---
    const formattingGroup = createDropdownGroup();
    formattingGroup.appendChild(createFormattingButton('bold', '<b>B</b>'));
    formattingGroup.appendChild(createFormattingButton('italic', '<i>I</i>'));
    formattingGroup.appendChild(createFormattingButton('underline', '<u>U</u>'));
    formattingGroup.appendChild(createFormattingButton('strikethrough', '<del>S</del>'));
    formattingGroup.appendChild(createFormattingButton('insertOrderedList', '1.', null, 'Ordered List'));
    formattingGroup.appendChild(createFormattingButton('insertUnorderedList', '•', null, 'Unordered List'));
    formattingGroup.appendChild(createFormattingButton('formatBlock', '“', 'BLOCKQUOTE', 'Blockquote'));
    formattingGroup.appendChild(createFormattingButton('formatBlock', 'Code', 'PRE', 'Code Block'));
    formattingGroup.appendChild(createFormattingButton('removeFormat', '🧹', null, 'Clear Formatting'));

    // Text alignment group
    const alignmentGroup = createDropdownGroup('Text Alignment');
    alignmentGroup.appendChild(createFormattingButton('justifyLeft', '⬅', null, 'Align Left'));
    alignmentGroup.appendChild(createFormattingButton('justifyCenter', '⬌', null, 'Align Center'));
    alignmentGroup.appendChild(createFormattingButton('justifyRight', '➡', null, 'Align Right'));
    alignmentGroup.appendChild(createFormattingButton('justifyFull', '⬍', null, 'Justify'));
    // Font size buttons group
    const fontSizeGroup = createDropdownGroup('Font Size');
    // Add the +/- buttons with larger increments for better visibility
    fontSizeGroup.appendChild(createTextSizeButton('A–', -2));
    fontSizeGroup.appendChild(createTextSizeButton('A+', 2));

    // Add global font size control
    const globalFontSizeControl = createGlobalFontSizeControl();
    fontSizeGroup.appendChild(globalFontSizeControl);

    // Font family selection group
    const fontFamilyGroup = createDropdownGroup('Font Family');
    fontFamilyGroup.appendChild(createFontFamilySelector());

    const headingGroup = createDropdownGroup('Headings');
    headingGroup.appendChild(createFormattingButton('formatBlock', 'H2', 'H2', 'Heading 2'));
    headingGroup.appendChild(createFormattingButton('formatBlock', 'H3', 'H3', 'Heading 3'));
    headingGroup.appendChild(createFormattingButton('formatBlock', '¶', 'P', 'Paragraph'));
    const formattingDropdownChildren = [formattingGroup, alignmentGroup, fontSizeGroup, fontFamilyGroup, headingGroup];
    const formatDropdownId = typeof 'Stickara-format-dropdown-btn' !== 'undefined' ? 'Stickara-format-dropdown-btn' : null; // Safe ID check
    const formattingDropdown = createDropdown('Format', formattingDropdownChildren, { icon: '🎨', id: formatDropdownId });


    // --- Appearance (Style) Dropdown ---
    const appearanceColorGroup = createDropdownGroup('Color');
    createColorSwatches().forEach(sw => appearanceColorGroup.appendChild(sw));
    const appearanceOpacityGroup = createDropdownGroup('Opacity');
    createOpacityButtons().forEach(btn => appearanceOpacityGroup.appendChild(btn));
    const appearanceEffectsGroup = createDropdownGroup('Effects');
    appearanceEffectsGroup.appendChild(createTextShadowButton());
    const styleDropdownId = typeof 'Stickara-style-dropdown-btn' !== 'undefined' ? 'Stickara-style-dropdown-btn' : null; // Safe ID check
    const appearanceDropdown = createDropdown('Style', [appearanceColorGroup, appearanceOpacityGroup, appearanceEffectsGroup], { icon: '✨', id: styleDropdownId });

    // Template Selector (Grouped within Insert)
    const insertTemplateGroup = createDropdownGroup('Templates');
    insertTemplateGroup.appendChild(createTemplateSelector());

    // Add Template Builder button (primary template creation button)
    if (typeof openVisualTemplateBuilder === 'function') {
        const visualBuilderButton = createToolButton('🎨', 'Template Builder', 'Create templates with drag-and-drop (no HTML required)', openVisualTemplateBuilder);
        visualBuilderButton.style.cssText = `
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            font-weight: bold !important;
            border: 2px solid #5a67d8 !important;
        `;
        insertTemplateGroup.appendChild(visualBuilderButton);
    }




    // --- Insert Dropdown ---
    const insertToolsGroup = createDropdownGroup('Insert Tools'); // Standard tools group
    insertToolsGroup.appendChild(createToolButton('🖼️', 'Image', 'Insert image', handleImageSelectClick));
    insertToolsGroup.appendChild(createToolButton('∑', 'Equation', 'Insert equation', insertEquation));
    insertToolsGroup.appendChild(createToolButton('✏️', 'Diagram', 'Insert diagram', openDiagramEditor));

    // Page Title & Heading button removed from insert dropdown as requested
    // It's still available as a snippet in the Stickara toolbar

    // Add Element Picker button
    if (typeof toggleElementPicker === 'function') {
        insertToolsGroup.appendChild(createToolButton('🎯', 'Element Picker', 'Pick elements from the page to insert into note', toggleElementPicker));
    } else {
        console.warn("Stickara: toggleElementPicker function not found, Element Picker button not added.");
    }

    // Spacebar button removed as requested by user

    // Screenshot Section (Grouped within Insert)
    const screenshotGroup = createDropdownGroup('Screenshot');
    // Check if the primary handler exists
    if (typeof handleCaptureScreenshot === 'function') {
        // Screenshot Trigger Button (within the group)
        const screenshotButton = createToolButton(
            '📸', // Emoji
            'Screenshot',            // Text part - will be styled to fit properly
            'Capture visible part of the page', // Tooltip
            handleCaptureScreenshot // Calls the handler which reads options
        );
        screenshotButton.classList.add('Stickara-dropdown-screenshot-btn'); // Optional specific class
        screenshotGroup.appendChild(screenshotButton);

        // Container for Checkbox Options
        const optionsContainer = document.createElement('div');
        optionsContainer.classList.add('Stickara-dropdown-screenshot-options'); // For styling

        // Helper to create label + checkbox inside the dropdown group
        const createOptionCheckbox = (id, labelText, title) => {
            const label = document.createElement('label');
            label.htmlFor = id; label.title = title;
            label.classList.add('Stickara-dropdown-checkbox-label'); // Style hook
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox'; checkbox.id = id;
            checkbox.classList.add('Stickara-dropdown-checkbox'); // Style hook
            label.appendChild(checkbox);
            label.appendChild(document.createTextNode(` ${labelText}`));
            // Prevent clicks on the label/checkbox from closing the dropdown
            label.addEventListener('click', (e) => e.stopPropagation());
            return label;
        };

        // --- Use consistent IDs defined in content-ui.js ---
        const optDownloadId = typeof SCREENSHOT_OPTION_DOWNLOAD_ID !== 'undefined' ? SCREENSHOT_OPTION_DOWNLOAD_ID : 'screenshot-option-download';
        const optHideUiId = 'Stickara-screenshot-option-hide-ui';
        const optFullPageId = 'Stickara-screenshot-option-fullpage'; // <<< NEW ID
        const optSelectedAreaId = 'Stickara-screenshot-option-selected-area'; // <<< SELECTED AREA ID

        // Create and Append Checkboxes
        optionsContainer.appendChild(createOptionCheckbox(optDownloadId, 'Download Directly', 'Download immediately (skip annotation)'));
        optionsContainer.appendChild(createOptionCheckbox(optHideUiId, 'Hide Note', 'Hide Stickara UI during capture'));
        optionsContainer.appendChild(createOptionCheckbox(optFullPageId, 'Full Page', 'Capture entire scrollable page (Experimental)')); // <<< ADDED CHECKBOX
        optionsContainer.appendChild(createOptionCheckbox(optSelectedAreaId, 'Selected Area', 'Drag to select a specific area for capture')); // <<< SELECTED AREA CHECKBOX
        screenshotGroup.appendChild(optionsContainer); // Add options container to the screenshot group

    } else { // Handle missing screenshot function
        console.error("Stickara Error: handleCaptureScreenshot function not found, cannot add Screenshot section.");
        const disabledItem = document.createElement('span'); disabledItem.textContent = "Screenshot Unavailable";
        disabledItem.style.color = "#999"; disabledItem.style.fontSize = "12px"; disabledItem.style.padding = "8px 10px"; disabledItem.style.display = 'block';
        screenshotGroup.appendChild(disabledItem);
    }

    // Assemble children for the Insert dropdown
    const insertDropdownChildren = [insertToolsGroup, insertTemplateGroup, screenshotGroup];
    const insertDropdownId = typeof 'Stickara-insert-dropdown-btn' !== 'undefined' ? 'Stickara-insert-dropdown-btn' : null; // Safe ID check
    const insertDropdown = createDropdown('Insert', insertDropdownChildren, { icon: '➕', id: insertDropdownId });


    // --- Tools Dropdown ---
    // Use constants safely for IDs
    const flashcardStudyBtnId = typeof FLASHCARD_STUDY_BTN_ID !== 'undefined' ? FLASHCARD_STUDY_BTN_ID : null;
    const voiceRecordBtnId = typeof VOICE_RECORD_BTN_ID !== 'undefined' ? VOICE_RECORD_BTN_ID : null;
    const flashcardGroup = createDropdownGroup('Flashcards');
    flashcardGroup.appendChild(createToolButton('🎓', 'Study Q/A', 'Review Q/A cards from note text', openFlashcardModal, flashcardStudyBtnId));
    const voiceGroup = createDropdownGroup('Voice');
    voiceGroup.appendChild(createToolButton('🎤', 'Voice Typing', 'Start/Stop voice recording', toggleRecording, voiceRecordBtnId));
    const formatGroup = createDropdownGroup('Formatting');
    formatGroup.appendChild(createToolButton('☑️', 'Checklist', 'Convert Selection to Checklist', handleConvertSelectionToChecklist));

    const toolsDropdownChildren = [flashcardGroup, voiceGroup, formatGroup];
    const toolsDropdownId = typeof 'Stickara-tools-dropdown-btn' !== 'undefined' ? 'Stickara-tools-dropdown-btn' : null; // Safe ID check
    const toolsDropdown = createDropdown('Tools', toolsDropdownChildren, { icon: '🛠️', id: toolsDropdownId });


    // --- View Controls Dropdown ---
    // Use constants safely for IDs
    const pinBtnId = typeof 'Stickara-pin-btn' !== 'undefined' ? 'Stickara-pin-btn' : null;
    const globalPinBtnId = typeof 'Stickara-global-pin-btn' !== 'undefined' ? 'Stickara-global-pin-btn' : null;
    // Minimize button ID removed as the button is no longer used
    const globalNoteBtnId = 'Stickara-global-note-btn';
    const viewControlsGroup = createDropdownGroup();
    viewControlsGroup.appendChild(createToolButton('📌', 'Pin', 'Pin/Unpin note for this page', togglePin, pinBtnId)); // Icon only
    viewControlsGroup.appendChild(createToolButton('⭐', 'Important', 'Mark/Unmark as important note', toggleGlobalPin, globalPinBtnId)); // Icon only

    // Add separate buttons for global and URL-specific notes
    const urlNoteBtnId = 'Stickara-url-note-btn';
    viewControlsGroup.appendChild(createToolButton('🔗', 'URL Note', 'Show URL-specific note (only for this page)', showUrlNote, urlNoteBtnId)); // Icon only
    viewControlsGroup.appendChild(createToolButton('🌐', 'Global Note', 'Show global note (works across all URLs)', showGlobalNote, globalNoteBtnId)); // Icon only

    viewControlsGroup.appendChild(createToolButton('📋', 'Copy Note Text', 'Copy note text to clipboard', copyNoteText)); // Icon only
    // Minimize button removed as requested
    viewControlsGroup.appendChild(createToolButton('❌', 'Close', 'Close note window', hideNote)); // Icon only



    const viewControlsDropdownId = typeof 'Stickara-view-dropdown-btn' !== 'undefined' ? 'Stickara-view-dropdown-btn' : null; // Safe ID check
    const viewControlsDropdown = createDropdown('View', [viewControlsGroup], { icon: '👁️', id: viewControlsDropdownId });

    // Append all dropdowns to the wrapper
    controlsWrapper.appendChild(formattingDropdown);
    controlsWrapper.appendChild(appearanceDropdown);
    controlsWrapper.appendChild(insertDropdown); // Add the modified Insert dropdown
    controlsWrapper.appendChild(toolsDropdown);
    controlsWrapper.appendChild(viewControlsDropdown);



    return controlsWrapper;
}

console.log("Stickara: Toolbar Logic Loaded (v4.0 - Page Info Button)");
// --- END OF FILE content-toolbar.js ---