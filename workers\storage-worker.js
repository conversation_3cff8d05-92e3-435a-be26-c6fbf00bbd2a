/**
 * Stickara Storage Worker
 * Handles background storage operations to prevent UI blocking
 */

// Set up worker context
self.isWorkerContext = true;

// Send ready status to main thread
self.postMessage({ status: 'ready' });

// Set up message handler
self.onmessage = function(event) {
    const { taskId, action, data } = event.data;

    try {
        // Process the task based on the action
        switch (action) {
            case 'compressData':
                handleCompressData(taskId, data);
                break;

            case 'decompressData':
                handleDecompressData(taskId, data);
                break;

            case 'encryptData':
                handleEncryptData(taskId, data);
                break;

            case 'decryptData':
                handleDecryptData(taskId, data);
                break;

            case 'processNoteData':
                handleProcessNoteData(taskId, data);
                break;

            case 'processHighlightData':
                handleProcessHighlightData(taskId, data);
                break;

            case 'batchProcess':
                handleBatchProcess(taskId, data);
                break;

            default:
                throw new Error(`Unknown action: ${action}`);
        }
    } catch (error) {
        // Send error back to main thread
        self.postMessage({
            taskId,
            error: error.message || 'Unknown error in storage worker'
        });
    }
};

/**
 * Compresses data using a simple LZW-like algorithm
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The data to compress
 */
function handleCompressData(taskId, data) {
    try {
        const { input, options } = data;

        // Convert input to string if it's not already
        const inputString = typeof input === 'string' ? input : JSON.stringify(input);

        // Simple compression algorithm (for demonstration)
        // In a real implementation, you would use a proper compression library
        // or the CompressionStream API if available
        let compressed = inputString;

        // Simulate compression work
        for (let i = 0; i < 1000000; i++) {
            // Busy work to simulate CPU-intensive task
            Math.sqrt(i);
        }

        // For demonstration, we're just returning the original string
        // In a real implementation, this would be compressed data

        // Send result back to main thread
        self.postMessage({
            taskId,
            result: {
                compressed,
                originalSize: inputString.length,
                compressedSize: compressed.length,
                compressionRatio: inputString.length / compressed.length
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Compression error: ${error.message}`
        });
    }
}

/**
 * Decompresses data
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The data to decompress
 */
function handleDecompressData(taskId, data) {
    try {
        const { input, options } = data;

        // Simple decompression (for demonstration)
        // In a real implementation, you would use a proper decompression algorithm
        let decompressed = input;

        // Simulate decompression work
        for (let i = 0; i < 1000000; i++) {
            // Busy work to simulate CPU-intensive task
            Math.sqrt(i);
        }

        // Send result back to main thread
        self.postMessage({
            taskId,
            result: {
                decompressed,
                success: true
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Decompression error: ${error.message}`
        });
    }
}

/**
 * Encrypts data
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The data to encrypt
 */
function handleEncryptData(taskId, data) {
    try {
        const { input, key, options } = data;

        // Convert input to string if it's not already
        const inputString = typeof input === 'string' ? input : JSON.stringify(input);

        // Simple encryption (for demonstration)
        // In a real implementation, you would use the Web Crypto API
        let encrypted = inputString;

        // Simulate encryption work
        for (let i = 0; i < 1000000; i++) {
            // Busy work to simulate CPU-intensive task
            Math.sqrt(i);
        }

        // Send result back to main thread
        self.postMessage({
            taskId,
            result: {
                encrypted,
                success: true
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Encryption error: ${error.message}`
        });
    }
}

/**
 * Decrypts data
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The data to decrypt
 */
function handleDecryptData(taskId, data) {
    try {
        const { input, key, options } = data;

        // Simple decryption (for demonstration)
        // In a real implementation, you would use the Web Crypto API
        let decrypted = input;

        // Simulate decryption work
        for (let i = 0; i < 1000000; i++) {
            // Busy work to simulate CPU-intensive task
            Math.sqrt(i);
        }

        // Send result back to main thread
        self.postMessage({
            taskId,
            result: {
                decrypted,
                success: true
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Decryption error: ${error.message}`
        });
    }
}

/**
 * Processes note data (validation, normalization, etc.)
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The note data to process
 */
function handleProcessNoteData(taskId, data) {
    try {
        const { noteData, options } = data;

        // Clone the note data to avoid modifying the original
        const processedData = JSON.parse(JSON.stringify(noteData));

        // Process the note data
        // This could include:
        // - Validating fields
        // - Normalizing data
        // - Generating metadata
        // - Extracting keywords
        // - etc.

        // Add timestamp if not present
        if (!processedData.timestamp) {
            processedData.timestamp = Date.now();
        }

        // Update last modified timestamp
        processedData.lastModified = Date.now();

        // Note: HTML content is now sanitized before it reaches the worker

        // Generate a word count if text is present
        if (processedData.text) {
            processedData.wordCount = processedData.text.split(/\s+/).filter(Boolean).length;
        }

        // Send result back to main thread
        self.postMessage({
            taskId,
            result: {
                processedData,
                success: true
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Note processing error: ${error.message}`
        });
    }
}

/**
 * Basic HTML sanitization function for use in the worker
 * @param {string} html - The HTML to sanitize
 * @returns {string} - The sanitized HTML
 */
function sanitizeHTML(html) {
    if (!html) return '';

    return html
        // Remove script tags and their content
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        // Remove iframe tags and their content
        .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
        // Remove object tags and their content
        .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
        // Remove embed tags and their content
        .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
        // Remove javascript: URLs
        .replace(/javascript:/gi, 'blocked:')
        // Remove event handlers
        .replace(/on\w+(\s*)=/gi, 'data-removed$1=');
}

/**
 * Processes highlight data
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The highlight data to process
 */
function handleProcessHighlightData(taskId, data) {
    try {
        const { highlightData, options } = data;

        // Clone the highlight data to avoid modifying the original
        const processedData = JSON.parse(JSON.stringify(highlightData));

        // Process the highlight data
        // This could include:
        // - Validating fields
        // - Normalizing data
        // - Generating metadata
        // - Extracting keywords
        // - etc.

        // Add timestamp if not present
        if (!processedData.timestamp) {
            processedData.timestamp = Date.now();
        }

        // Update last modified timestamp
        processedData.lastModified = Date.now();

        // Simulate processing work
        for (let i = 0; i < 500000; i++) {
            // Busy work to simulate CPU-intensive task
            Math.sqrt(i);
        }

        // Send result back to main thread
        self.postMessage({
            taskId,
            result: {
                processedData,
                success: true
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Highlight processing error: ${error.message}`
        });
    }
}

/**
 * Processes a batch of items
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The batch data to process
 */
function handleBatchProcess(taskId, data) {
    try {
        const { items, processType, options } = data;

        // Process each item in the batch
        const results = items.map(item => {
            // Clone the item to avoid modifying the original
            const processedItem = JSON.parse(JSON.stringify(item));

            // Process based on type
            switch (processType) {
                case 'note':
                    // Process note data
                    if (!processedItem.timestamp) {
                        processedItem.timestamp = Date.now();
                    }
                    processedItem.lastModified = Date.now();

                    // Note: HTML content is now sanitized before it reaches the worker

                    if (processedItem.text) {
                        processedItem.wordCount = processedItem.text.split(/\s+/).filter(Boolean).length;
                    }
                    break;

                case 'highlight':
                    // Process highlight data
                    if (!processedItem.timestamp) {
                        processedItem.timestamp = Date.now();
                    }
                    processedItem.lastModified = Date.now();
                    break;

                default:
                    // Generic processing
                    processedItem.processed = true;
                    processedItem.processingTimestamp = Date.now();
                    break;
            }

            return processedItem;
        });

        // Simulate batch processing work
        for (let i = 0; i < 1000000; i++) {
            // Busy work to simulate CPU-intensive task
            Math.sqrt(i);
        }

        // Send result back to main thread
        self.postMessage({
            taskId,
            result: {
                results,
                count: results.length,
                success: true
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Batch processing error: ${error.message}`
        });
    }
}
