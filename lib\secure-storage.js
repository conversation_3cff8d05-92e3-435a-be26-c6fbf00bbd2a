// --- START OF FILE secure-storage.js ---

/**
 * Secure Storage Utility for Stickara
 * Provides secure wrappers around chrome.storage and localStorage operations
 * with input validation, sanitization, and encryption capabilities
 */

// Create a namespace to avoid global pollution
window.StickaraSecureStorage = (function() {

    /**
     * Validates and sanitizes data before storage
     * @param {any} data - The data to validate and sanitize
     * @returns {any} - The validated and sanitized data
     */
    function validateData(data) {
        // Check for null/undefined
        if (data === null || data === undefined) {
            // Just log at debug level to reduce noise
            console.log("Stickara: Validating null/undefined data - allowed");
            // Allow null/undefined to be stored
            return data;
        }

        // Handle primitive types directly
        if (typeof data === 'number' || typeof data === 'boolean' || data === '') {
            return data;
        }

        // Sanitize strings, especially those that might contain HTML
        if (typeof data === 'string') {
            // For very long strings, check if they might contain HTML
            if (data.includes('<') && data.includes('>')) {
                console.log("Stickara: String contains potential HTML, sanitizing");

                try {
                    // Try DOMPurify first (global instance)
                    if (window.DOMPurify) {
                        return window.DOMPurify.sanitize(data);
                    }

                    // Try StickaraDOMPurify next
                    if (window.StickaraDOMPurify) {
                        return window.StickaraDOMPurify.sanitize(data);
                    }

                    // Try StickaraSanitizationManager's fallback
                    if (window.StickaraSanitizationManager &&
                        typeof window.StickaraSanitizationManager.fallbackSanitize === 'function') {
                        return window.StickaraSanitizationManager.fallbackSanitize(data);
                    }
                } catch (e) {
                    console.error('Stickara: Error using sanitization libraries:', e);
                    // Continue to fallback
                }

                // Fallback to basic sanitization if all else fails
                console.log('Stickara: Using basic sanitization in validateData');
                return data.replace(/<script\b[^>]*>([\s\S]*?)<\/script>/gi, '')
                           .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
                           .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
                           .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
                           .replace(/javascript:/gi, 'blocked:')
                           .replace(/on\w+\s*=\s*["']?[^"']*["']?/gi, '');
            }

            return data;
        }

        // For objects, recursively validate and sanitize each property
        if (typeof data === 'object' && !Array.isArray(data) && data !== null) {
            // Create a sanitized copy of the object
            const sanitizedData = {};

            // Special handling for note data objects
            const isNoteData = data.hasOwnProperty('text') &&
                              (data.hasOwnProperty('timestamp') || data.hasOwnProperty('lastModified'));

            // List of keys that need special HTML sanitization
            const htmlContentKeys = ['text', 'noteText', 'html', 'content'];

            // Process each property
            for (const key in data) {
                if (Object.prototype.hasOwnProperty.call(data, key)) {
                    // Special handling for HTML content in notes
                    if (isNoteData && htmlContentKeys.includes(key) && typeof data[key] === 'string') {
                        // For note HTML content, apply thorough sanitization
                        console.log(`Stickara: Sanitizing HTML content in note data (${key})`);

                        try {
                            // Try DOMPurify first (global instance)
                            if (window.DOMPurify) {
                                sanitizedData[key] = window.DOMPurify.sanitize(data[key]);
                            }
                            // Try StickaraDOMPurify next
                            else if (window.StickaraDOMPurify) {
                                sanitizedData[key] = window.StickaraDOMPurify.sanitize(data[key]);
                            }
                            // Try StickaraSanitizationManager's fallback
                            else if (window.StickaraSanitizationManager &&
                                typeof window.StickaraSanitizationManager.fallbackSanitize === 'function') {
                                sanitizedData[key] = window.StickaraSanitizationManager.fallbackSanitize(data[key]);
                            }
                            else {
                                // Fallback to basic sanitization
                                sanitizedData[key] = data[key]
                                    .replace(/<script\b[^>]*>([\s\S]*?)<\/script>/gi, '')
                                    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
                                    .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
                                    .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
                                    .replace(/javascript:/gi, 'blocked:')
                                    .replace(/on\w+\s*=\s*["']?[^"']*["']?/gi, '');
                            }
                        } catch (e) {
                            console.error(`Stickara: Error sanitizing HTML in note data (${key}):`, e);
                            // Use a very basic fallback
                            sanitizedData[key] = String(data[key]).replace(/</g, '&lt;').replace(/>/g, '&gt;');
                        }
                    } else {
                        // For other properties, recursively validate and sanitize
                        sanitizedData[key] = validateData(data[key]);
                    }
                }
            }

            return sanitizedData;
        }

        // For arrays, validate and sanitize each item
        if (Array.isArray(data)) {
            // Create a sanitized copy of the array
            const sanitizedArray = [];

            // Process each item
            for (let i = 0; i < data.length; i++) {
                // Recursively validate and sanitize each item
                sanitizedArray[i] = validateData(data[i]);
            }

            return sanitizedArray;
        }

        // If we get here, return the data as is
        return data;
    }

    /**
     * Sanitizes keys to prevent injection attacks
     * @param {string} key - The storage key
     * @returns {string} - The sanitized key
     */
    function sanitizeKey(key) {
        if (typeof key !== 'string') {
            console.error("Stickara: Storage key must be a string");
            return '';
        }

        // Remove potentially dangerous characters from keys
        return key.replace(/[^\w\d_\-\.]/g, '_');
    }

    /**
     * Securely saves data to chrome.storage.local
     * @param {string} key - The key to store under
     * @param {any} data - The data to store
     * @param {Function} callback - Optional callback function
     */
    function secureSet(key, data, callback) {
        // Validate the key
        const safeKey = sanitizeKey(key);
        if (!safeKey) {
            console.log(`Stickara: Invalid storage key: ${key}`);
            if (callback) callback(new Error("Invalid storage key"));
            return;
        }

        // Handle null/undefined data explicitly
        if (data === null || data === undefined) {
            console.log(`Stickara: Storing null/undefined data for key: ${safeKey}`);
            // We'll allow this but log it
            const storageObj = {};
            storageObj[safeKey] = data;

            chrome.storage.local.set(storageObj, () => {
                const error = chrome.runtime.lastError;
                if (error) {
                    console.log(`Stickara: Error in secureSet for key ${safeKey}:`, error.message);
                    if (callback) callback(error);
                } else {
                    if (callback) callback(null);
                }
            });
            return;
        }

        // Sanitize the data
        let sanitizedData;
        try {
            sanitizedData = validateData(data);
        } catch (e) {
            console.log(`Stickara: Exception in validateData:`, e);
            // Use original data as fallback
            sanitizedData = data;
        }

        // Store the data
        try {
            const storageObj = {};
            storageObj[safeKey] = sanitizedData;

            chrome.storage.local.set(storageObj, () => {
                const error = chrome.runtime.lastError;
                if (error) {
                    console.log(`Stickara: Error in secureSet for key ${safeKey}:`, error.message);
                    if (callback) callback(error);
                } else {
                    if (callback) callback(null);
                }
            });
        } catch (e) {
            console.log(`Stickara: Exception in secureSet:`, e);
            if (callback) callback(new Error("Storage failed"));
        }
    }

    /**
     * Securely retrieves data from chrome.storage.local
     * @param {string|Array<string>} keys - The key(s) to retrieve
     * @param {Function} callback - Callback function(result, error)
     */
    function secureGet(keys, callback) {
        // Handle single key or array of keys
        let safeKeys;

        if (typeof keys === 'string') {
            safeKeys = sanitizeKey(keys);
            if (!safeKeys) {
                if (callback) callback(null, new Error("Invalid storage key"));
                return;
            }
        } else if (Array.isArray(keys)) {
            safeKeys = keys.map(sanitizeKey).filter(Boolean);
            if (safeKeys.length === 0) {
                if (callback) callback({}, new Error("No valid storage keys provided"));
                return;
            }
        } else {
            if (callback) callback(null, new Error("Keys must be a string or array of strings"));
            return;
        }

        // Retrieve the data
        chrome.storage.local.get(safeKeys, (result) => {
            const error = chrome.runtime.lastError;
            if (error) {
                console.error(`Stickara: Error in secureGet:`, error.message);
                if (callback) callback(null, error);
            } else {
                if (callback) callback(result, null);
            }
        });
    }

    /**
     * Securely removes data from chrome.storage.local
     * @param {string|Array<string>} keys - The key(s) to remove
     * @param {Function} callback - Optional callback function
     */
    function secureRemove(keys, callback) {
        // Handle single key or array of keys
        let safeKeys;

        if (typeof keys === 'string') {
            safeKeys = sanitizeKey(keys);
            if (!safeKeys) {
                if (callback) callback(new Error("Invalid storage key"));
                return;
            }
        } else if (Array.isArray(keys)) {
            safeKeys = keys.map(sanitizeKey).filter(Boolean);
            if (safeKeys.length === 0) {
                if (callback) callback(new Error("No valid storage keys provided"));
                return;
            }
        } else {
            if (callback) callback(new Error("Keys must be a string or array of strings"));
            return;
        }

        // Remove the data
        chrome.storage.local.remove(safeKeys, () => {
            const error = chrome.runtime.lastError;
            if (error) {
                console.error(`Stickara: Error in secureRemove:`, error.message);
                if (callback) callback(error);
            } else {
                if (callback) callback(null);
            }
        });
    }

    /**
     * Securely saves data to localStorage with validation
     * @param {string} key - The key to store under
     * @param {any} data - The data to store
     * @returns {boolean} - Whether the operation was successful
     */
    function secureLocalStorageSet(key, data) {
        try {
            // Validate the key
            const safeKey = sanitizeKey(key);
            if (!safeKey) {
                console.log(`Stickara: Invalid localStorage key: ${key}`);
                return false;
            }

            // Handle null/undefined data explicitly
            if (data === null || data === undefined) {
                console.log(`Stickara: Storing null/undefined data in localStorage for key: ${safeKey}`);
                // For null/undefined, we'll store a special string
                localStorage.setItem(safeKey, data === null ? "null" : "undefined");
                return true;
            }

            // Validate and sanitize the data
            try {
                // validateData now returns the sanitized data
                const sanitizedData = validateData(data);

                // Convert sanitized data to JSON string
                const jsonData = JSON.stringify(sanitizedData);

                // Store in localStorage
                localStorage.setItem(safeKey, jsonData);
                return true;
            } catch (jsonError) {
                // If JSON stringification fails, try to store as string
                console.log(`Stickara: JSON stringify failed, storing as string:`, jsonError);
                try {
                    localStorage.setItem(safeKey, String(data));
                    return true;
                } catch (stringError) {
                    console.log(`Stickara: String storage failed:`, stringError);
                    return false;
                }
            }
        } catch (e) {
            console.log(`Stickara: Error in secureLocalStorageSet:`, e);
            return false;
        }
    }

    /**
     * Securely retrieves data from localStorage
     * @param {string} key - The key to retrieve
     * @returns {any} - The retrieved data or null if not found/error
     */
    function secureLocalStorageGet(key) {
        try {
            // Validate the key
            const safeKey = sanitizeKey(key);
            if (!safeKey) {
                console.log(`Stickara: Invalid localStorage key for get: ${key}`);
                return null;
            }

            // Retrieve from localStorage
            const data = localStorage.getItem(safeKey);
            if (data === null) {
                return null;
            }

            // Handle special strings for null/undefined
            if (data === "null") {
                return null;
            }
            if (data === "undefined") {
                return undefined;
            }

            // Try to parse as JSON
            try {
                return JSON.parse(data);
            } catch (jsonError) {
                // If JSON parsing fails, return the raw string
                console.log(`Stickara: JSON parse failed, returning raw string:`, jsonError);
                return data;
            }
        } catch (e) {
            console.log(`Stickara: Error in secureLocalStorageGet:`, e);
            return null;
        }
    }

    /**
     * Securely removes data from localStorage
     * @param {string} key - The key to remove
     * @returns {boolean} - Whether the operation was successful
     */
    function secureLocalStorageRemove(key) {
        try {
            // Validate the key
            const safeKey = sanitizeKey(key);
            if (!safeKey) {
                return false;
            }

            // Remove from localStorage
            localStorage.removeItem(safeKey);
            return true;
        } catch (e) {
            console.error(`Stickara: Error in secureLocalStorageRemove:`, e);
            return false;
        }
    }

    // Return the public API
    return {
        // Chrome storage methods
        set: secureSet,
        get: secureGet,
        remove: secureRemove,

        // localStorage methods
        localSet: secureLocalStorageSet,
        localGet: secureLocalStorageGet,
        localRemove: secureLocalStorageRemove,

        // Utility methods
        validateData: validateData,
        sanitizeKey: sanitizeKey
    };
})();

console.log("Stickara: Secure Storage Utilities Loaded");
// --- END OF FILE secure-storage.js ---
