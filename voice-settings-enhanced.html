<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Stickara Voice Settings</title>
    <link rel="stylesheet" href="popup.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #34D399;
            --primary-hover: #10B981;
            --primary-light: #D1FAE5;
            --secondary-color: #6B7280;
            --secondary-light: #F3F4F6;
            --secondary-lighter: #F9FAFB;
            --border-color: #E5E7EB;
            --text-primary: #1F2937;
            --text-secondary: #4B5563;
            --text-light: #9CA3AF;
            --danger-color: #EF4444;
            --warning-color: #F59E0B;
            --info-color: #3B82F6;
            --success-color: #10B981;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --transition: all 0.2s ease;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            color: var(--text-primary);
            background-color: #ffffff;
            line-height: 1.5;
            padding: 0;
            margin: 0;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0;
        }

        header {
            background-color: var(--primary-color);
            color: white;
            padding: 20px;
            box-shadow: var(--shadow-md);
            position: relative;
        }

        header h1 {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin-top: 4px;
            font-weight: 400;
        }

        .back-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            border-radius: var(--radius-sm);
            padding: 8px 12px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: var(--transition);
            text-decoration: none;
        }

        .back-button:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        main {
            padding: 20px;
        }

        .settings-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        @media (min-width: 768px) {
            .settings-container {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .setting-card {
            background-color: white;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: var(--transition);
        }

        .setting-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .card-header {
            background-color: var(--secondary-lighter);
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-header h2 {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            color: var(--text-primary);
        }

        .card-header .icon {
            color: var(--primary-color);
            font-size: 18px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .card-body {
            padding: 16px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            font-size: 14px;
            color: var(--text-secondary);
        }

        select,
        input[type="text"],
        input[type="password"],
        input[type="number"] {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            font-size: 14px;
            color: var(--text-primary);
            background-color: white;
            transition: var(--transition);
        }

        select:focus,
        input[type="text"]:focus,
        input[type="password"]:focus,
        input[type="number"]:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        .checkbox-group {
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .checkbox-group:last-child {
            margin-bottom: 0;
        }

        .checkbox-group label {
            display: flex;
            align-items: center;
            margin-bottom: 0;
            cursor: pointer;
            user-select: none;
        }

        .checkbox-container {
            position: relative;
            display: inline-block;
            width: 18px;
            height: 18px;
            margin-right: 10px;
        }

        .checkbox-container input {
            opacity: 0;
            width: 0;
            height: 0;
            position: absolute;
        }

        .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: 3px;
            transition: var(--transition);
        }

        .checkbox-container input:checked ~ .checkmark {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
            left: 6px;
            top: 2px;
            width: 4px;
            height: 9px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .checkbox-container input:checked ~ .checkmark:after {
            display: block;
        }

        .checkbox-container input:focus ~ .checkmark {
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        .api-key-container {
            background-color: var(--secondary-lighter);
            border-radius: var(--radius-sm);
            padding: 12px;
            margin-top: 12px;
            border: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .api-key-container.visible {
            display: block;
        }

        .api-key-container.hidden {
            display: none;
        }

        .api-key-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .api-key-header label {
            margin-bottom: 0;
        }

        .toggle-password {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 14px;
        }

        .toggle-password:hover {
            color: var(--text-primary);
        }

        .help-text {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .provider-info {
            margin-top: 12px;
            font-size: 13px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .provider-info a {
            color: var(--info-color);
            text-decoration: none;
        }

        .provider-info a:hover {
            text-decoration: underline;
        }

        .slider-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .slider-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .slider-value {
            font-size: 14px;
            font-weight: 500;
            color: var(--primary-color);
            background-color: var(--primary-light);
            padding: 2px 8px;
            border-radius: 12px;
        }

        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: var(--secondary-light);
            outline: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            transition: var(--transition);
        }

        .slider::-webkit-slider-thumb:hover {
            background: var(--primary-hover);
            transform: scale(1.1);
        }

        .slider::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            transition: var(--transition);
            border: none;
        }

        .slider::-moz-range-thumb:hover {
            background: var(--primary-hover);
            transform: scale(1.1);
        }

        .action-bar {
            background-color: var(--secondary-lighter);
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid var(--border-color);
            margin-top: 20px;
        }

        .primary-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--radius-sm);
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .primary-button:hover {
            background-color: var(--primary-hover);
        }

        .secondary-button {
            background-color: white;
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
        }

        .secondary-button:hover {
            background-color: var(--secondary-lighter);
            color: var(--text-primary);
        }

        .status-message {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: var(--radius-sm);
            font-size: 14px;
            font-weight: 500;
            box-shadow: var(--shadow-md);
            display: flex;
            align-items: center;
            gap: 8px;
            transform: translateY(100px);
            opacity: 0;
            transition: transform 0.3s ease, opacity 0.3s ease;
            z-index: 1000;
        }

        .status-message.success {
            background-color: var(--success-color);
            color: white;
        }

        .status-message.error {
            background-color: var(--danger-color);
            color: white;
        }

        .status-message.info {
            background-color: var(--info-color);
            color: white;
        }

        .status-message.visible {
            transform: translateY(0);
            opacity: 1;
        }

        .provider-logo {
            width: 24px;
            height: 24px;
            object-fit: contain;
            margin-right: 8px;
        }

        .provider-option {
            display: flex;
            align-items: center;
        }

        .feature-badge {
            display: inline-block;
            font-size: 10px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: 6px;
            text-transform: uppercase;
        }

        .feature-badge.free {
            background-color: var(--primary-light);
            color: var(--primary-hover);
        }

        .feature-badge.premium {
            background-color: #FEF3C7;
            color: #D97706;
        }

        .tooltip {
            position: relative;
            display: inline-block;
            margin-left: 4px;
            color: var(--text-light);
        }

        .tooltip .tooltip-text {
            visibility: hidden;
            width: 200px;
            background-color: var(--text-primary);
            color: white;
            text-align: center;
            border-radius: var(--radius-sm);
            padding: 8px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            font-weight: normal;
            box-shadow: var(--shadow-md);
        }

        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }

        .tooltip .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: var(--text-primary) transparent transparent transparent;
        }

        @media (max-width: 767px) {
            .settings-container {
                grid-template-columns: 1fr;
            }

            header h1 {
                font-size: 20px;
            }

            .back-button {
                padding: 6px 10px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>
                <i class="fas fa-microphone"></i>
                Voice Transcription Settings
            </h1>
            <div class="header-subtitle">Configure speech recognition options for Stickara</div>
            <a href="popup.html" class="back-button">
                <i class="fas fa-arrow-left"></i>
                Back to Stickara
            </a>
        </header>

        <main>
            <div class="settings-container">
                <!-- Provider Card -->
                <div class="setting-card">
                    <div class="card-header">
                        <div class="icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <h2>Speech Recognition Provider</h2>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="voice-provider">Select Provider:</label>
                            <select id="voice-provider">
                                <option value="browser" data-logo="browser">Browser Speech API (Default)</option>
                                <option value="google" data-logo="google">Google Speech-to-Text</option>
                                <option value="azure" data-logo="azure">Azure Speech Service</option>
                                <option value="assembly" data-logo="assembly">AssemblyAI</option>
                            </select>
                            <p class="help-text">Choose the service that will convert your speech to text.</p>
                        </div>

                        <div id="provider-info-browser" class="provider-info">
                            <i class="fas fa-info-circle"></i>
                            Built-in browser capability. No API key required.
                            <span class="feature-badge free">Free</span>
                        </div>

                        <div id="provider-info-google" class="provider-info" style="display: none;">
                            <i class="fas fa-info-circle"></i>
                            Google's advanced speech recognition.
                            <a href="https://cloud.google.com/speech-to-text" target="_blank">Learn more</a>
                            <span class="feature-badge premium">Premium</span>
                        </div>

                        <div id="provider-info-azure" class="provider-info" style="display: none;">
                            <i class="fas fa-info-circle"></i>
                            Microsoft's AI-powered speech service.
                            <a href="https://azure.microsoft.com/en-us/services/cognitive-services/speech-services/" target="_blank">Learn more</a>
                            <span class="feature-badge premium">Premium</span>
                        </div>

                        <div id="provider-info-assembly" class="provider-info" style="display: none;">
                            <i class="fas fa-info-circle"></i>
                            Specialized speech-to-text API.
                            <a href="https://www.assemblyai.com/" target="_blank">Learn more</a>
                            <span class="feature-badge premium">Premium</span>
                        </div>

                        <div id="api-key-container" class="api-key-container hidden">
                            <div class="api-key-header">
                                <label for="voice-api-key">API Key:</label>
                                <button type="button" id="toggle-password" class="toggle-password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <input type="password" id="voice-api-key" placeholder="Enter your API key">
                            <p class="help-text">Your API key is stored locally and never shared.</p>
                        </div>
                    </div>
                </div>

                <!-- Language Card -->
                <div class="setting-card">
                    <div class="card-header">
                        <div class="icon">
                            <i class="fas fa-language"></i>
                        </div>
                        <h2>Language Settings</h2>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="voice-language">Recognition Language:</label>
                            <select id="voice-language">
                                <option value="en-US">English (United States)</option>
                                <option value="en-GB">English (United Kingdom)</option>
                                <option value="es-ES">Spanish (Spain)</option>
                                <option value="fr-FR">French (France)</option>
                                <option value="de-DE">German (Germany)</option>
                                <option value="it-IT">Italian (Italy)</option>
                                <option value="ja-JP">Japanese (Japan)</option>
                                <option value="ko-KR">Korean (South Korea)</option>
                                <option value="pt-BR">Portuguese (Brazil)</option>
                                <option value="ru-RU">Russian (Russia)</option>
                                <option value="zh-CN">Chinese (Simplified, China)</option>
                            </select>
                            <p class="help-text">Select the language you'll be speaking in.</p>
                        </div>

                        <div class="form-group">
                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-auto-detect">
                                        <span class="checkmark"></span>
                                    </span>
                                    Auto-detect language
                                </label>
                                <div class="tooltip">
                                    <i class="fas fa-question-circle"></i>
                                    <span class="tooltip-text">Only available with premium providers. May reduce accuracy.</span>
                                </div>
                            </div>
                            <p class="help-text">Attempt to automatically detect the spoken language.</p>
                        </div>
                    </div>
                </div>

                <!-- Advanced Options Card -->
                <div class="setting-card">
                    <div class="card-header">
                        <div class="icon">
                            <i class="fas fa-sliders-h"></i>
                        </div>
                        <h2>Advanced Options</h2>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-punctuation" checked>
                                        <span class="checkmark"></span>
                                    </span>
                                    Automatic punctuation
                                </label>
                            </div>

                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-capitalization" checked>
                                        <span class="checkmark"></span>
                                    </span>
                                    Automatic capitalization
                                </label>
                            </div>

                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-profanity-filter">
                                        <span class="checkmark"></span>
                                    </span>
                                    Filter profanity
                                </label>
                            </div>

                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-interim-results" checked>
                                        <span class="checkmark"></span>
                                    </span>
                                    Show interim results
                                </label>
                                <div class="tooltip">
                                    <i class="fas fa-question-circle"></i>
                                    <span class="tooltip-text">Display partial transcriptions while you're speaking.</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group slider-container">
                            <div class="slider-header">
                                <label for="voice-silence-threshold">Stop after silence:</label>
                                <span class="slider-value" id="silence-value">15 seconds</span>
                            </div>
                            <input type="range" min="5" max="60" value="15" class="slider" id="voice-silence-threshold">
                            <p class="help-text">Automatically stop recording after this period of silence.</p>
                        </div>
                    </div>
                </div>

                <!-- Performance Card -->
                <div class="setting-card">
                    <div class="card-header">
                        <div class="icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <h2>Performance & Quality</h2>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="voice-quality">Transcription Quality:</label>
                            <select id="voice-quality">
                                <option value="standard">Standard</option>
                                <option value="enhanced">Enhanced (Premium only)</option>
                                <option value="high">High (Premium only)</option>
                            </select>
                            <p class="help-text">Higher quality requires more processing time and may only be available with premium providers.</p>
                        </div>

                        <div class="form-group">
                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-background-noise-reduction">
                                        <span class="checkmark"></span>
                                    </span>
                                    Background noise reduction
                                </label>
                                <div class="tooltip">
                                    <i class="fas fa-question-circle"></i>
                                    <span class="tooltip-text">Reduce background noise in the audio. Premium providers only.</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-speaker-diarization">
                                        <span class="checkmark"></span>
                                    </span>
                                    Speaker identification
                                </label>
                                <div class="tooltip">
                                    <i class="fas fa-question-circle"></i>
                                    <span class="tooltip-text">Identify different speakers in the transcription. Premium providers only.</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Audio Processing Card -->
                <div class="setting-card">
                    <div class="card-header">
                        <div class="icon">
                            <i class="fas fa-volume-up"></i>
                        </div>
                        <h2>Audio Processing</h2>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-audio-monitoring" checked>
                                        <span class="checkmark"></span>
                                    </span>
                                    Real-time audio monitoring
                                </label>
                                <div class="tooltip">
                                    <i class="fas fa-question-circle"></i>
                                    <span class="tooltip-text">Show audio level indicator and quality feedback during recording.</span>
                                </div>
                            </div>

                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-adaptive-thresholds" checked>
                                        <span class="checkmark"></span>
                                    </span>
                                    Adaptive audio thresholds
                                </label>
                                <div class="tooltip">
                                    <i class="fas fa-question-circle"></i>
                                    <span class="tooltip-text">Automatically adjust audio sensitivity based on your environment.</span>
                                </div>
                            </div>

                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-enhanced-constraints" checked>
                                        <span class="checkmark"></span>
                                    </span>
                                    Enhanced audio constraints
                                </label>
                                <div class="tooltip">
                                    <i class="fas fa-question-circle"></i>
                                    <span class="tooltip-text">Use advanced microphone settings for better audio quality.</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group slider-container">
                            <div class="slider-header">
                                <label for="voice-audio-sensitivity">Audio sensitivity:</label>
                                <span class="slider-value" id="audio-sensitivity-value">50%</span>
                            </div>
                            <input type="range" min="10" max="100" value="50" class="slider" id="voice-audio-sensitivity">
                            <p class="help-text">Adjust microphone sensitivity for your environment.</p>
                        </div>

                        <div class="form-group">
                            <label for="voice-noise-suppression">Noise suppression level:</label>
                            <select id="voice-noise-suppression">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                                <option value="aggressive">Aggressive</option>
                            </select>
                            <p class="help-text">Level of background noise reduction to apply.</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <div class="action-bar">
            <button type="button" id="reset-settings" class="secondary-button">
                <i class="fas fa-undo"></i> Reset to Defaults
            </button>
            <button type="button" id="save-voice-settings" class="primary-button">
                <i class="fas fa-save"></i> Save Settings
            </button>
        </div>
    </div>

    <div id="status-message" class="status-message">
        <i class="fas fa-check-circle"></i>
        <span id="status-text">Settings saved successfully!</span>
    </div>

    <script src="voice-settings-enhanced.js"></script>
    <script>
        // Additional UI enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Provider selection
            const providerSelect = document.getElementById('voice-provider');
            const apiKeyContainer = document.getElementById('api-key-container');
            const providerInfos = {
                browser: document.getElementById('provider-info-browser'),
                google: document.getElementById('provider-info-google'),
                azure: document.getElementById('provider-info-azure'),
                assembly: document.getElementById('provider-info-assembly')
            };

            // Update provider info display
            function updateProviderInfo(provider) {
                Object.keys(providerInfos).forEach(key => {
                    providerInfos[key].style.display = key === provider ? 'flex' : 'none';
                });

                // Show/hide API key input
                if (provider === 'browser') {
                    apiKeyContainer.classList.remove('visible');
                    apiKeyContainer.classList.add('hidden');
                } else {
                    apiKeyContainer.classList.remove('hidden');
                    apiKeyContainer.classList.add('visible');
                }
            }

            providerSelect.addEventListener('change', function() {
                updateProviderInfo(this.value);
            });

            // Initialize provider info
            updateProviderInfo(providerSelect.value);

            // Toggle password visibility
            const togglePasswordBtn = document.getElementById('toggle-password');
            const apiKeyInput = document.getElementById('voice-api-key');

            togglePasswordBtn.addEventListener('click', function() {
                const type = apiKeyInput.getAttribute('type') === 'password' ? 'text' : 'password';
                apiKeyInput.setAttribute('type', type);

                // Update icon
                const icon = this.querySelector('i');
                if (type === 'text') {
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });

            // Silence threshold slider
            const silenceSlider = document.getElementById('voice-silence-threshold');
            const silenceValue = document.getElementById('silence-value');

            silenceSlider.addEventListener('input', function() {
                silenceValue.textContent = this.value + ' seconds';
            });

            // Audio sensitivity slider
            const audioSensitivitySlider = document.getElementById('voice-audio-sensitivity');
            const audioSensitivityValue = document.getElementById('audio-sensitivity-value');

            audioSensitivitySlider.addEventListener('input', function() {
                audioSensitivityValue.textContent = this.value + '%';
            });

            // Status message
            function showStatus(message, type = 'success') {
                const statusMessage = document.getElementById('status-message');
                const statusText = document.getElementById('status-text');

                statusText.textContent = message;
                statusMessage.className = 'status-message ' + type;
                statusMessage.classList.add('visible');

                setTimeout(() => {
                    statusMessage.classList.remove('visible');
                }, 3000);
            }

            // Reset button
            document.getElementById('reset-settings').addEventListener('click', function() {
                if (confirm('Reset all voice settings to default values?')) {
                    // Reset form to defaults
                    providerSelect.value = 'browser';
                    document.getElementById('voice-language').value = 'en-US';
                    document.getElementById('voice-punctuation').checked = true;
                    document.getElementById('voice-capitalization').checked = true;
                    document.getElementById('voice-profanity-filter').checked = false;
                    document.getElementById('voice-interim-results').checked = true;
                    document.getElementById('voice-auto-detect').checked = false;
                    document.getElementById('voice-background-noise-reduction').checked = false;
                    document.getElementById('voice-speaker-diarization').checked = false;
                    document.getElementById('voice-quality').value = 'standard';
                    silenceSlider.value = 15;
                    silenceValue.textContent = '15 seconds';
                    apiKeyInput.value = '';

                    // Reset audio processing settings
                    document.getElementById('voice-audio-monitoring').checked = true;
                    document.getElementById('voice-adaptive-thresholds').checked = true;
                    document.getElementById('voice-enhanced-constraints').checked = true;
                    document.getElementById('voice-audio-sensitivity').value = 50;
                    document.getElementById('audio-sensitivity-value').textContent = '50%';
                    document.getElementById('voice-noise-suppression').value = 'medium';

                    // Update UI
                    updateProviderInfo('browser');

                    showStatus('Settings reset to defaults', 'info');
                }
            });

            // Enhance the existing save button with visual feedback
            const originalSaveFunction = window.saveSettings;
            if (typeof originalSaveFunction === 'function') {
                window.saveSettings = function() {
                    const saveButton = document.getElementById('save-voice-settings');
                    const originalText = saveButton.innerHTML;

                    // Show loading state
                    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                    saveButton.disabled = true;

                    // Call the original save function
                    const result = originalSaveFunction();

                    // Simulate a delay for visual feedback
                    setTimeout(() => {
                        saveButton.innerHTML = originalText;
                        saveButton.disabled = false;
                        showStatus('Settings saved successfully!');
                    }, 800);

                    return result;
                };
            }

            // Attach the enhanced save function to the button
            document.getElementById('save-voice-settings').addEventListener('click', window.saveSettings);
        });
    </script>
</body>
</html>