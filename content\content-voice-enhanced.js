/**
 * Enhanced Voice Transcription Module
 * Provides multiple speech-to-text options with improved accuracy and features
 */

// API Configuration and State
const VOICE_API_CONFIG = {
    // Default is browser's Web Speech API (no API key needed)
    current: 'browser',

    // Available providers
    providers: {
        browser: {
            name: 'Browser Speech API',
            requiresKey: false,
            supportsLanguages: ['en-US', 'en-GB', 'es-ES', 'fr-FR', 'de-DE', 'it-IT', 'pt-BR', 'ru-RU', 'zh-CN', 'ja-<PERSON>'],
            defaultLanguage: 'en-US'
        },
        google: {
            name: 'Google Speech-to-Text',
            requiresKey: true,
            apiKeyName: 'googleSpeechApiKey',
            endpoint: 'https://speech.googleapis.com/v1/speech:recognize',
            supportsLanguages: ['en-US', 'en-GB', 'es-ES', 'fr-FR', 'de-DE', 'it-IT', 'pt-BR', 'ru-RU', 'zh-C<PERSON>', 'ja-<PERSON>', 'ko-KR', 'nl-NL', 'pl-PL', 'sv-SE', 'tr-TR'],
            defaultLanguage: 'en-US'
        },
        azure: {
            name: 'Azure Speech Service',
            requiresKey: true,
            apiKeyName: 'azureSpeechApiKey',
            regionName: 'azureSpeechRegion',
            endpoint: 'https://{region}.stt.speech.microsoft.com/speech/recognition/conversation/cognitiveservices/v1',
            supportsLanguages: ['en-US', 'en-GB', 'es-ES', 'fr-FR', 'de-DE', 'it-IT', 'pt-BR', 'ru-RU', 'zh-CN', 'ja-JP', 'ko-KR', 'ar-SA', 'cs-CZ', 'da-DK', 'fi-FI', 'hi-IN', 'hu-HU', 'id-ID', 'nb-NO', 'nl-NL', 'pl-PL', 'pt-PT', 'sv-SE', 'th-TH', 'tr-TR', 'zh-HK', 'zh-TW'],
            defaultLanguage: 'en-US'
        },
        assembly: {
            name: 'AssemblyAI',
            requiresKey: true,
            apiKeyName: 'assemblyAiApiKey',
            endpoint: 'https://api.assemblyai.com/v2/transcript',
            supportsLanguages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'nl', 'hi', 'ja', 'zh', 'fi', 'ko', 'pl', 'ru', 'tr', 'uk', 'vi'],
            defaultLanguage: 'en'
        }
    },

    // User preferences
    preferences: {
        language: 'en-US',
        enablePunctuation: true,
        enableAutomaticCapitalization: true,
        enableProfanityFilter: false,
        enableInterimResults: true,
        silenceThreshold: 30000, // 30 seconds
    }
};

// Enhanced Audio Processing Configuration
const AUDIO_CONFIG = {
    // Enhanced audio constraints for better quality
    constraints: {
        audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            sampleRate: 44100,
            sampleSize: 16,
            channelCount: 1,
            volume: 1.0,
            // Advanced constraints for better microphone handling
            googEchoCancellation: true,
            googAutoGainControl: true,
            googNoiseSuppression: true,
            googHighpassFilter: true,
            googTypingNoiseDetection: true,
            googAudioMirroring: false
        }
    },

    // Audio quality thresholds
    quality: {
        minVolume: 0.01,        // Minimum audio level to consider as speech
        maxVolume: 1.0,         // Maximum audio level before clipping
        optimalRange: [0.1, 0.8], // Optimal audio level range
        noiseFloor: 0.005,      // Background noise threshold
        silenceThreshold: 0.02,  // Silence detection threshold
        qualityCheckDuration: 2000, // Duration to check audio quality (ms)
        adaptiveThresholdEnabled: true
    },

    // Audio monitoring settings
    monitoring: {
        enabled: false, // DISABLED - user doesn't want audio level messages
        updateInterval: 100,    // Update frequency for audio level monitoring (ms)
        bufferSize: 2048,       // Audio buffer size for analysis
        smoothingFactor: 0.8,   // Smoothing factor for audio level display
        calibrationDuration: 5000 // Duration for automatic calibration (ms)
    },

    // Adaptive settings
    adaptive: {
        enabled: false, // TEMPORARILY DISABLED to fix calibration error
        learningRate: 0.1,      // How quickly to adapt to new conditions
        minAdaptationSamples: 10, // Minimum samples before adaptation
        maxAdaptationSamples: 100, // Maximum samples to keep for adaptation
        environmentChangeThreshold: 0.3 // Threshold to detect environment changes
    }
};

// State variables
let currentRecognizer = null;
let isEnhancedRecording = false;
let audioRecorder = null;
let audioStream = null;
let audioChunks = [];
let silenceTimeout = null;
let apiKeys = {};

// Audio monitoring state
let audioContext = null;
let audioAnalyser = null;
let audioDataArray = null;
let audioLevelHistory = [];
let currentAudioLevel = 0;
let backgroundNoiseLevel = 0;
let isCalibrating = false;
let audioQualityStatus = 'unknown';
let adaptiveThresholds = {
    silence: AUDIO_CONFIG.quality.silenceThreshold,
    noise: AUDIO_CONFIG.quality.noiseFloor,
    optimal: AUDIO_CONFIG.quality.optimalRange
};

/**
 * Initialize the enhanced voice module
 * Loads saved preferences and API keys
 */
function initEnhancedVoice() {
    console.log("Stickara: Initializing Enhanced Voice Module");

    // Load saved preferences
    chrome.storage.local.get(['voiceApiPreferences'], (result) => {
        if (chrome.runtime.lastError) {
            console.error("Stickara: Error loading voice preferences:", chrome.runtime.lastError);
            return;
        }

        if (result.voiceApiPreferences) {
            console.log("Stickara: Found saved voice preferences:", result.voiceApiPreferences);

            VOICE_API_CONFIG.current = result.voiceApiPreferences.provider || 'browser';
            VOICE_API_CONFIG.preferences = {
                ...VOICE_API_CONFIG.preferences,
                ...result.voiceApiPreferences
            };

            console.log("Stickara: Voice config updated:", VOICE_API_CONFIG);
        } else {
            console.log("Stickara: No saved voice preferences found, using defaults");
        }

        // Load API keys (try encrypted first, then fallback)
        loadApiKeys();
    });
}

/**
 * Loads API keys, trying encrypted storage first
 */
function loadApiKeys() {
    // Check for encrypted keys first
    chrome.storage.local.get(['encryptedVoiceApiKeys', 'voiceApiKeys'], (result) => {
        if (chrome.runtime.lastError) {
            console.error("Stickara: Error loading API keys from storage:", chrome.runtime.lastError);
            apiKeys = {};
            return;
        }

        if (result.encryptedVoiceApiKeys && window.StickaraEncryption && typeof window.StickaraEncryption.decrypt === 'function') {
            console.log("Stickara: Found encrypted API keys, attempting to decrypt");

            // Initialize encryption if not already done
            window.StickaraEncryption.init()
                .then(() => {
                    // Decrypt the API keys
                    return window.StickaraEncryption.decrypt(result.encryptedVoiceApiKeys);
                })
                .then(decryptedKeys => {
                    console.log("Stickara: Successfully decrypted API keys");
                    apiKeys = decryptedKeys || {};
                    console.log(`Stickara: Voice module initialized with provider: ${VOICE_API_CONFIG.current}`);
                })
                .catch(error => {
                    console.error("Stickara: Error decrypting API keys:", error);
                    // Fall back to unencrypted keys
                    fallbackLoadApiKeys(result.voiceApiKeys);
                });
        } else {
            // Fall back to unencrypted keys
            fallbackLoadApiKeys(result.voiceApiKeys);
        }
    });
}

/**
 * Fallback method to load unencrypted API keys
 * @param {Object} keys - The unencrypted API keys
 */
function fallbackLoadApiKeys(keys) {
    if (keys && typeof keys === 'object') {
        console.log("Stickara: Using unencrypted API keys");
        apiKeys = keys;
        console.log("Stickara: Available API keys:", Object.keys(keys));
    } else {
        console.log("Stickara: No API keys found in storage");
        apiKeys = {};
    }

    console.log(`Stickara: Voice module initialized with provider: ${VOICE_API_CONFIG.current}`);

    // Update audio config based on user preferences
    updateAudioConfigFromPreferences();

    // Initialize audio monitoring if supported
    if (AUDIO_CONFIG.monitoring.enabled) {
        initializeAudioMonitoring();
    }
}

/**
 * Update audio configuration based on user preferences
 */
function updateAudioConfigFromPreferences() {
    const prefs = VOICE_API_CONFIG.preferences;

    // Update monitoring settings
    if (prefs.audioMonitoring !== undefined) {
        AUDIO_CONFIG.monitoring.enabled = prefs.audioMonitoring;
    }

    // Update adaptive settings
    if (prefs.adaptiveThresholds !== undefined) {
        AUDIO_CONFIG.adaptive.enabled = prefs.adaptiveThresholds;
    }

    // Update audio constraints based on settings
    if (prefs.enhancedConstraints !== undefined && !prefs.enhancedConstraints) {
        // Use basic constraints if enhanced is disabled
        AUDIO_CONFIG.constraints.audio = {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
        };
    }

    // Update noise suppression level
    if (prefs.noiseSuppression) {
        const level = prefs.noiseSuppression;
        switch (level) {
            case 'low':
                AUDIO_CONFIG.constraints.audio.noiseSuppression = true;
                AUDIO_CONFIG.constraints.audio.googNoiseSuppression = false;
                break;
            case 'medium':
                AUDIO_CONFIG.constraints.audio.noiseSuppression = true;
                AUDIO_CONFIG.constraints.audio.googNoiseSuppression = true;
                break;
            case 'high':
            case 'aggressive':
                AUDIO_CONFIG.constraints.audio.noiseSuppression = true;
                AUDIO_CONFIG.constraints.audio.googNoiseSuppression = true;
                AUDIO_CONFIG.constraints.audio.googHighpassFilter = true;
                break;
        }
    }

    // Update sensitivity-based thresholds
    if (prefs.audioSensitivity !== undefined) {
        const sensitivity = prefs.audioSensitivity / 100; // Convert percentage to decimal

        // Adjust thresholds based on sensitivity
        AUDIO_CONFIG.quality.minVolume = Math.max(0.005, 0.02 * (1 - sensitivity));
        AUDIO_CONFIG.quality.silenceThreshold = Math.max(0.01, 0.05 * (1 - sensitivity));
        AUDIO_CONFIG.quality.noiseFloor = Math.max(0.002, 0.01 * (1 - sensitivity));

        // Adjust optimal range
        const baseOptimal = [0.1, 0.8];
        const sensitivityFactor = 0.5 + (sensitivity * 0.5); // 0.5 to 1.0
        AUDIO_CONFIG.quality.optimalRange = [
            baseOptimal[0] * sensitivityFactor,
            baseOptimal[1] * (1 + (1 - sensitivityFactor) * 0.2)
        ];
    }

    console.log("Stickara: Updated audio config from preferences:", {
        monitoring: AUDIO_CONFIG.monitoring.enabled,
        adaptive: AUDIO_CONFIG.adaptive.enabled,
        sensitivity: prefs.audioSensitivity,
        noiseSuppression: prefs.noiseSuppression
    });
}

/**
 * Initialize audio monitoring and analysis
 */
function initializeAudioMonitoring() {
    try {
        // Create audio context for monitoring
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        console.log("Stickara: Audio context initialized for monitoring");
    } catch (error) {
        console.warn("Stickara: Could not initialize audio context:", error);
        AUDIO_CONFIG.monitoring.enabled = false;
    }
}

/**
 * Setup audio stream monitoring and analysis
 * @param {MediaStream} stream - The audio stream to monitor
 */
function setupAudioMonitoring(stream) {
    if (!AUDIO_CONFIG.monitoring.enabled || !audioContext) {
        return;
    }

    try {
        // Create audio analyser
        audioAnalyser = audioContext.createAnalyser();
        audioAnalyser.fftSize = AUDIO_CONFIG.monitoring.bufferSize;
        audioAnalyser.smoothingTimeConstant = AUDIO_CONFIG.monitoring.smoothingFactor;

        // Create audio data array
        const bufferLength = audioAnalyser.frequencyBinCount;
        audioDataArray = new Uint8Array(bufferLength);

        // Connect stream to analyser
        const source = audioContext.createMediaStreamSource(stream);
        source.connect(audioAnalyser);

        // Start monitoring
        startAudioLevelMonitoring();

        // Start calibration if enabled
        if (AUDIO_CONFIG.adaptive.enabled) {
            // Add a small delay to ensure audio monitoring is fully started
            setTimeout(() => {
                startAudioCalibration();
            }, 100);
        }

        console.log("Stickara: Audio monitoring setup complete");
    } catch (error) {
        console.error("Stickara: Error setting up audio monitoring:", error);
    }
}

/**
 * Start real-time audio level monitoring
 */
function startAudioLevelMonitoring() {
    if (!audioAnalyser || !audioDataArray) {
        return;
    }

    const monitorAudioLevel = () => {
        if (!isEnhancedRecording && !isCalibrating) {
            return; // Stop monitoring when not recording and not calibrating
        }

        // Get audio data
        audioAnalyser.getByteFrequencyData(audioDataArray);

        // Calculate RMS (Root Mean Square) for audio level
        let sum = 0;
        for (let i = 0; i < audioDataArray.length; i++) {
            sum += audioDataArray[i] * audioDataArray[i];
        }
        const rms = Math.sqrt(sum / audioDataArray.length) / 255;

        // Update current audio level with smoothing
        const previousLevel = currentAudioLevel;
        currentAudioLevel = (currentAudioLevel * AUDIO_CONFIG.monitoring.smoothingFactor) +
                           (rms * (1 - AUDIO_CONFIG.monitoring.smoothingFactor));

        // Audio monitoring active

        // Store in history for analysis
        audioLevelHistory.push({
            level: currentAudioLevel,
            timestamp: Date.now()
        });

        // Keep only recent history
        const cutoffTime = Date.now() - 10000; // Keep 10 seconds of history
        audioLevelHistory = audioLevelHistory.filter(entry => entry.timestamp > cutoffTime);

        // Update audio quality status
        updateAudioQualityStatus();

        // Update visual feedback
        updateAudioLevelDisplay();

        // Continue monitoring
        setTimeout(monitorAudioLevel, AUDIO_CONFIG.monitoring.updateInterval);
    };

    monitorAudioLevel();
}

/**
 * Start audio calibration to determine optimal thresholds
 */
function startAudioCalibration() {
    if (isCalibrating) {
        return;
    }

    isCalibrating = true;
    const calibrationStart = Date.now();
    const calibrationSamples = [];
    let sampleCount = 0;
    const maxSamples = Math.ceil(AUDIO_CONFIG.monitoring.calibrationDuration / AUDIO_CONFIG.monitoring.updateInterval);

    showAudioStatus("Calibrating microphone...", "info");

    const calibrate = () => {
        const elapsed = Date.now() - calibrationStart;
        const isTimeUp = elapsed > AUDIO_CONFIG.monitoring.calibrationDuration;
        const hasEnoughSamples = calibrationSamples.length >= AUDIO_CONFIG.adaptive.minAdaptationSamples;

        if (!isEnhancedRecording || isTimeUp || sampleCount >= maxSamples) {
            // Calibration complete
            finishAudioCalibration(calibrationSamples);
            return;
        }

        // Collect calibration sample with better validation
        if (currentAudioLevel > 0 && currentAudioLevel < 1.0) {
            calibrationSamples.push(currentAudioLevel);
        } else if (audioAnalyser && audioDataArray) {
            // Fallback: calculate audio level directly if currentAudioLevel is invalid
            audioAnalyser.getByteFrequencyData(audioDataArray);
            const average = audioDataArray.reduce((sum, value) => sum + value, 0) / audioDataArray.length;
            const normalizedLevel = average / 255;
            if (normalizedLevel > 0 && normalizedLevel < 1.0) {
                calibrationSamples.push(normalizedLevel);
            }
        }

        sampleCount++;

        // Show progress
        if (sampleCount % 10 === 0) {
            const progress = Math.min(100, (sampleCount / maxSamples) * 100);
            showAudioStatus(`Calibrating microphone... ${Math.round(progress)}%`, "info");
        }

        setTimeout(calibrate, AUDIO_CONFIG.monitoring.updateInterval);
    };

    calibrate();
}

/**
 * Finish audio calibration and set adaptive thresholds
 * @param {Array} samples - Collected audio level samples
 */
function finishAudioCalibration(samples) {
    isCalibrating = false;

    if (samples.length < AUDIO_CONFIG.adaptive.minAdaptationSamples) {
        console.warn(`Stickara: Insufficient calibration samples (${samples.length}/${AUDIO_CONFIG.adaptive.minAdaptationSamples}), using default thresholds`);

        // Use default thresholds but still set up the adaptive system
        adaptiveThresholds.silence = AUDIO_CONFIG.quality.silenceThreshold;
        adaptiveThresholds.noise = AUDIO_CONFIG.quality.noiseFloor;
        adaptiveThresholds.optimal = AUDIO_CONFIG.quality.optimalRange;
        backgroundNoiseLevel = AUDIO_CONFIG.quality.noiseFloor;

        showAudioStatus("Using default audio settings", "info");
        return;
    }

    // Calculate statistics
    samples.sort((a, b) => a - b);
    const median = samples[Math.floor(samples.length / 2)];
    const q1 = samples[Math.floor(samples.length * 0.25)];
    const q3 = samples[Math.floor(samples.length * 0.75)];

    // Set adaptive thresholds
    backgroundNoiseLevel = q1;
    adaptiveThresholds.noise = Math.max(backgroundNoiseLevel * 1.5, AUDIO_CONFIG.quality.noiseFloor);
    adaptiveThresholds.silence = Math.max(median * 0.3, AUDIO_CONFIG.quality.silenceThreshold);

    // Adjust optimal range based on observed levels
    const dynamicRange = q3 - q1;
    if (dynamicRange > 0.1) {
        adaptiveThresholds.optimal = [q1 + dynamicRange * 0.2, q3 - dynamicRange * 0.1];
    }

    showAudioStatus("Microphone calibrated successfully", "success");
}

/**
 * Update audio quality status based on current levels
 */
function updateAudioQualityStatus() {
    if (!AUDIO_CONFIG.monitoring.enabled || audioLevelHistory.length === 0) {
        return;
    }

    const recentLevels = audioLevelHistory.slice(-10); // Last 10 samples
    const avgLevel = recentLevels.reduce((sum, entry) => sum + entry.level, 0) / recentLevels.length;

    let newStatus = 'unknown';

    if (avgLevel < adaptiveThresholds.noise) {
        newStatus = 'too_quiet';
    } else if (avgLevel > AUDIO_CONFIG.quality.maxVolume * 0.9) {
        newStatus = 'too_loud';
    } else if (avgLevel >= adaptiveThresholds.optimal[0] && avgLevel <= adaptiveThresholds.optimal[1]) {
        newStatus = 'optimal';
    } else if (avgLevel < adaptiveThresholds.optimal[0]) {
        newStatus = 'quiet';
    } else {
        newStatus = 'loud';
    }

    // Only update if status changed
    if (newStatus !== audioQualityStatus) {
        audioQualityStatus = newStatus;
        updateAudioQualityIndicator(newStatus);
    }
}

/**
 * Update visual audio level display
 */
function updateAudioLevelDisplay() {
    if (!AUDIO_CONFIG.monitoring.enabled) {
        return;
    }

    // Audio level indicator disabled - user doesn't want audio level messages
    return;

    if (audioIndicator) {
        // Update level bar
        const levelBar = audioIndicator.querySelector('.audio-level-bar');
        if (levelBar) {
            const percentage = Math.min(currentAudioLevel * 100, 100);
            levelBar.style.width = `${percentage}%`;

            // Color coding based on level
            if (currentAudioLevel < adaptiveThresholds.noise) {
                levelBar.style.backgroundColor = '#ef4444'; // Red - too quiet
            } else if (currentAudioLevel >= adaptiveThresholds.optimal[0] && currentAudioLevel <= adaptiveThresholds.optimal[1]) {
                levelBar.style.backgroundColor = '#10b981'; // Green - optimal
            } else if (currentAudioLevel > AUDIO_CONFIG.quality.maxVolume * 0.9) {
                levelBar.style.backgroundColor = '#f59e0b'; // Orange - too loud
            } else {
                levelBar.style.backgroundColor = '#6b7280'; // Gray - acceptable
            }
        }

        // Update level text
        const levelText = audioIndicator.querySelector('.audio-level-text');
        if (levelText) {
            levelText.textContent = `${Math.round(currentAudioLevel * 100)}%`;
        }
    }
}

/**
 * Create audio level indicator UI
 */
function createAudioLevelIndicator() {
    // Remove existing indicator if present
    const existing = document.getElementById('stickara-audio-level-indicator');
    if (existing) {
        existing.remove();
    }

    const indicator = document.createElement('div');
    indicator.id = 'stickara-audio-level-indicator';
    indicator.className = 'stickara-audio-indicator';
    indicator.innerHTML = `
        <div class="audio-indicator-header">
            <span class="audio-indicator-title">🎤 Audio Level</span>
            <span class="audio-level-text">0%</span>
        </div>
        <div class="audio-level-container">
            <div class="audio-level-background">
                <div class="audio-level-bar"></div>
            </div>
        </div>
        <div class="audio-quality-status">
            <span class="audio-quality-text">Initializing...</span>
        </div>
    `;

    // Add styles
    const style = document.createElement('style');
    style.textContent = `
        .stickara-audio-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 2147483647;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 12px;
            min-width: 200px;
            backdrop-filter: blur(10px);
        }

        .audio-indicator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .audio-indicator-title {
            font-weight: 600;
            color: #374151;
        }

        .audio-level-text {
            font-weight: 500;
            color: #6b7280;
        }

        .audio-level-container {
            margin-bottom: 8px;
        }

        .audio-level-background {
            width: 100%;
            height: 8px;
            background-color: #f3f4f6;
            border-radius: 4px;
            overflow: hidden;
        }

        .audio-level-bar {
            height: 100%;
            width: 0%;
            background-color: #6b7280;
            transition: width 0.1s ease-out, background-color 0.2s ease;
        }

        .audio-quality-status {
            text-align: center;
        }

        .audio-quality-text {
            font-size: 11px;
            color: #6b7280;
            font-weight: 500;
        }

        .audio-quality-optimal { color: #10b981; }
        .audio-quality-quiet { color: #f59e0b; }
        .audio-quality-loud { color: #f59e0b; }
        .audio-quality-too_quiet { color: #ef4444; }
        .audio-quality-too_loud { color: #ef4444; }
    `;

    document.head.appendChild(style);
    document.body.appendChild(indicator);
}

/**
 * Update audio quality indicator
 * @param {string} status - The audio quality status
 */
function updateAudioQualityIndicator(status) {
    const indicator = document.getElementById('stickara-audio-level-indicator');
    if (!indicator) return;

    const qualityText = indicator.querySelector('.audio-quality-text');
    if (!qualityText) return;

    // Remove existing quality classes
    qualityText.className = 'audio-quality-text';

    let message = '';
    switch (status) {
        case 'optimal':
            message = '✅ Optimal audio quality';
            qualityText.classList.add('audio-quality-optimal');
            break;
        case 'quiet':
            message = '🔉 Speak louder';
            qualityText.classList.add('audio-quality-quiet');
            break;
        case 'loud':
            message = '🔊 Good audio level';
            qualityText.classList.add('audio-quality-loud');
            break;
        case 'too_quiet':
            message = '❌ Too quiet - speak up';
            qualityText.classList.add('audio-quality-too_quiet');
            break;
        case 'too_loud':
            message = '⚠️ Too loud - reduce volume';
            qualityText.classList.add('audio-quality-too_loud');
            break;
        default:
            message = '🎤 Monitoring audio...';
    }

    qualityText.textContent = message;
}

/**
 * Show audio status message
 * @param {string} message - The message to show
 * @param {string} type - The message type (info, success, warning, error)
 */
function showAudioStatus(message, type = 'info') {
    // Audio status messages disabled - user doesn't want them
    // Just log to console for debugging if needed
    console.log(`Stickara Audio: ${message}`);
}

/**
 * Remove audio level indicator
 */
function removeAudioLevelIndicator() {
    const indicator = document.getElementById('stickara-audio-level-indicator');
    if (indicator) {
        indicator.remove();
    }
}

/**
 * Starts recording with the configured speech recognition provider
 */
function startEnhancedRecording() {
    if (isEnhancedRecording) return;

    // Force reload settings before starting recording to ensure we have the latest
    console.log('Stickara: Force reloading voice settings before recording...');

    chrome.storage.local.get(['voiceApiPreferences', 'voiceApiKeys'], (result) => {
        if (chrome.runtime.lastError) {
            console.error("Stickara: Error loading voice preferences:", chrome.runtime.lastError);
            alert("Error loading voice settings. Please try again.");
            return;
        }

        // Update configuration with latest saved settings
        if (result.voiceApiPreferences) {
            console.log("Stickara: Found saved voice preferences:", result.voiceApiPreferences);
            VOICE_API_CONFIG.current = result.voiceApiPreferences.provider || 'browser';
            VOICE_API_CONFIG.preferences = {
                ...VOICE_API_CONFIG.preferences,
                ...result.voiceApiPreferences
            };
        } else {
            console.log("Stickara: No saved voice preferences found, forcing browser provider");
            VOICE_API_CONFIG.current = 'browser';
        }

        // Update API keys
        if (result.voiceApiKeys) {
            apiKeys = result.voiceApiKeys;
        } else {
            apiKeys = {};
        }

        console.log("Stickara: Updated voice config:", VOICE_API_CONFIG);

        // Now proceed with the actual recording start
        startRecordingWithCurrentConfig();
    });
}

/**
 * Starts recording with the current configuration (called after settings are loaded)
 */
function startRecordingWithCurrentConfig() {
    const provider = VOICE_API_CONFIG.current;
    const providerConfig = VOICE_API_CONFIG.providers[provider];

    // Debug logging for troubleshooting
    console.log(`Stickara: Starting enhanced recording with provider: ${provider}`);
    console.log(`Stickara: Provider config:`, providerConfig);
    console.log(`Stickara: Available API keys:`, Object.keys(apiKeys));

    // Check if provider exists
    if (!providerConfig) {
        console.error(`Stickara: Provider '${provider}' not found in configuration`);
        alert(`Invalid voice provider configuration: ${provider}. Please check your settings.`);
        return;
    }

    // Check if provider requires API key
    if (providerConfig.requiresKey) {
        const apiKey = apiKeys[providerConfig.apiKeyName];
        console.log(`Stickara: Provider requires API key '${providerConfig.apiKeyName}': ${!!apiKey}`);
        if (!apiKey) {
            alert(`Please configure an API key for ${providerConfig.name} in the extension settings.`);
            return;
        }
    } else {
        console.log(`Stickara: Provider '${provider}' does not require API key`);
    }

    // Update UI
    updateRecordButtonUI(true);
    isEnhancedRecording = true;
    window.isEnhancedRecording = true; // Update global reference

    // Start the appropriate recognizer
    switch (provider) {
        case 'browser':
            startBrowserRecognition();
            break;
        case 'google':
        case 'azure':
        case 'assembly':
            startStreamingRecognition(provider);
            break;
        default:
            console.error(`Unknown provider: ${provider}`);
            stopEnhancedRecording();
            alert("Unknown speech recognition provider configured.");
            return;
    }

    // Start silence detection timer
    resetSilenceTimer();
}

/**
 * Stops the current recording session
 */
function stopEnhancedRecording() {
    if (!isEnhancedRecording) return;

    clearTimeout(silenceTimeout);
    isEnhancedRecording = false;
    window.isEnhancedRecording = false; // Update global reference

    // Clear any pending insertion timers and insert remaining text immediately
    if (insertionTimer) {
        clearTimeout(insertionTimer);
        insertionTimer = null;
    }
    // Force insert any pending transcript immediately when stopping
    if (typeof insertAccumulatedText === 'function') {
        insertAccumulatedText();
    }

    // Clean up enhanced voice UI
    if (typeof cleanupVoiceUI === 'function') {
        cleanupVoiceUI();
    }

    // Stop the appropriate recognizer
    if (currentRecognizer) {
        if (VOICE_API_CONFIG.current === 'browser') {
            try {
                currentRecognizer.stop();
            } catch (e) {
                console.warn("Error stopping browser recognition:", e);
            }
        } else {
            // For other providers, stop the audio recording
            if (audioRecorder) {
                audioRecorder.stop();
            }

            if (audioStream) {
                audioStream.getTracks().forEach(track => track.stop());
                audioStream = null;
            }
        }

        currentRecognizer = null;
    }

    // Clean up audio monitoring
    if (audioContext && audioContext.state !== 'closed') {
        try {
            audioContext.close();
        } catch (e) {
            console.warn("Error closing audio context:", e);
        }
    }
    audioContext = null;
    audioAnalyser = null;
    audioDataArray = null;
    audioLevelHistory = [];
    currentAudioLevel = 0;
    isCalibrating = false;
    audioQualityStatus = 'unknown';

    // Remove audio level indicator
    removeAudioLevelIndicator();

    // Update UI
    updateRecordButtonUI(false);

    // Update ARIA for accessibility
    if (typeof updateAriaLiveRegion === 'function') {
        updateAriaLiveRegion('Enhanced voice recording stopped.');
    }

    // Save any final transcription
    scheduleSave();
}

/**
 * Starts the browser's built-in SpeechRecognition with enhanced audio processing
 */
function startBrowserRecognition() {
    if (!SpeechRecognition) {
        alert("Your browser doesn't support the Web Speech API. Try using a different provider or browser.");
        stopEnhancedRecording();
        return;
    }

    // First, request microphone access with enhanced constraints for monitoring
    navigator.mediaDevices.getUserMedia(AUDIO_CONFIG.constraints)
        .then(stream => {
            // Setup audio monitoring
            setupAudioMonitoring(stream);

            // Start the actual speech recognition
            startBrowserSpeechRecognition();

            // Keep the stream for monitoring but don't interfere with SpeechRecognition
            // SpeechRecognition will handle its own audio capture
            // We just use this stream for monitoring purposes
        })
        .catch(err => {
            console.error("Stickara: Enhanced microphone access failed:", err);
            handleMicrophoneError(err);
        });
}

/**
 * Start the actual browser speech recognition
 */
function startBrowserSpeechRecognition() {
    try {
        const recognition = new SpeechRecognition();
        currentRecognizer = recognition;

        recognition.continuous = true;
        recognition.interimResults = VOICE_API_CONFIG.preferences.enableInterimResults;
        recognition.lang = VOICE_API_CONFIG.preferences.language;

        // Enhanced configuration for better language support
        recognition.maxAlternatives = 3; // Get multiple alternatives for better accuracy

        // Configure for native script output based on language
        const language = VOICE_API_CONFIG.preferences.language;
        console.log(`Stickara: Configuring browser speech recognition for language: ${language}`);

        // Set additional properties for better non-Latin script support
        if (language.startsWith('hi-') || language.startsWith('bn-') || language.startsWith('gu-') ||
            language.startsWith('kn-') || language.startsWith('ml-') || language.startsWith('mr-') ||
            language.startsWith('ne-') || language.startsWith('or-') || language.startsWith('pa-') ||
            language.startsWith('si-') || language.startsWith('ta-') || language.startsWith('te-') ||
            language.startsWith('ur-')) {
            // Indic languages - ensure proper script output
            console.log(`Stickara: Configuring for Indic language: ${language}`);
        } else if (language.startsWith('zh-') || language.startsWith('ja-') || language.startsWith('ko-')) {
            // CJK languages - ensure proper character output
            console.log(`Stickara: Configuring for CJK language: ${language}`);
        } else if (language.startsWith('ar-') || language.startsWith('fa-') || language.startsWith('he-')) {
            // RTL languages - ensure proper script output
            console.log(`Stickara: Configuring for RTL language: ${language}`);
        } else if (language.startsWith('th-') || language.startsWith('my-') || language.startsWith('km-') || language.startsWith('lo-')) {
            // Southeast Asian languages with complex scripts
            console.log(`Stickara: Configuring for Southeast Asian language: ${language}`);
        }

        let finalTranscriptSegment = '';

        recognition.onstart = () => {
            console.log("Stickara: Browser speech recognition started");
            resetSilenceTimer();

            // Store original note content and cursor position for interim preview
            originalNoteContent = noteText ? noteText.innerHTML : '';
            if (typeof saveCurrentCursorPosition === 'function') {
                voiceInsertionPoint = saveCurrentCursorPosition();

                // Debug logging for cursor position
                if (voiceInsertionPoint) {
                    console.log('Stickara: Enhanced voice saved cursor position:', {
                        offset: voiceInsertionPoint.startOffset,
                        isAtEnd: voiceInsertionPoint.isAtEnd,
                        containerType: voiceInsertionPoint.startContainer.nodeType === Node.TEXT_NODE ? 'text' : 'element'
                    });
                } else {
                    console.log('Stickara: Enhanced voice - no cursor position saved, will append to end');
                }
            }

            // Initialize voice UI enhancements
            if (typeof updateVoiceStatus === 'function') {
                updateVoiceStatus('active');
            }
            if (typeof updateAriaLiveRegion === 'function') {
                updateAriaLiveRegion('Enhanced voice recording started. Speak now.');
            }
        };

        // Create variable to accumulate transcript segments
        let accumulatedTranscript = '';

        // Clear any existing pending transcript and timer from global state
        pendingTranscript = '';
        if (insertionTimer) {
            clearTimeout(insertionTimer);
            insertionTimer = null;
        }

        // Clear any existing interim transcript
        clearInterimTranscript();

        recognition.onresult = (event) => {
            resetSilenceTimer();
            let interimTranscript = '';
            let maxConfidence = 0;
            finalTranscriptSegment = '';

            for (let i = event.resultIndex; i < event.results.length; ++i) {
                const result = event.results[i];
                const transcript = result[0].transcript;
                const confidence = result[0].confidence || 0.8; // Default confidence if not provided

                if (result.isFinal) {
                    // Process voice commands in final transcript
                    const processed = typeof processVoiceCommands === 'function' ?
                        processVoiceCommands(transcript) : { text: transcript, immediateCommands: [], delayedCommands: [], commands: [] };

                    // Execute immediate commands (line breaks) right away
                    if (processed.immediateCommands && processed.immediateCommands.length > 0) {
                        console.log(`Stickara: Enhanced voice executing ${processed.immediateCommands.length} immediate commands`);
                        if (typeof executeVoiceCommands === 'function') {
                            executeVoiceCommands(processed.immediateCommands);
                        }
                    }

                    // Store delayed commands to execute after text insertion
                    if (processed.delayedCommands && processed.delayedCommands.length > 0) {
                        if (typeof pendingVoiceCommands !== 'undefined') {
                            pendingVoiceCommands.push(...processed.delayedCommands);
                            console.log(`Stickara: Enhanced voice stored ${processed.delayedCommands.length} delayed commands for execution after text insertion`);
                        }
                    }

                    // Only add remaining text if there's any after command processing
                    if (processed.text.trim()) {
                        finalTranscriptSegment += processed.text + ' ';
                        // Add to accumulated transcript
                        accumulatedTranscript += processed.text + ' ';
                    }
                    if (typeof updateVoiceStatus === 'function') {
                        updateVoiceStatus('processing');
                    }
                } else {
                    // For interim results, also check for commands but don't execute them yet
                    const processed = typeof processVoiceCommands === 'function' ?
                        processVoiceCommands(transcript) : { text: transcript, commands: [] };

                    if (processed.text.trim()) {
                        interimTranscript += processed.text;
                        maxConfidence = Math.max(maxConfidence, confidence);
                    }

                    // Show command feedback in interim if commands detected
                    const allCommands = [...(processed.immediateCommands || []), ...(processed.delayedCommands || [])];
                    if (allCommands.length > 0) {
                        const commandText = allCommands.map(cmd => `[${cmd.type}]`).join(' ');
                        interimTranscript += ` ${commandText}`;
                    }
                }
            }

            // Accumulate final transcript segments instead of inserting immediately
            if (finalTranscriptSegment.trim()) {
                pendingTranscript += finalTranscriptSegment;

                // Clear any existing timer and set a new one
                clearTimeout(insertionTimer);
                insertionTimer = setTimeout(insertAccumulatedText, INSERTION_DELAY);

                finalTranscriptSegment = '';

                // Update ARIA for final text
                if (typeof updateAriaLiveRegion === 'function') {
                    updateAriaLiveRegion(`Added: ${finalTranscriptSegment.trim()}`);
                }
            }

            // Show interim results directly in the note text area with confidence
            if (interimTranscript.trim()) {
                if (typeof showInterimTranscript === 'function') {
                    showInterimTranscript(interimTranscript, maxConfidence);
                }
            } else {
                if (typeof clearInterimTranscript === 'function') {
                    clearInterimTranscript();
                }
            }
        };

        recognition.onerror = (event) => {
            console.error("Stickara: Speech recognition error:", event.error, event.message);
            handleRecognitionError(event.error, event.message);
        };

        recognition.onend = () => {
            console.log("Stickara: Speech recognition service ended");
            if (isEnhancedRecording) {
                console.log("Stickara: Recognition ended unexpectedly, restarting");
                try {
                    recognition.start();
                } catch (e) {
                    console.error("Failed to restart recognition:", e);
                    stopEnhancedRecording();
                }
            }
        };

        recognition.start();

    } catch (e) {
        console.error("Stickara: Failed to initialize SpeechRecognition:", e);
        alert("Could not start voice recording. An internal error occurred.");
        stopEnhancedRecording();
    }
}

/**
 * Starts recording audio for cloud-based speech recognition services with enhanced audio processing
 * @param {string} provider - The provider to use ('google', 'azure', or 'assembly')
 */
function startStreamingRecognition(provider) {
    // Request microphone access with enhanced constraints
    navigator.mediaDevices.getUserMedia(AUDIO_CONFIG.constraints)
        .then(stream => {
            audioStream = stream;
            audioChunks = [];

            // Setup audio monitoring
            setupAudioMonitoring(stream);

            // Create a MediaRecorder instance with enhanced options
            const options = {
                mimeType: 'audio/webm',
                audioBitsPerSecond: 128000 // Higher quality audio
            };
            audioRecorder = new MediaRecorder(stream, options);
            currentRecognizer = audioRecorder; // Store reference

            // Set up event handlers
            audioRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    audioChunks.push(event.data);
                }

                // For streaming services, we can send chunks as they come
                if (provider === 'google' || provider === 'azure') {
                    // In a real implementation, you would send this chunk to your backend
                    // which would forward it to the API
                    // For now, we'll just simulate this with a message
                    console.log(`Stickara: Would send audio chunk to ${provider} API`);
                }
            };

            audioRecorder.onstop = () => {
                // For non-streaming services like AssemblyAI, send the complete audio file
                if (provider === 'assembly' && audioChunks.length > 0) {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                    sendAudioToService(audioBlob, provider);
                }

                // Clean up
                audioChunks = [];
            };

            // Start recording
            audioRecorder.start(1000); // Collect data in 1-second chunks
            console.log(`Stickara: Started recording for ${provider} transcription`);

        })
        .catch(err => {
            console.error("Stickara: Microphone access failed:", err);
            handleMicrophoneError(err);
        });
}

/**
 * Sends recorded audio to the selected cloud service via the secure proxy
 * @param {Blob} audioBlob - The recorded audio data
 * @param {string} provider - The provider to use
 */
function sendAudioToService(audioBlob, provider) {
    console.log(`Stickara: Sending audio to ${provider} via secure proxy`);

    // Show processing indicator
    updateRecordButtonWithInterim("Processing...");

    // Convert blob to data URL for secure transmission
    const reader = new FileReader();
    reader.onloadend = function() {
        const audioDataUrl = reader.result;

        // Prepare enhanced options based on user preferences and language
        const language = VOICE_API_CONFIG.preferences.language;
        const options = {
            language: language,
            enablePunctuation: VOICE_API_CONFIG.preferences.enablePunctuation,
            enableAutomaticCapitalization: VOICE_API_CONFIG.preferences.enableAutomaticCapitalization,
            enableProfanityFilter: VOICE_API_CONFIG.preferences.enableProfanityFilter,
            sampleRate: 16000, // Default sample rate

            // Enhanced language-specific configuration for native script output
            enableNativeScript: true,
            languageFamily: getLanguageFamily(language),
            scriptType: getScriptType(language),

            // Alternative language codes for better recognition
            alternativeLanguageCodes: getAlternativeLanguageCodes(language),

            // Enhanced configuration for specific language families
            enableWordTimeOffsets: true,
            enableAutomaticPunctuation: VOICE_API_CONFIG.preferences.enablePunctuation,
            useEnhancedModel: true
        };

        console.log(`Stickara: Enhanced audio processing options for ${language}:`, {
            languageFamily: options.languageFamily,
            scriptType: options.scriptType,
            alternativeLanguageCodes: options.alternativeLanguageCodes
        });

        // Send to background script for secure processing
        chrome.runtime.sendMessage({
            action: 'processAudioTranscription',
            provider: provider,
            audioDataUrl: audioDataUrl,
            options: options
        }, (response) => {
            if (chrome.runtime.lastError) {
                console.error("Stickara: Error sending audio to background script:", chrome.runtime.lastError);
                alert(`Error processing audio: ${chrome.runtime.lastError.message}`);
                return;
            }

            if (response && response.success) {
                // Enhanced text processing for native scripts
                let finalText = response.transcript.trim();

                // Apply enhanced text processing for native scripts
                finalText = enhanceTextForNativeScript(finalText, VOICE_API_CONFIG.preferences.language);

                // Validate and clean Unicode text
                finalText = validateAndCleanUnicodeText(finalText);

                console.log(`Stickara: Enhanced transcription result for ${VOICE_API_CONFIG.preferences.language}:`, finalText);

                // Insert the enhanced transcript
                insertTranscript(finalText);
            } else {
                console.error("Stickara: Transcription failed:", response?.error || "Unknown error");
                alert(`Transcription failed: ${response?.error || "Unknown error"}`);
            }
        });
    };

    reader.onerror = function() {
        console.error("Stickara: Error reading audio blob");
        alert("Error processing audio: Could not read audio data");
    };

    // Start reading the blob as data URL
    reader.readAsDataURL(audioBlob);
}

/**
 * Inserts transcription text into the note at the cursor position
 * @param {string} transcript - The transcription text to insert
 */
function insertTranscript(transcript) {
    if (!transcript || !noteText) return;

    // Trim the transcript
    const trimmedTranscript = transcript.trim();
    if (!trimmedTranscript) return;

    // Try to insert at the saved cursor position using the standard voice module functions
    if (typeof insertTextAtCursorPosition === 'function' && voiceInsertionPoint && voiceInsertionPoint.startContainer) {
        insertTextAtCursorPosition(trimmedTranscript);
    } else if (typeof insertTextFallback === 'function') {
        // Use the fallback method from standard voice module
        insertTextFallback(trimmedTranscript);
    } else {
        // Final fallback if functions are not available
        if (noteText.innerHTML.length === 0) {
            noteText.innerHTML = trimmedTranscript;
        } else {
            const currentContent = noteText.innerHTML;
            if (!/[\s\>]$/.test(currentContent)) {
                noteText.innerHTML = currentContent + ' ' + trimmedTranscript;
            } else {
                noteText.innerHTML = currentContent + trimmedTranscript;
            }
        }
    }

    // Execute any pending delayed voice commands AFTER text insertion
    // (Immediate commands like line breaks are already executed)
    if (typeof pendingVoiceCommands !== 'undefined' && pendingVoiceCommands && pendingVoiceCommands.length > 0) {
        // Use adaptive delay for enhanced voice as well
        const adaptiveDelay = typeof calculateOptimalDelay === 'function' ?
            calculateOptimalDelay(trimmedTranscript, pendingVoiceCommands) : 50;

        console.log(`Stickara: Enhanced voice executing ${pendingVoiceCommands.length} delayed commands after ${adaptiveDelay}ms delay`);
        setTimeout(() => {
            if (typeof executeVoiceCommandsWithRetry === 'function') {
                executeVoiceCommandsWithRetry(pendingVoiceCommands, trimmedTranscript);
            } else if (typeof executeVoiceCommands === 'function') {
                executeVoiceCommands(pendingVoiceCommands, trimmedTranscript);
            }
            pendingVoiceCommands = []; // Clear pending commands
        }, adaptiveDelay);
    }

    // Schedule save after updating content
    scheduleSave();
}

/**
 * Resets the silence detection timer
 */
function resetSilenceTimer() {
    clearTimeout(silenceTimeout);
    silenceTimeout = setTimeout(() => {
        console.log("Stickara: Stopping recording due to silence");
        alert("Stopping recording due to silence.");
        stopEnhancedRecording();
    }, VOICE_API_CONFIG.preferences.silenceThreshold);
}

/**
 * Handles errors from the speech recognition service
 * @param {string} errorType - The type of error
 * @param {string} errorMessage - The error message
 */
function handleRecognitionError(errorType, errorMessage) {
    clearTimeout(silenceTimeout);
    let userMessage = "Voice recording error: " + (errorMessage || errorType);

    // Provide more specific user messages based on error type
    if (errorType === 'no-speech') {
        userMessage = "No speech detected for a while. Please try speaking clearly.";
        resetSilenceTimer();
    } else if (errorType === 'audio-capture') {
        userMessage = "Microphone error. Could not capture audio. Please check your microphone connection and browser permissions.";
        stopEnhancedRecording();
    } else if (errorType === 'not-allowed') {
        userMessage = "Microphone access denied. Please allow access in browser settings.";
        stopEnhancedRecording();
    } else if (errorType === 'network') {
        userMessage = "Network error during speech recognition. Please check your internet connection.";
        resetSilenceTimer();
    } else if (errorType === 'aborted') {
        userMessage = "Voice recording aborted.";
    } else {
        stopEnhancedRecording();
    }

    alert(userMessage);
}

/**
 * Handles microphone access errors
 * @param {Error} error - The error object
 */
function handleMicrophoneError(error) {
    let userMessage = "Could not access microphone.";

    if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
        userMessage = "Microphone permission denied. Please allow access in browser settings.";
    } else if (error.name === 'NotFoundError' || error.name === 'DevicesNotFoundError') {
        userMessage = "No microphone found. Please ensure one is connected and enabled.";
    } else {
        userMessage = `Could not access microphone: ${error.message}`;
    }

    alert(userMessage);
    stopEnhancedRecording();
}

/**
 * Updates the record button to show interim transcription results
 * @param {string} interimText - The interim transcription text (no longer used)
 */
function updateRecordButtonWithInterim(interimText) {
    // No longer showing interim results in button - interim results are shown directly in note area
    // Keep function for compatibility but don't update button text with interim results
    const recordButton = document.getElementById(VOICE_RECORD_BTN_ID);
    if (recordButton && isEnhancedRecording) {
        const textSpan = recordButton.querySelector('.Stickara-text');
        if (textSpan) {
            textSpan.textContent = 'Stop'; // Always show "Stop" during recording
        }
    }
}

/**
 * Saves the current voice API configuration
 */
function saveVoiceApiConfig() {
    const configToSave = {
        provider: VOICE_API_CONFIG.current,
        language: VOICE_API_CONFIG.preferences.language,
        enablePunctuation: VOICE_API_CONFIG.preferences.enablePunctuation,
        enableAutomaticCapitalization: VOICE_API_CONFIG.preferences.enableAutomaticCapitalization,
        enableProfanityFilter: VOICE_API_CONFIG.preferences.enableProfanityFilter,
        enableInterimResults: VOICE_API_CONFIG.preferences.enableInterimResults,
        silenceThreshold: VOICE_API_CONFIG.preferences.silenceThreshold,
        // Add metadata for better tracking
        lastUpdated: new Date().toISOString(),
        version: '2.1'
    };

    chrome.storage.local.set({ voiceApiPreferences: configToSave }, () => {
        if (chrome.runtime.lastError) {
            console.error("Stickara: Error saving voice preferences:", chrome.runtime.lastError);
        } else {
            console.log("Stickara: Voice API preferences saved successfully:", configToSave);
        }
    });
}

/**
 * Saves API keys securely using encryption if available
 * @param {Object} keys - The API keys to save
 */
function saveApiKeys(keys) {
    // Check if encryption module is available
    if (window.StickaraEncryption && typeof window.StickaraEncryption.encrypt === 'function') {
        console.log("Stickara: Encrypting API keys before storage");

        // Initialize encryption if not already done
        window.StickaraEncryption.init()
            .then(() => {
                // Encrypt the API keys
                return window.StickaraEncryption.encrypt(keys);
            })
            .then(encryptedKeys => {
                // Store the encrypted keys
                chrome.storage.local.set({ encryptedVoiceApiKeys: encryptedKeys }, () => {
                    console.log("Stickara: Encrypted voice API keys saved");
                    apiKeys = keys; // Keep unencrypted version in memory for current session
                });
            })
            .catch(error => {
                console.error("Stickara: Error encrypting API keys:", error);
                // Fall back to standard storage if encryption fails
                fallbackSaveApiKeys(keys);
            });
    } else {
        // Fall back to standard storage if encryption is not available
        fallbackSaveApiKeys(keys);
    }
}

/**
 * Fallback method to save API keys without encryption
 * @param {Object} keys - The API keys to save
 */
function fallbackSaveApiKeys(keys) {
    console.log("Stickara: Using standard storage for API keys (encryption not available)");
    chrome.storage.local.set({ voiceApiKeys: keys }, () => {
        console.log("Stickara: Voice API keys saved (unencrypted)");
        apiKeys = keys;
    });
}

/**
 * Gets the language family for enhanced speech recognition configuration
 * @param {string} languageCode - The language code (e.g., 'hi-IN')
 * @returns {string} The language family
 */
function getLanguageFamily(languageCode) {
    const langBase = languageCode.split('-')[0];

    // Indic languages
    if (['hi', 'bn', 'gu', 'kn', 'ml', 'mr', 'ne', 'or', 'pa', 'si', 'ta', 'te', 'ur'].includes(langBase)) {
        return 'indic';
    }

    // CJK languages
    if (['zh', 'ja', 'ko'].includes(langBase)) {
        return 'cjk';
    }

    // RTL languages
    if (['ar', 'fa', 'he', 'ur'].includes(langBase)) {
        return 'rtl';
    }

    // Southeast Asian
    if (['th', 'vi', 'my', 'km', 'lo', 'id', 'ms', 'fil', 'tl'].includes(langBase)) {
        return 'southeast_asian';
    }

    // Slavic languages
    if (['ru', 'uk', 'bg', 'hr', 'cs', 'sk', 'sl', 'sr', 'bs', 'mk', 'pl', 'be'].includes(langBase)) {
        return 'slavic';
    }

    // Germanic languages
    if (['en', 'de', 'nl', 'sv', 'da', 'no', 'is'].includes(langBase)) {
        return 'germanic';
    }

    // Romance languages
    if (['es', 'fr', 'it', 'pt', 'ro', 'ca', 'gl'].includes(langBase)) {
        return 'romance';
    }

    return 'other';
}

/**
 * Gets the script type for enhanced speech recognition configuration
 * @param {string} languageCode - The language code (e.g., 'hi-IN')
 * @returns {string} The script type
 */
function getScriptType(languageCode) {
    const langBase = languageCode.split('-')[0];

    // Devanagari script
    if (['hi', 'ne', 'mr'].includes(langBase)) {
        return 'devanagari';
    }

    // Bengali script
    if (['bn'].includes(langBase)) {
        return 'bengali';
    }

    // Gujarati script
    if (['gu'].includes(langBase)) {
        return 'gujarati';
    }

    // Kannada script
    if (['kn'].includes(langBase)) {
        return 'kannada';
    }

    // Malayalam script
    if (['ml'].includes(langBase)) {
        return 'malayalam';
    }

    // Odia script
    if (['or'].includes(langBase)) {
        return 'odia';
    }

    // Gurmukhi script (Punjabi)
    if (['pa'].includes(langBase)) {
        return 'gurmukhi';
    }

    // Sinhala script
    if (['si'].includes(langBase)) {
        return 'sinhala';
    }

    // Tamil script
    if (['ta'].includes(langBase)) {
        return 'tamil';
    }

    // Telugu script
    if (['te'].includes(langBase)) {
        return 'telugu';
    }

    // Arabic script
    if (['ar', 'fa', 'ur'].includes(langBase)) {
        return 'arabic';
    }

    // Hebrew script
    if (['he'].includes(langBase)) {
        return 'hebrew';
    }

    // Chinese characters
    if (['zh'].includes(langBase)) {
        return 'chinese';
    }

    // Japanese scripts
    if (['ja'].includes(langBase)) {
        return 'japanese';
    }

    // Korean script
    if (['ko'].includes(langBase)) {
        return 'korean';
    }

    // Thai script
    if (['th'].includes(langBase)) {
        return 'thai';
    }

    // Myanmar script
    if (['my'].includes(langBase)) {
        return 'myanmar';
    }

    // Khmer script
    if (['km'].includes(langBase)) {
        return 'khmer';
    }

    // Lao script
    if (['lo'].includes(langBase)) {
        return 'lao';
    }

    // Cyrillic script
    if (['ru', 'uk', 'bg', 'sr', 'mk', 'be'].includes(langBase)) {
        return 'cyrillic';
    }

    return 'latin';
}

/**
 * Enhances text for native script output based on language
 * @param {string} text - The transcribed text
 * @param {string} languageCode - The language code
 * @returns {string} Enhanced text
 */
function enhanceTextForNativeScript(text, languageCode) {
    if (!text || !languageCode) return text;

    const langBase = languageCode.split('-')[0];
    const scriptType = getScriptType(languageCode);

    console.log(`Stickara: Enhancing text for ${languageCode} (script: ${scriptType})`);

    // For Indic languages, ensure proper Unicode normalization
    if (['hi', 'bn', 'gu', 'kn', 'ml', 'mr', 'ne', 'or', 'pa', 'si', 'ta', 'te'].includes(langBase)) {
        // Normalize Unicode for Indic scripts
        text = text.normalize('NFC'); // Canonical decomposition followed by canonical composition

        // Remove any Latin transliteration artifacts if present
        text = removeLatinTransliterationArtifacts(text, langBase);

        console.log(`Stickara: Applied Indic script enhancements for ${langBase}`);
    }

    // For Arabic and RTL languages
    else if (['ar', 'fa', 'he', 'ur'].includes(langBase)) {
        // Normalize Unicode for Arabic script
        text = text.normalize('NFC');

        // Ensure proper RTL markers if needed
        text = enhanceRTLText(text);

        console.log(`Stickara: Applied RTL script enhancements for ${langBase}`);
    }

    // For CJK languages
    else if (['zh', 'ja', 'ko'].includes(langBase)) {
        // Normalize Unicode for CJK characters
        text = text.normalize('NFC');

        // Handle specific CJK text processing
        text = enhanceCJKText(text, langBase);

        console.log(`Stickara: Applied CJK script enhancements for ${langBase}`);
    }

    // For Southeast Asian languages with complex scripts
    else if (['th', 'my', 'km', 'lo'].includes(langBase)) {
        // Normalize Unicode for complex scripts
        text = text.normalize('NFC');

        console.log(`Stickara: Applied Southeast Asian script enhancements for ${langBase}`);
    }

    return text;
}

/**
 * Removes Latin transliteration artifacts from Indic text
 * @param {string} text - The text to process
 * @param {string} langBase - The base language code
 * @returns {string} Cleaned text
 */
function removeLatinTransliterationArtifacts(text, langBase) {
    // If text contains mostly Latin characters but should be in native script,
    // it might be transliterated. For now, we'll just log this for debugging.
    const latinCharCount = (text.match(/[a-zA-Z]/g) || []).length;
    const totalCharCount = text.length;
    const latinRatio = latinCharCount / totalCharCount;

    if (latinRatio > 0.8) {
        console.log(`Stickara: Warning - High Latin character ratio (${(latinRatio * 100).toFixed(1)}%) in ${langBase} text. Possible transliteration: "${text}"`);
        // In a future enhancement, we could add transliteration conversion here
    }

    return text;
}

/**
 * Enhances RTL text with proper formatting
 * @param {string} text - The text to enhance
 * @returns {string} Enhanced RTL text
 */
function enhanceRTLText(text) {
    // Add RTL mark if the text starts with RTL characters
    if (/^[\u0590-\u05FF\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/.test(text)) {
        // Add RLM (Right-to-Left Mark) at the beginning if not present
        if (!text.startsWith('\u200F')) {
            text = '\u200F' + text;
        }
    }

    return text;
}

/**
 * Enhances CJK text with proper formatting
 * @param {string} text - The text to enhance
 * @param {string} langBase - The base language code
 * @returns {string} Enhanced CJK text
 */
function enhanceCJKText(text, langBase) {
    // For Japanese, ensure proper spacing between different character types
    if (langBase === 'ja') {
        // Add subtle spacing improvements for Japanese text if needed
        // This is a placeholder for future Japanese-specific enhancements
    }

    // For Chinese, ensure proper punctuation
    else if (langBase === 'zh') {
        // Replace Western punctuation with Chinese equivalents if appropriate
        text = text.replace(/,/g, '，').replace(/\./g, '。').replace(/\?/g, '？').replace(/!/g, '！');
    }

    return text;
}

/**
 * Validates and cleans Unicode text for proper display
 * @param {string} text - The text to validate
 * @returns {string} Cleaned text
 */
function validateAndCleanUnicodeText(text) {
    if (!text) return text;

    // Remove any invalid Unicode characters
    text = text.replace(/[\uFFFD]/g, ''); // Remove replacement characters

    // Remove any control characters except common ones (tab, newline, carriage return)
    text = text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g, '');

    // Ensure proper Unicode normalization
    text = text.normalize('NFC');

    // Remove any zero-width characters that might cause display issues
    text = text.replace(/[\u200B-\u200D\uFEFF]/g, '');

    return text;
}

/**
 * Gets alternative language codes for better recognition accuracy
 * @param {string} languageCode - The primary language code
 * @returns {Array} Array of alternative language codes
 */
function getAlternativeLanguageCodes(languageCode) {
    const langBase = languageCode.split('-')[0];
    const alternatives = [];

    // For Hindi, include related Indic languages
    if (langBase === 'hi') {
        alternatives.push('hi-IN', 'ur-IN', 'ne-NP');
    }

    // For Bengali, include variants
    if (langBase === 'bn') {
        alternatives.push('bn-IN', 'bn-BD');
    }

    // For Chinese, include variants
    if (langBase === 'zh') {
        alternatives.push('zh-CN', 'zh-TW', 'zh-HK');
    }

    // For Arabic, include major variants
    if (langBase === 'ar') {
        alternatives.push('ar-SA', 'ar-EG', 'ar-AE');
    }

    // For Spanish, include major variants
    if (langBase === 'es') {
        alternatives.push('es-ES', 'es-MX', 'es-AR');
    }

    // For English, include major variants
    if (langBase === 'en') {
        alternatives.push('en-US', 'en-GB', 'en-AU');
    }

    return alternatives;
}

/**
 * Gets default languages for a provider
 * @param {string} provider - The provider name
 * @returns {Array} Array of language codes
 */
function getDefaultLanguagesForProvider(provider) {
    const defaultLanguages = {
        browser: [
            // English variants (most comprehensive)
            'en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ', 'en-ZA', 'en-PH', 'en-SG',
            // Spanish variants (comprehensive)
            'es-ES', 'es-MX', 'es-AR', 'es-CO', 'es-CL', 'es-PE', 'es-VE', 'es-BO', 'es-CR', 'es-DO', 'es-EC', 'es-GT', 'es-HN', 'es-NI', 'es-PA', 'es-PR', 'es-PY', 'es-SV', 'es-UY', 'es-US',
            // French variants
            'fr-FR', 'fr-CA', 'fr-BE', 'fr-CH', 'fr-LU', 'fr-MC',
            // German variants
            'de-DE', 'de-AT', 'de-CH', 'de-LU', 'de-LI',
            // Italian variants
            'it-IT', 'it-CH', 'it-SM', 'it-VA',
            // Portuguese variants
            'pt-BR', 'pt-PT', 'pt-AO', 'pt-MZ',
            // Chinese variants
            'zh-CN', 'zh-TW', 'zh-HK', 'zh-SG',
            // Arabic variants
            'ar-SA', 'ar-AE', 'ar-BH', 'ar-DZ', 'ar-EG', 'ar-IQ', 'ar-JO', 'ar-KW', 'ar-LB', 'ar-LY', 'ar-MA', 'ar-OM', 'ar-QA', 'ar-SY', 'ar-TN', 'ar-YE',
            // Major world languages
            'ru-RU', 'ja-JP', 'ko-KR', 'hi-IN', 'th-TH', 'vi-VN', 'tr-TR', 'pl-PL', 'nl-NL', 'sv-SE',
            'da-DK', 'no-NO', 'fi-FI', 'cs-CZ', 'hu-HU', 'ro-RO', 'sk-SK', 'sl-SI', 'hr-HR', 'bg-BG',
            'et-EE', 'lv-LV', 'lt-LT', 'mt-MT', 'el-GR', 'cy-GB', 'ga-IE', 'is-IS', 'mk-MK', 'sq-AL',
            // Additional European languages
            'be-BY', 'bs-BA', 'eu-ES', 'gl-ES', 'ca-ES', 'sr-RS', 'uk-UA', 'mn-MN',
            // Asian languages
            'bn-BD', 'bn-IN', 'gu-IN', 'kn-IN', 'ml-IN', 'mr-IN', 'ne-NP', 'or-IN', 'pa-IN', 'si-LK', 'ta-IN', 'te-IN', 'ur-PK',
            'id-ID', 'ms-MY', 'fil-PH', 'tl-PH', 'jv-ID', 'su-ID', 'my-MM', 'km-KH', 'lo-LA',
            // African languages
            'af-ZA', 'am-ET', 'ha-NG', 'ig-NG', 'rw-RW', 'so-SO', 'sw-KE', 'sw-TZ', 'yo-NG', 'zu-ZA', 'xh-ZA',
            // Middle Eastern languages
            'fa-IR', 'he-IL', 'hy-AM', 'ka-GE', 'az-AZ', 'kk-KZ', 'ky-KG', 'tg-TJ', 'tk-TM', 'uz-UZ'
        ],
        google: [
            // English variants (comprehensive Google support)
            'en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ', 'en-PH', 'en-ZA', 'en-SG', 'en-KE', 'en-GH', 'en-NG', 'en-TZ',
            // Spanish variants (all Google-supported regions)
            'es-ES', 'es-MX', 'es-AR', 'es-BO', 'es-CL', 'es-CO', 'es-CR', 'es-DO', 'es-EC', 'es-SV', 'es-GT', 'es-HN', 'es-NI', 'es-PA', 'es-PY', 'es-PE', 'es-PR', 'es-UY', 'es-VE', 'es-US',
            // French variants
            'fr-FR', 'fr-CA', 'fr-BE', 'fr-CH', 'fr-LU', 'fr-MC', 'fr-SN', 'fr-CI', 'fr-BF', 'fr-ML',
            // German variants
            'de-DE', 'de-AT', 'de-CH', 'de-LU', 'de-LI',
            // Italian variants
            'it-IT', 'it-CH', 'it-SM',
            // Portuguese variants
            'pt-BR', 'pt-PT', 'pt-AO', 'pt-MZ', 'pt-GW', 'pt-CV', 'pt-ST', 'pt-TL',
            // Russian and Slavic languages
            'ru-RU', 'uk-UA', 'be-BY', 'bg-BG', 'hr-HR', 'cs-CZ', 'sk-SK', 'sl-SI', 'sr-RS', 'bs-BA', 'mk-MK', 'pl-PL',
            // Asian languages (comprehensive)
            'ja-JP', 'ko-KR', 'zh-CN', 'zh-TW', 'zh-HK', 'zh-SG',
            'hi-IN', 'bn-IN', 'bn-BD', 'gu-IN', 'kn-IN', 'ml-IN', 'mr-IN', 'ne-NP', 'or-IN', 'pa-IN', 'si-LK', 'ta-IN', 'te-IN', 'ur-PK', 'ur-IN',
            'th-TH', 'vi-VN', 'id-ID', 'ms-MY', 'fil-PH', 'tl-PH', 'jv-ID', 'su-ID', 'my-MM', 'km-KH', 'lo-LA',
            // Arabic variants (comprehensive)
            'ar-SA', 'ar-AE', 'ar-BH', 'ar-DZ', 'ar-EG', 'ar-IQ', 'ar-JO', 'ar-KW', 'ar-LB', 'ar-LY', 'ar-MA', 'ar-OM', 'ar-QA', 'ar-SY', 'ar-TN', 'ar-YE',
            // European languages (comprehensive)
            'tr-TR', 'nl-NL', 'nl-BE', 'sv-SE', 'da-DK', 'no-NO', 'fi-FI', 'hu-HU', 'ro-RO', 'et-EE', 'lv-LV', 'lt-LT', 'el-GR',
            // Additional European languages
            'ca-ES', 'eu-ES', 'gl-ES', 'cy-GB', 'ga-IE', 'is-IS', 'mt-MT', 'sq-AL',
            // Middle Eastern and Caucasian languages
            'he-IL', 'fa-IR', 'hy-AM', 'ka-GE', 'az-AZ',
            // Central Asian languages
            'kk-KZ', 'ky-KG', 'tg-TJ', 'tk-TM', 'uz-UZ', 'mn-MN',
            // African languages
            'af-ZA', 'am-ET', 'ha-NG', 'ig-NG', 'rw-RW', 'so-SO', 'sw-KE', 'sw-TZ', 'yo-NG', 'zu-ZA', 'xh-ZA'
        ],
        azure: [
            // English variants (Azure comprehensive support)
            'en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ', 'en-ZA', 'en-PH', 'en-SG', 'en-KE', 'en-GH', 'en-NG', 'en-TZ',
            // Spanish variants (Azure comprehensive support)
            'es-ES', 'es-MX', 'es-AR', 'es-BO', 'es-CL', 'es-CO', 'es-CR', 'es-DO', 'es-EC', 'es-SV', 'es-GT', 'es-HN', 'es-NI', 'es-PA', 'es-PY', 'es-PE', 'es-PR', 'es-UY', 'es-VE', 'es-US',
            // French variants
            'fr-FR', 'fr-CA', 'fr-BE', 'fr-CH', 'fr-LU', 'fr-MC', 'fr-SN', 'fr-CI', 'fr-BF', 'fr-ML',
            // German variants
            'de-DE', 'de-AT', 'de-CH', 'de-LU', 'de-LI',
            // Italian variants
            'it-IT', 'it-CH', 'it-SM',
            // Portuguese variants
            'pt-BR', 'pt-PT', 'pt-AO', 'pt-MZ', 'pt-GW', 'pt-CV', 'pt-ST', 'pt-TL',
            // Russian and Slavic languages
            'ru-RU', 'uk-UA', 'be-BY', 'bg-BG', 'hr-HR', 'cs-CZ', 'sk-SK', 'sl-SI', 'sr-RS', 'bs-BA', 'mk-MK', 'pl-PL',
            // Asian languages (Azure comprehensive)
            'ja-JP', 'ko-KR', 'zh-CN', 'zh-TW', 'zh-HK', 'zh-SG',
            'hi-IN', 'bn-IN', 'bn-BD', 'gu-IN', 'kn-IN', 'ml-IN', 'mr-IN', 'ne-NP', 'or-IN', 'pa-IN', 'si-LK', 'ta-IN', 'te-IN', 'ur-PK', 'ur-IN',
            'th-TH', 'vi-VN', 'id-ID', 'ms-MY', 'fil-PH', 'tl-PH', 'jv-ID', 'su-ID', 'my-MM', 'km-KH', 'lo-LA',
            // Arabic variants (Azure comprehensive)
            'ar-SA', 'ar-AE', 'ar-BH', 'ar-DZ', 'ar-EG', 'ar-IQ', 'ar-JO', 'ar-KW', 'ar-LB', 'ar-LY', 'ar-MA', 'ar-OM', 'ar-QA', 'ar-SY', 'ar-TN', 'ar-YE',
            // European languages (Azure comprehensive)
            'tr-TR', 'nl-NL', 'nl-BE', 'sv-SE', 'da-DK', 'no-NO', 'fi-FI', 'hu-HU', 'ro-RO', 'et-EE', 'lv-LV', 'lt-LT', 'el-GR',
            // Additional European languages
            'ca-ES', 'eu-ES', 'gl-ES', 'cy-GB', 'ga-IE', 'is-IS', 'mt-MT', 'sq-AL',
            // Middle Eastern and Caucasian languages
            'he-IL', 'fa-IR', 'hy-AM', 'ka-GE', 'az-AZ',
            // Central Asian languages
            'kk-KZ', 'ky-KG', 'tg-TJ', 'tk-TM', 'uz-UZ', 'mn-MN',
            // African languages
            'af-ZA', 'am-ET', 'ha-NG', 'ig-NG', 'rw-RW', 'so-SO', 'sw-KE', 'sw-TZ', 'yo-NG', 'zu-ZA', 'xh-ZA'
        ],
        assembly: [
            // English variants (AssemblyAI focused on quality)
            'en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ', 'en-ZA', 'en-PH', 'en-SG',
            // Spanish variants (major regions)
            'es-ES', 'es-MX', 'es-AR', 'es-CO', 'es-CL', 'es-PE', 'es-VE', 'es-BO', 'es-CR', 'es-DO', 'es-EC', 'es-GT', 'es-HN', 'es-NI', 'es-PA', 'es-PY', 'es-PR', 'es-SV', 'es-UY', 'es-US',
            // French variants
            'fr-FR', 'fr-CA', 'fr-BE', 'fr-CH',
            // German variants
            'de-DE', 'de-AT', 'de-CH',
            // Italian variants
            'it-IT', 'it-CH',
            // Portuguese variants
            'pt-BR', 'pt-PT', 'pt-AO', 'pt-MZ',
            // Russian and major Slavic languages
            'ru-RU', 'uk-UA', 'bg-BG', 'hr-HR', 'cs-CZ', 'sk-SK', 'sl-SI', 'sr-RS', 'bs-BA', 'mk-MK', 'pl-PL',
            // Asian languages (high-quality focus)
            'ja-JP', 'ko-KR', 'zh-CN', 'zh-TW', 'zh-HK',
            'hi-IN', 'bn-IN', 'bn-BD', 'gu-IN', 'kn-IN', 'ml-IN', 'mr-IN', 'ne-NP', 'or-IN', 'pa-IN', 'si-LK', 'ta-IN', 'te-IN', 'ur-PK', 'ur-IN',
            'th-TH', 'vi-VN', 'id-ID', 'ms-MY', 'fil-PH', 'tl-PH', 'my-MM', 'km-KH', 'lo-LA',
            // Arabic variants (major regions)
            'ar-SA', 'ar-AE', 'ar-BH', 'ar-DZ', 'ar-EG', 'ar-IQ', 'ar-JO', 'ar-KW', 'ar-LB', 'ar-LY', 'ar-MA', 'ar-OM', 'ar-QA', 'ar-SY', 'ar-TN', 'ar-YE',
            // European languages (quality-focused)
            'tr-TR', 'nl-NL', 'nl-BE', 'sv-SE', 'da-DK', 'no-NO', 'fi-FI', 'hu-HU', 'ro-RO', 'et-EE', 'lv-LV', 'lt-LT', 'el-GR',
            // Additional European languages
            'ca-ES', 'eu-ES', 'gl-ES', 'cy-GB', 'ga-IE', 'is-IS', 'mt-MT', 'sq-AL',
            // Middle Eastern and Caucasian languages
            'he-IL', 'fa-IR', 'hy-AM', 'ka-GE', 'az-AZ',
            // Central Asian languages
            'kk-KZ', 'ky-KG', 'tg-TJ', 'tk-TM', 'uz-UZ', 'mn-MN',
            // African languages (major languages)
            'af-ZA', 'am-ET', 'ha-NG', 'ig-NG', 'rw-RW', 'so-SO', 'sw-KE', 'sw-TZ', 'yo-NG', 'zu-ZA', 'xh-ZA',
            // Additional Slavic languages
            'be-BY'
        ]
    };

    return defaultLanguages[provider] || defaultLanguages.browser;
}

/**
 * Gets a user-friendly display name for a language code
 * @param {string} langCode - The language code (e.g., 'en-US')
 * @returns {string} Display name (e.g., 'English (United States)')
 */
function getLanguageDisplayName(langCode) {
    const languageNames = {
        'en-US': 'English (United States)',
        'en-GB': 'English (United Kingdom)',
        'en-AU': 'English (Australia)',
        'en-CA': 'English (Canada)',
        'en-IN': 'English (India)',
        'en-IE': 'English (Ireland)',
        'en-NZ': 'English (New Zealand)',
        'en-PH': 'English (Philippines)',
        'en-ZA': 'English (South Africa)',
        'es-ES': 'Spanish (Spain)',
        'es-MX': 'Spanish (Mexico)',
        'es-AR': 'Spanish (Argentina)',
        'es-BO': 'Spanish (Bolivia)',
        'es-CL': 'Spanish (Chile)',
        'es-CO': 'Spanish (Colombia)',
        'es-CR': 'Spanish (Costa Rica)',
        'es-DO': 'Spanish (Dominican Republic)',
        'es-EC': 'Spanish (Ecuador)',
        'es-SV': 'Spanish (El Salvador)',
        'es-GT': 'Spanish (Guatemala)',
        'es-HN': 'Spanish (Honduras)',
        'es-NI': 'Spanish (Nicaragua)',
        'es-PA': 'Spanish (Panama)',
        'es-PY': 'Spanish (Paraguay)',
        'es-PE': 'Spanish (Peru)',
        'es-PR': 'Spanish (Puerto Rico)',
        'es-UY': 'Spanish (Uruguay)',
        'es-VE': 'Spanish (Venezuela)',
        'fr-FR': 'French (France)',
        'fr-CA': 'French (Canada)',
        'fr-BE': 'French (Belgium)',
        'fr-CH': 'French (Switzerland)',
        'de-DE': 'German (Germany)',
        'de-AT': 'German (Austria)',
        'de-CH': 'German (Switzerland)',
        'it-IT': 'Italian (Italy)',
        'pt-BR': 'Portuguese (Brazil)',
        'pt-PT': 'Portuguese (Portugal)',
        'ru-RU': 'Russian (Russia)',
        'ja-JP': 'Japanese (Japan)',
        'ko-KR': 'Korean (South Korea)',
        'zh-CN': 'Chinese (Simplified)',
        'zh-TW': 'Chinese (Traditional)',
        'zh-HK': 'Chinese (Hong Kong)',
        'ar-SA': 'Arabic (Saudi Arabia)',
        'ar-AE': 'Arabic (UAE)',
        'ar-BH': 'Arabic (Bahrain)',
        'ar-DZ': 'Arabic (Algeria)',
        'ar-EG': 'Arabic (Egypt)',
        'ar-IQ': 'Arabic (Iraq)',
        'ar-JO': 'Arabic (Jordan)',
        'ar-KW': 'Arabic (Kuwait)',
        'ar-LB': 'Arabic (Lebanon)',
        'ar-LY': 'Arabic (Libya)',
        'ar-MA': 'Arabic (Morocco)',
        'ar-OM': 'Arabic (Oman)',
        'ar-QA': 'Arabic (Qatar)',
        'ar-SY': 'Arabic (Syria)',
        'ar-TN': 'Arabic (Tunisia)',
        'ar-YE': 'Arabic (Yemen)',
        'hi-IN': 'Hindi (India)',
        'th-TH': 'Thai (Thailand)',
        'vi-VN': 'Vietnamese (Vietnam)',
        'tr-TR': 'Turkish (Turkey)',
        'pl-PL': 'Polish (Poland)',
        'nl-NL': 'Dutch (Netherlands)',
        'nl-BE': 'Dutch (Belgium)',
        'sv-SE': 'Swedish (Sweden)',
        'da-DK': 'Danish (Denmark)',
        'no-NO': 'Norwegian (Norway)',
        'fi-FI': 'Finnish (Finland)',
        'cs-CZ': 'Czech (Czech Republic)',
        'hu-HU': 'Hungarian (Hungary)',
        'ro-RO': 'Romanian (Romania)',
        'sk-SK': 'Slovak (Slovakia)',
        'sl-SI': 'Slovenian (Slovenia)',
        'hr-HR': 'Croatian (Croatia)',
        'bg-BG': 'Bulgarian (Bulgaria)',
        'et-EE': 'Estonian (Estonia)',
        'lv-LV': 'Latvian (Latvia)',
        'lt-LT': 'Lithuanian (Lithuania)',
        'mt-MT': 'Maltese (Malta)',
        'el-GR': 'Greek (Greece)',
        'cy-GB': 'Welsh (United Kingdom)',
        'he-IL': 'Hebrew (Israel)',
        'fa-IR': 'Persian (Iran)',
        'ur-PK': 'Urdu (Pakistan)',
        'ur-IN': 'Urdu (India)',
        'bn-BD': 'Bengali (Bangladesh)',
        'bn-IN': 'Bengali (India)',
        'ta-IN': 'Tamil (India)',
        'ta-SG': 'Tamil (Singapore)',
        'ta-LK': 'Tamil (Sri Lanka)',
        'ta-MY': 'Tamil (Malaysia)',
        'te-IN': 'Telugu (India)',
        'ml-IN': 'Malayalam (India)',
        'kn-IN': 'Kannada (India)',
        'gu-IN': 'Gujarati (India)',
        'mr-IN': 'Marathi (India)',
        'ne-NP': 'Nepali (Nepal)',
        'si-LK': 'Sinhala (Sri Lanka)',
        'my-MM': 'Myanmar (Myanmar)',
        'km-KH': 'Khmer (Cambodia)',
        'lo-LA': 'Lao (Laos)',
        'ka-GE': 'Georgian (Georgia)',
        'am-ET': 'Amharic (Ethiopia)',
        'sw-TZ': 'Swahili (Tanzania)',
        'sw-KE': 'Swahili (Kenya)',
        'af-ZA': 'Afrikaans (South Africa)',
        'zu-ZA': 'Zulu (South Africa)',
        'is-IS': 'Icelandic (Iceland)',
        'eu-ES': 'Basque (Spain)',
        'ca-ES': 'Catalan (Spain)',
        'gl-ES': 'Galician (Spain)',
        'sq-AL': 'Albanian (Albania)',
        'az-AZ': 'Azerbaijani (Azerbaijan)',
        'be-BY': 'Belarusian (Belarus)',
        'bs-BA': 'Bosnian (Bosnia and Herzegovina)',
        'mk-MK': 'Macedonian (North Macedonia)',
        'mn-MN': 'Mongolian (Mongolia)',
        'sr-RS': 'Serbian (Serbia)',
        'uk-UA': 'Ukrainian (Ukraine)',
        'uz-UZ': 'Uzbek (Uzbekistan)',
        'hy-AM': 'Armenian (Armenia)',
        'id-ID': 'Indonesian (Indonesia)',
        'ms-MY': 'Malay (Malaysia)',
        'fil-PH': 'Filipino (Philippines)',
        'jv-ID': 'Javanese (Indonesia)',
        'su-ID': 'Sundanese (Indonesia)',
        // Additional language display names for comprehensive coverage
        'en-SG': 'English (Singapore)',
        'en-KE': 'English (Kenya)',
        'en-GH': 'English (Ghana)',
        'en-NG': 'English (Nigeria)',
        'en-TZ': 'English (Tanzania)',
        'es-US': 'Spanish (United States)',
        'fr-LU': 'French (Luxembourg)',
        'fr-MC': 'French (Monaco)',
        'fr-SN': 'French (Senegal)',
        'fr-CI': 'French (Côte d\'Ivoire)',
        'fr-BF': 'French (Burkina Faso)',
        'fr-ML': 'French (Mali)',
        'de-LU': 'German (Luxembourg)',
        'de-LI': 'German (Liechtenstein)',
        'it-SM': 'Italian (San Marino)',
        'it-VA': 'Italian (Vatican City)',
        'pt-AO': 'Portuguese (Angola)',
        'pt-MZ': 'Portuguese (Mozambique)',
        'pt-GW': 'Portuguese (Guinea-Bissau)',
        'pt-CV': 'Portuguese (Cape Verde)',
        'pt-ST': 'Portuguese (São Tomé and Príncipe)',
        'pt-TL': 'Portuguese (East Timor)',
        'zh-SG': 'Chinese (Singapore)',
        'ar-LY': 'Arabic (Libya)',
        'ar-SY': 'Arabic (Syria)',
        'ar-YE': 'Arabic (Yemen)',
        'ga-IE': 'Irish (Ireland)',
        'or-IN': 'Odia (India)',
        'tl-PH': 'Tagalog (Philippines)',
        'my-MM': 'Myanmar (Myanmar)',
        'km-KH': 'Khmer (Cambodia)',
        'lo-LA': 'Lao (Laos)',
        'ha-NG': 'Hausa (Nigeria)',
        'ig-NG': 'Igbo (Nigeria)',
        'rw-RW': 'Kinyarwanda (Rwanda)',
        'so-SO': 'Somali (Somalia)',
        'yo-NG': 'Yoruba (Nigeria)',
        'xh-ZA': 'Xhosa (South Africa)',
        'kk-KZ': 'Kazakh (Kazakhstan)',
        'ky-KG': 'Kyrgyz (Kyrgyzstan)',
        'tg-TJ': 'Tajik (Tajikistan)',
        'tk-TM': 'Turkmen (Turkmenistan)'
    };

    return languageNames[langCode] || langCode;
}

/**
 * Creates the voice settings UI in the popup
 * @param {HTMLElement} container - The container element to append the settings to
 */
function createVoiceSettingsUI(container) {
    if (!container) return;

    const settingsDiv = document.createElement('div');
    settingsDiv.className = 'voice-settings-container';

    // Provider selection
    const providerLabel = document.createElement('label');
    providerLabel.textContent = 'Speech Recognition Provider:';
    const providerSelect = document.createElement('select');
    providerSelect.id = 'voice-provider-select';

    Object.keys(VOICE_API_CONFIG.providers).forEach(key => {
        const option = document.createElement('option');
        option.value = key;
        option.textContent = VOICE_API_CONFIG.providers[key].name;
        if (key === VOICE_API_CONFIG.current) {
            option.selected = true;
        }
        providerSelect.appendChild(option);
    });

    // Language selection
    const langLabel = document.createElement('label');
    langLabel.textContent = 'Language:';
    const langSelect = document.createElement('select');
    langSelect.id = 'voice-language-select';

    // Populate with languages from current provider
    const currentProvider = VOICE_API_CONFIG.providers[VOICE_API_CONFIG.current];
    const supportedLanguages = currentProvider.supportsLanguages || getDefaultLanguagesForProvider(VOICE_API_CONFIG.current);

    if (supportedLanguages && supportedLanguages.length > 0) {
        supportedLanguages.forEach(lang => {
            const option = document.createElement('option');
            option.value = lang;
            option.textContent = getLanguageDisplayName(lang);
            if (lang === VOICE_API_CONFIG.preferences.language) {
                option.selected = true;
            }
            langSelect.appendChild(option);
        });
    } else {
        // Fallback: add a default option
        const option = document.createElement('option');
        option.value = 'en-US';
        option.textContent = 'English (United States)';
        option.selected = true;
        langSelect.appendChild(option);
        console.warn('Stickara: No languages found for provider, using default');
    }

    // API Key input (shown only for providers that need it)
    const apiKeyDiv = document.createElement('div');
    apiKeyDiv.id = 'api-key-container';
    apiKeyDiv.style.display = currentProvider.requiresKey ? 'block' : 'none';

    const apiKeyLabel = document.createElement('label');
    apiKeyLabel.textContent = `${currentProvider.name} API Key:`;
    const apiKeyInput = document.createElement('input');
    apiKeyInput.type = 'password';
    apiKeyInput.id = 'voice-api-key';
    apiKeyInput.placeholder = 'Enter your API key';
    if (currentProvider.requiresKey && apiKeys[currentProvider.apiKeyName]) {
        apiKeyInput.value = apiKeys[currentProvider.apiKeyName];
    }

    // Options checkboxes
    const optionsDiv = document.createElement('div');
    optionsDiv.className = 'voice-options';

    const createCheckbox = (id, label, checked) => {
        const div = document.createElement('div');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = id;
        checkbox.checked = checked;

        const labelEl = document.createElement('label');
        labelEl.htmlFor = id;
        labelEl.textContent = label;

        div.appendChild(checkbox);
        div.appendChild(labelEl);
        return div;
    };

    optionsDiv.appendChild(createCheckbox(
        'voice-punctuation',
        'Enable automatic punctuation',
        VOICE_API_CONFIG.preferences.enablePunctuation
    ));

    optionsDiv.appendChild(createCheckbox(
        'voice-capitalization',
        'Enable automatic capitalization',
        VOICE_API_CONFIG.preferences.enableAutomaticCapitalization
    ));

    optionsDiv.appendChild(createCheckbox(
        'voice-profanity-filter',
        'Filter profanity',
        VOICE_API_CONFIG.preferences.enableProfanityFilter
    ));

    optionsDiv.appendChild(createCheckbox(
        'voice-interim-results',
        'Show interim results',
        VOICE_API_CONFIG.preferences.enableInterimResults
    ));

    // Silence threshold
    const silenceLabel = document.createElement('label');
    silenceLabel.textContent = 'Stop after silence (seconds):';
    const silenceInput = document.createElement('input');
    silenceInput.type = 'number';
    silenceInput.id = 'voice-silence-threshold';
    silenceInput.min = '5';
    silenceInput.max = '60';
    silenceInput.value = Math.floor(VOICE_API_CONFIG.preferences.silenceThreshold / 1000);

    // Voice modifier key settings
    const modifierSection = document.createElement('div');
    modifierSection.style.marginTop = '15px';
    modifierSection.style.padding = '10px';
    modifierSection.style.border = '1px solid #e5e7eb';
    modifierSection.style.borderRadius = '6px';
    modifierSection.style.backgroundColor = '#f9fafb';

    const modifierTitle = document.createElement('h4');
    modifierTitle.textContent = 'Symbol Commands';
    modifierTitle.style.margin = '0 0 10px 0';
    modifierTitle.style.fontSize = '14px';
    modifierTitle.style.fontWeight = '600';

    // Get current modifier key configuration
    const modifierConfig = typeof getVoiceModifierKeyConfig === 'function' ?
        getVoiceModifierKeyConfig() : { enabled: true, modifierKey: 'ctrlKey', visualFeedbackEnabled: true };

    modifierSection.appendChild(modifierTitle);
    modifierSection.appendChild(createCheckbox(
        'voice-modifier-enabled',
        'Enable modifier key toggle for symbol commands',
        modifierConfig.enabled
    ));

    // Modifier key selection
    const modifierKeyDiv = document.createElement('div');
    modifierKeyDiv.style.marginLeft = '20px';
    modifierKeyDiv.style.marginTop = '8px';

    const modifierKeyLabel = document.createElement('label');
    modifierKeyLabel.textContent = 'Modifier Key:';
    modifierKeyLabel.style.display = 'block';
    modifierKeyLabel.style.fontSize = '12px';
    modifierKeyLabel.style.color = '#6b7280';
    modifierKeyLabel.style.marginBottom = '4px';

    const modifierKeySelect = document.createElement('select');
    modifierKeySelect.id = 'voice-modifier-key';
    modifierKeySelect.style.width = '100%';
    modifierKeySelect.style.padding = '4px';
    modifierKeySelect.style.border = '1px solid #d1d5db';
    modifierKeySelect.style.borderRadius = '4px';
    modifierKeySelect.style.fontSize = '12px';

    const modifierOptions = [
        { value: 'ctrlKey', label: 'Ctrl' },
        { value: 'altKey', label: 'Alt' },
        { value: 'shiftKey', label: 'Shift' }
    ];

    modifierOptions.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option.value;
        optionElement.textContent = option.label;
        if (option.value === modifierConfig.modifierKey) {
            optionElement.selected = true;
        }
        modifierKeySelect.appendChild(optionElement);
    });

    modifierKeyDiv.appendChild(modifierKeyLabel);
    modifierKeyDiv.appendChild(modifierKeySelect);
    modifierSection.appendChild(modifierKeyDiv);

    modifierSection.appendChild(createCheckbox(
        'voice-modifier-visual-feedback',
        'Show visual feedback when modifier key is active',
        modifierConfig.visualFeedbackEnabled
    ));

    // Help text
    const helpText = document.createElement('div');
    helpText.style.fontSize = '11px';
    helpText.style.color = '#6b7280';
    helpText.style.marginTop = '8px';
    helpText.style.padding = '6px';
    helpText.style.backgroundColor = '#ffffff';
    helpText.style.borderRadius = '4px';
    helpText.style.border = '1px solid #e5e7eb';
    helpText.textContent = 'Hold the modifier key during voice typing to disable symbol commands and treat them as literal text. For example, saying "question mark" while holding Ctrl will insert "question mark" instead of "?".';

    modifierSection.appendChild(helpText);

    // Save button
    const saveButton = document.createElement('button');
    saveButton.textContent = 'Save Voice Settings';
    saveButton.className = 'Stickara-button';
    saveButton.onclick = () => {
        try {
            // Disable save button to prevent double-clicks
            saveButton.disabled = true;
            saveButton.textContent = 'Saving...';

            // Update config from UI
            VOICE_API_CONFIG.current = providerSelect.value;
            VOICE_API_CONFIG.preferences.language = langSelect.value;
            VOICE_API_CONFIG.preferences.enablePunctuation = document.getElementById('voice-punctuation').checked;
            VOICE_API_CONFIG.preferences.enableAutomaticCapitalization = document.getElementById('voice-capitalization').checked;
            VOICE_API_CONFIG.preferences.enableProfanityFilter = document.getElementById('voice-profanity-filter').checked;
            VOICE_API_CONFIG.preferences.enableInterimResults = document.getElementById('voice-interim-results').checked;
            VOICE_API_CONFIG.preferences.silenceThreshold = parseInt(document.getElementById('voice-silence-threshold').value) * 1000;

            console.log("Stickara: Saving voice settings:", VOICE_API_CONFIG);

            // Save API key if needed
            const currentProvider = VOICE_API_CONFIG.providers[VOICE_API_CONFIG.current];
            if (currentProvider.requiresKey) {
                const apiKey = document.getElementById('voice-api-key').value.trim();
                if (apiKey) {
                    const newKeys = {...apiKeys};
                    newKeys[currentProvider.apiKeyName] = apiKey;
                    saveApiKeys(newKeys);
                    console.log("Stickara: API key saved for provider:", currentProvider.name);
                } else {
                    console.warn("Stickara: No API key provided for provider that requires one:", currentProvider.name);
                }
            }

            // Save modifier key settings
            if (typeof updateVoiceModifierKeyConfig === 'function') {
                const modifierConfig = {
                    enabled: document.getElementById('voice-modifier-enabled').checked,
                    modifierKey: document.getElementById('voice-modifier-key').value,
                    visualFeedbackEnabled: document.getElementById('voice-modifier-visual-feedback').checked
                };
                updateVoiceModifierKeyConfig(modifierConfig);
                console.log("Stickara: Modifier key settings saved:", modifierConfig);
            }

            // Save preferences
            saveVoiceApiConfig();

            // Show success message
            setTimeout(() => {
                saveButton.disabled = false;
                saveButton.textContent = 'Save Voice Settings';

                // Create a temporary success message
                const successMsg = document.createElement('div');
                successMsg.textContent = '✓ Settings saved successfully!';
                successMsg.style.cssText = `
                    color: #059669;
                    font-weight: bold;
                    margin-top: 8px;
                    padding: 8px;
                    background: #d1fae5;
                    border: 1px solid #a7f3d0;
                    border-radius: 4px;
                    text-align: center;
                `;

                saveButton.parentNode.insertBefore(successMsg, saveButton.nextSibling);

                // Remove success message after 3 seconds
                setTimeout(() => {
                    if (successMsg.parentNode) {
                        successMsg.parentNode.removeChild(successMsg);
                    }
                }, 3000);

            }, 500);

        } catch (error) {
            console.error("Stickara: Error saving voice settings:", error);

            // Re-enable button and show error
            saveButton.disabled = false;
            saveButton.textContent = 'Save Voice Settings';

            // Create error message
            const errorMsg = document.createElement('div');
            errorMsg.textContent = '✗ Error saving settings. Please try again.';
            errorMsg.style.cssText = `
                color: #dc2626;
                font-weight: bold;
                margin-top: 8px;
                padding: 8px;
                background: #fee2e2;
                border: 1px solid #fecaca;
                border-radius: 4px;
                text-align: center;
            `;

            saveButton.parentNode.insertBefore(errorMsg, saveButton.nextSibling);

            // Remove error message after 5 seconds
            setTimeout(() => {
                if (errorMsg.parentNode) {
                    errorMsg.parentNode.removeChild(errorMsg);
                }
            }, 5000);
        }
    };

    // Event handler for provider change
    providerSelect.onchange = () => {
        const selectedProvider = VOICE_API_CONFIG.providers[providerSelect.value];
        const newProviderKey = providerSelect.value;

        // Update language options using the new helper functions
        langSelect.innerHTML = '';
        const supportedLanguages = selectedProvider.supportsLanguages || getDefaultLanguagesForProvider(newProviderKey);

        if (supportedLanguages && supportedLanguages.length > 0) {
            supportedLanguages.forEach(lang => {
                const option = document.createElement('option');
                option.value = lang;
                option.textContent = getLanguageDisplayName(lang);
                langSelect.appendChild(option);
            });
            langSelect.value = selectedProvider.defaultLanguage || supportedLanguages[0];
        } else {
            // Fallback option
            const option = document.createElement('option');
            option.value = 'en-US';
            option.textContent = 'English (United States)';
            langSelect.appendChild(option);
            langSelect.value = 'en-US';
        }

        // Show/hide API key input
        apiKeyDiv.style.display = selectedProvider.requiresKey ? 'block' : 'none';
        apiKeyLabel.textContent = `${selectedProvider.name} API Key:`;

        // Update API key input if we have a saved key
        if (selectedProvider.requiresKey && apiKeys[selectedProvider.apiKeyName]) {
            apiKeyInput.value = apiKeys[selectedProvider.apiKeyName];
        } else {
            apiKeyInput.value = '';
        }

        console.log(`Stickara: Provider changed to ${selectedProvider.name}, loaded ${supportedLanguages.length} languages`);
    };

    // Load saved settings and populate UI
    loadVoiceSettingsIntoUI();

    // Function to load saved settings into the UI
    function loadVoiceSettingsIntoUI() {
        chrome.storage.local.get(['voiceApiPreferences', 'voiceApiKeys'], (result) => {
            if (chrome.runtime.lastError) {
                console.error("Stickara: Error loading voice settings:", chrome.runtime.lastError);
                return;
            }

            const prefs = result.voiceApiPreferences || {};
            const keys = result.voiceApiKeys || {};

            console.log("Stickara: Loading voice settings:", prefs);

            // Set provider
            if (prefs.provider && VOICE_API_CONFIG.providers[prefs.provider]) {
                providerSelect.value = prefs.provider;
                VOICE_API_CONFIG.current = prefs.provider;

                // Trigger provider change to update languages and API key fields
                const changeEvent = new Event('change');
                providerSelect.dispatchEvent(changeEvent);
            }

            // Set language (after provider change has updated the options)
            setTimeout(() => {
                if (prefs.language && langSelect.querySelector(`option[value="${prefs.language}"]`)) {
                    langSelect.value = prefs.language;
                    VOICE_API_CONFIG.preferences.language = prefs.language;
                }
            }, 100);

            // Set checkboxes
            if (typeof prefs.enablePunctuation !== 'undefined') {
                const punctuationCheckbox = document.getElementById('voice-punctuation');
                if (punctuationCheckbox) {
                    punctuationCheckbox.checked = prefs.enablePunctuation;
                    VOICE_API_CONFIG.preferences.enablePunctuation = prefs.enablePunctuation;
                }
            }

            if (typeof prefs.enableAutomaticCapitalization !== 'undefined') {
                const capitalizationCheckbox = document.getElementById('voice-capitalization');
                if (capitalizationCheckbox) {
                    capitalizationCheckbox.checked = prefs.enableAutomaticCapitalization;
                    VOICE_API_CONFIG.preferences.enableAutomaticCapitalization = prefs.enableAutomaticCapitalization;
                }
            }

            if (typeof prefs.enableProfanityFilter !== 'undefined') {
                const profanityCheckbox = document.getElementById('voice-profanity-filter');
                if (profanityCheckbox) {
                    profanityCheckbox.checked = prefs.enableProfanityFilter;
                    VOICE_API_CONFIG.preferences.enableProfanityFilter = prefs.enableProfanityFilter;
                }
            }

            if (typeof prefs.enableInterimResults !== 'undefined') {
                const interimCheckbox = document.getElementById('voice-interim-results');
                if (interimCheckbox) {
                    interimCheckbox.checked = prefs.enableInterimResults;
                    VOICE_API_CONFIG.preferences.enableInterimResults = prefs.enableInterimResults;
                }
            }

            // Set silence threshold
            if (prefs.silenceThreshold) {
                silenceInput.value = Math.floor(prefs.silenceThreshold / 1000);
                VOICE_API_CONFIG.preferences.silenceThreshold = prefs.silenceThreshold;
            }

            // Set API key for current provider
            const currentProvider = VOICE_API_CONFIG.providers[VOICE_API_CONFIG.current];
            if (currentProvider && currentProvider.requiresKey && keys[currentProvider.apiKeyName]) {
                apiKeyInput.value = keys[currentProvider.apiKeyName];
            }

            console.log("Stickara: Voice settings loaded successfully");
        });
    }

    // Assemble the UI
    apiKeyDiv.appendChild(apiKeyLabel);
    apiKeyDiv.appendChild(apiKeyInput);

    settingsDiv.appendChild(providerLabel);
    settingsDiv.appendChild(providerSelect);
    settingsDiv.appendChild(langLabel);
    settingsDiv.appendChild(langSelect);
    settingsDiv.appendChild(apiKeyDiv);
    settingsDiv.appendChild(optionsDiv);
    settingsDiv.appendChild(silenceLabel);
    settingsDiv.appendChild(silenceInput);
    settingsDiv.appendChild(modifierSection);
    settingsDiv.appendChild(saveButton);

    container.appendChild(settingsDiv);
}



// Export functions and variables for use in other modules
window.startEnhancedRecording = startEnhancedRecording;
window.stopEnhancedRecording = stopEnhancedRecording;
window.initEnhancedVoice = initEnhancedVoice;
window.createVoiceSettingsUI = createVoiceSettingsUI;

window.getDefaultLanguagesForProvider = getDefaultLanguagesForProvider;
window.getLanguageDisplayName = getLanguageDisplayName;
window.isEnhancedRecording = isEnhancedRecording;

// Emergency fix function for browser provider issues
window.fixBrowserProvider = function() {
    console.log('🔧 Emergency fix: Forcing Browser Speech API provider...');

    const fixedSettings = {
        provider: 'browser',
        language: 'hi-IN',
        enablePunctuation: true,
        enableAutomaticCapitalization: true,
        enableProfanityFilter: false,
        enableInterimResults: true,
        silenceThreshold: 15000,
        lastUpdated: new Date().toISOString(),
        version: '2.1'
    };

    // Save to storage
    chrome.storage.local.set({
        voiceApiPreferences: fixedSettings
    }, () => {
        if (chrome.runtime.lastError) {
            console.error('❌ Error saving fixed settings:', chrome.runtime.lastError);
        } else {
            console.log('✅ Fixed settings saved successfully');

            // Update in-memory configuration
            VOICE_API_CONFIG.current = 'browser';
            VOICE_API_CONFIG.preferences = {
                ...VOICE_API_CONFIG.preferences,
                ...fixedSettings
            };

            console.log('✅ In-memory configuration updated');
            console.log('🎯 Current provider:', VOICE_API_CONFIG.current);
            console.log('🎯 Current language:', VOICE_API_CONFIG.preferences.language);
            console.log('📝 Please try voice recording now - it should work with Browser Speech API');
        }
    });
};













console.log("%c Stickara: Enhanced Voice Module Loaded", "background: #34D399; color: white; padding: 4px; border-radius: 4px; font-weight: bold;");