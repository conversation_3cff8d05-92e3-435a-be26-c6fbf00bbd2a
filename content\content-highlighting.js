// --- START OF FILE content-highlighting.js ---

// Constants HIGHLIGHT_KEY_PREFIX, HIGHLIGHT_CLASS, HIGHLIGHT_COLORS, DEFAULT_HIGHLIGHT_COLOR
// are defined in content-state.js
const HIGHLIGHT_CONTEXT_LENGTH = 30; // Characters of context

/**
 * Gets text content from a range, handling potential errors.
 * @param {Range} range
 * @returns {string}
 */
function getTextFromRange(range) {
    try {
        return range.toString();
    } catch (e) {
        console.warn("Stickara: Error getting text from range", range, e);
        return "";
    }
}

/**
 * Serializes a Range object using text context.
 * Enhanced to handle cross-paragraph highlights with better context extraction.
 * @param {Range} range
 * @param {boolean} isCrossParagraph - Whether this is a cross-paragraph highlight
 * @returns {object|null} Object like { prefix, text, suffix } or null on error.
 */
function serializeRangeWithContext(range, isCrossParagraph = false) {
    if (!range || range.collapsed) return null;
    try {
        const text = getTextFromRange(range);
        if (!text.trim()) {
            // console.warn("Stickara: Skipping serialization of empty/whitespace range.");
            return null;
        }

        // For cross-paragraph highlights, use enhanced context extraction
        if (isCrossParagraph) {
            return serializeCrossParagraphContext(range, text);
        }

        // Original single-paragraph logic
        const prefixRange = document.createRange();
        prefixRange.setStart(document.body, 0);
        prefixRange.setEnd(range.startContainer, range.startOffset);
        const prefix = getTextFromRange(prefixRange).replace(/\s+/g, ' ').slice(-HIGHLIGHT_CONTEXT_LENGTH);

        const suffixRange = document.createRange();
        suffixRange.setStart(range.endContainer, range.endOffset);
        suffixRange.setEnd(document.body, document.body.childNodes.length);
        const suffix = getTextFromRange(suffixRange).replace(/\s+/g, ' ').slice(0, HIGHLIGHT_CONTEXT_LENGTH);

        const serialized = {
            prefix,
            text,
            suffix,
            isCrossParagraph: false
        };

        return serialized;
    } catch (e) {
        console.error("Stickara: Error serializing range with context", range, e);
        return null;
    }
}

/**
 * Enhanced serialization for cross-paragraph highlights
 * @param {Range} range - The range to serialize
 * @param {string} text - The text content of the range
 * @returns {Object} - Enhanced serialization data
 */
function serializeCrossParagraphContext(range, text) {
    try {
        // Use shorter context for cross-paragraph to avoid confusion
        const CROSS_PARA_CONTEXT_LENGTH = Math.min(30, HIGHLIGHT_CONTEXT_LENGTH);

        // Get text nodes in the range for better analysis
        const textNodes = getTextNodesInRange(range);

        if (textNodes.length === 0) {
            // Fallback to original method
            return serializeOriginalContext(range, text);
        }

        // Extract context from first and last nodes for better accuracy
        const firstNode = textNodes[0];
        const lastNode = textNodes[textNodes.length - 1];

        // Get prefix from before the first node
        let prefix = "";
        try {
            // Get text before selection start in the same node
            if (firstNode.startOffset > 0) {
                prefix = firstNode.node.textContent.substring(
                    Math.max(0, firstNode.startOffset - CROSS_PARA_CONTEXT_LENGTH),
                    firstNode.startOffset
                );
            }

            // If we need more context, look at previous nodes
            if (prefix.length < CROSS_PARA_CONTEXT_LENGTH) {
                const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT);
                walker.currentNode = firstNode.node;

                let prevNode;
                while ((prevNode = walker.previousNode()) && prefix.length < CROSS_PARA_CONTEXT_LENGTH) {
                    const nodeText = prevNode.textContent.trim();
                    if (nodeText) {
                        const needed = CROSS_PARA_CONTEXT_LENGTH - prefix.length;
                        prefix = nodeText.substring(Math.max(0, nodeText.length - needed)) + prefix;
                    }
                }
            }
        } catch (e) {
            console.warn("Stickara: Error getting cross-paragraph prefix:", e);
        }

        // Get suffix from after the last node
        let suffix = "";
        try {
            // Get text after selection end in the same node
            if (lastNode.endOffset < lastNode.node.textContent.length) {
                suffix = lastNode.node.textContent.substring(
                    lastNode.endOffset,
                    Math.min(lastNode.node.textContent.length, lastNode.endOffset + CROSS_PARA_CONTEXT_LENGTH)
                );
            }

            // If we need more context, look at next nodes
            if (suffix.length < CROSS_PARA_CONTEXT_LENGTH) {
                const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT);
                walker.currentNode = lastNode.node;

                let nextNode;
                while ((nextNode = walker.nextNode()) && suffix.length < CROSS_PARA_CONTEXT_LENGTH) {
                    const nodeText = nextNode.textContent.trim();
                    if (nodeText) {
                        const needed = CROSS_PARA_CONTEXT_LENGTH - suffix.length;
                        suffix = suffix + nodeText.substring(0, Math.min(nodeText.length, needed));
                    }
                }
            }
        } catch (e) {
            console.warn("Stickara: Error getting cross-paragraph suffix:", e);
        }

        // Store additional metadata for cross-paragraph highlights
        const serialized = {
            prefix: prefix.replace(/\s+/g, ' ').trim(),
            text: text,
            suffix: suffix.replace(/\s+/g, ' ').trim(),
            isCrossParagraph: true,
            segmentCount: textNodes.length,
            // Store first and last segment text for additional validation
            firstSegmentText: textNodes[0].node.textContent.substring(
                textNodes[0].startOffset,
                textNodes[0].endOffset
            ).trim(),
            lastSegmentText: textNodes[textNodes.length - 1].node.textContent.substring(
                textNodes[textNodes.length - 1].startOffset,
                textNodes[textNodes.length - 1].endOffset
            ).trim()
        };

        console.log(`Stickara: Serialized cross-paragraph highlight with ${textNodes.length} segments`);
        return serialized;

    } catch (error) {
        console.warn("Stickara: Error in cross-paragraph serialization, falling back:", error);
        return serializeOriginalContext(range, text);
    }
}

/**
 * Original serialization method as fallback
 * @param {Range} range - The range to serialize
 * @param {string} text - The text content
 * @returns {Object} - Original serialization format
 */
function serializeOriginalContext(range, text) {
    const prefixRange = document.createRange();
    prefixRange.setStart(document.body, 0);
    prefixRange.setEnd(range.startContainer, range.startOffset);
    const prefix = getTextFromRange(prefixRange).replace(/\s+/g, ' ').slice(-HIGHLIGHT_CONTEXT_LENGTH);

    const suffixRange = document.createRange();
    suffixRange.setStart(range.endContainer, range.endOffset);
    suffixRange.setEnd(document.body, document.body.childNodes.length);
    const suffix = getTextFromRange(suffixRange).replace(/\s+/g, ' ').slice(0, HIGHLIGHT_CONTEXT_LENGTH);

    return { prefix, text, suffix, isCrossParagraph: false };
}

/**
 * Enhanced deserialization for cross-paragraph highlights
 * @param {Object} data - Serialized cross-paragraph highlight data
 * @returns {Range|null} - Reconstructed range or null on failure
 */
function deserializeCrossParagraphRange(data) {
    const localHighlightId = data?.id || '(no id)';

    try {
        // Use enhanced search strategy for cross-paragraph highlights
        // First, try to find the text using segment-based approach
        if (data.firstSegmentText && data.lastSegmentText) {
            const range = findCrossParagraphRangeBySegments(data);
            if (range) {
                console.log(`[SUCCESS Cross-Paragraph Segments] Range deserialized for ID ${localHighlightId}!`);
                return range;
            }
        }

        // Fallback to enhanced text search with relaxed context matching
        const range = findCrossParagraphRangeByText(data);
        if (range) {
            console.log(`[SUCCESS Cross-Paragraph Text] Range deserialized for ID ${localHighlightId}!`);
            return range;
        }

        // Final fallback to original deserialization method
        console.log(`Stickara: Cross-paragraph deserialization failed, trying original method for ID ${localHighlightId}`);
        return deserializeOriginalRange(data);

    } catch (error) {
        console.warn(`Stickara: Error in cross-paragraph deserialization for ID ${localHighlightId}:`, error);
        return deserializeOriginalRange(data);
    }
}

/**
 * Find cross-paragraph range using first and last segment text
 * @param {Object} data - Serialized data with segment information
 * @returns {Range|null} - Found range or null
 */
function findCrossParagraphRangeBySegments(data) {
    try {
        const firstSegmentText = data.firstSegmentText.trim();
        const lastSegmentText = data.lastSegmentText.trim();
        const fullText = data.text;

        if (!firstSegmentText || !lastSegmentText) {
            return null;
        }

        // Find first segment
        const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => {
                const parent = node.parentElement;
                if (parent && (
                    parent.closest('script, style') ||
                    parent.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`) ||
                    parent.closest('[style*="display: none"], [hidden]') ||
                    parent.offsetParent === null
                )) return NodeFilter.FILTER_REJECT;
                if (!node.textContent.trim()) return NodeFilter.FILTER_SKIP;
                return NodeFilter.FILTER_ACCEPT;
            }
        });

        let startNode = null, startOffset = -1;
        let endNode = null, endOffset = -1;

        // Find start position
        let currentNode;
        while (currentNode = walker.nextNode()) {
            const nodeText = currentNode.textContent;
            const firstSegmentIndex = nodeText.indexOf(firstSegmentText);

            if (firstSegmentIndex !== -1) {
                startNode = currentNode;
                startOffset = firstSegmentIndex;
                break;
            }
        }

        if (!startNode) {
            return null;
        }

        // Reset walker to find end position
        walker.currentNode = document.body;
        let foundStart = false;

        while (currentNode = walker.nextNode()) {
            if (currentNode === startNode) {
                foundStart = true;
                continue;
            }

            if (foundStart) {
                const nodeText = currentNode.textContent;
                const lastSegmentIndex = nodeText.indexOf(lastSegmentText);

                if (lastSegmentIndex !== -1) {
                    endNode = currentNode;
                    endOffset = lastSegmentIndex + lastSegmentText.length;
                    break;
                }
            }
        }

        // If last segment is in the same node as first segment
        if (!endNode && startNode) {
            const nodeText = startNode.textContent;
            const lastSegmentIndex = nodeText.indexOf(lastSegmentText, startOffset);

            if (lastSegmentIndex !== -1) {
                endNode = startNode;
                endOffset = lastSegmentIndex + lastSegmentText.length;
            }
        }

        if (startNode && endNode && startOffset !== -1 && endOffset !== -1) {
            const range = document.createRange();
            range.setStart(startNode, startOffset);
            range.setEnd(endNode, endOffset);

            // Validate the reconstructed text
            const reconstructedText = getTextFromRange(range);
            const reconstructedNorm = reconstructedText.replace(/\s+/g, ' ').trim();
            const targetNorm = fullText.replace(/\s+/g, ' ').trim();

            if (reconstructedNorm === targetNorm || isFuzzyTextMatch(reconstructedNorm, targetNorm)) {
                return range;
            }
        }

        return null;

    } catch (error) {
        console.warn("Stickara: Error in segment-based cross-paragraph search:", error);
        return null;
    }
}

/**
 * Find cross-paragraph range using enhanced text search
 * @param {Object} data - Serialized data
 * @returns {Range|null} - Found range or null
 */
function findCrossParagraphRangeByText(data) {
    try {
        const targetText = data.text;
        const targetTextNorm = targetText.replace(/\s+/g, ' ').trim();
        const prefixNorm = data.prefix.replace(/\s+/g, ' ').trim();
        const suffixNorm = data.suffix.replace(/\s+/g, ' ').trim();

        // Use relaxed context matching for cross-paragraph highlights
        const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => {
                const parent = node.parentElement;
                if (parent && (
                    parent.closest('script, style') ||
                    parent.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`) ||
                    parent.closest('[style*="display: none"], [hidden]') ||
                    parent.offsetParent === null
                )) return NodeFilter.FILTER_REJECT;
                if (!node.textContent.trim()) return NodeFilter.FILTER_SKIP;
                return NodeFilter.FILTER_ACCEPT;
            }
        });

        let accumulatedText = "";
        let nodesVisited = [];
        let currentNode;

        while (currentNode = walker.nextNode()) {
            const nodeTextOrig = currentNode.nodeValue;
            const nodeTextNorm = nodeTextOrig.replace(/\s+/g, ' ');
            if (!nodeTextNorm) continue;

            const nodeStartPos = accumulatedText.length;
            accumulatedText += nodeTextNorm;
            nodesVisited.push({ node: currentNode, start: nodeStartPos, normLength: nodeTextNorm.length });

            // Look for target text with very relaxed context matching
            const targetMatchIndex = accumulatedText.indexOf(targetTextNorm);
            if (targetMatchIndex !== -1) {
                // For cross-paragraph, use very lenient context checking
                let contextMatch = true;

                // Check prefix if available (very lenient)
                if (prefixNorm.length > 0) {
                    const actualPrefixStart = Math.max(0, targetMatchIndex - prefixNorm.length - 50);
                    const actualPrefix = accumulatedText.substring(actualPrefixStart, targetMatchIndex);
                    contextMatch = actualPrefix.includes(prefixNorm.slice(-Math.min(15, prefixNorm.length)));
                }

                // Check suffix if available (very lenient)
                if (contextMatch && suffixNorm.length > 0) {
                    const actualSuffixStart = targetMatchIndex + targetTextNorm.length;
                    const actualSuffix = accumulatedText.substring(actualSuffixStart, actualSuffixStart + suffixNorm.length + 50);
                    contextMatch = actualSuffix.includes(suffixNorm.slice(0, Math.min(15, suffixNorm.length)));
                }

                if (contextMatch) {
                    // Try to create range
                    const range = createRangeFromAccumulatedText(targetMatchIndex, targetTextNorm.length, nodesVisited);
                    if (range) {
                        return range;
                    }
                }
            }
        }

        return null;

    } catch (error) {
        console.warn("Stickara: Error in text-based cross-paragraph search:", error);
        return null;
    }
}

/**
 * Create range from accumulated text and node positions
 * @param {number} startIndex - Start index in accumulated text
 * @param {number} length - Length of target text
 * @param {Array} nodesVisited - Array of visited nodes with positions
 * @returns {Range|null} - Created range or null
 */
function createRangeFromAccumulatedText(startIndex, length, nodesVisited) {
    try {
        const endIndex = startIndex + length;
        let startNode = null, startOffset = -1;
        let endNode = null, endOffset = -1;
        let foundStart = false, foundEnd = false;

        for (const visited of nodesVisited) {
            if (!foundStart && startIndex >= visited.start && startIndex < visited.start + visited.normLength) {
                startNode = visited.node;
                startOffset = mapNormalizedOffsetToOriginal(startNode.nodeValue, startIndex - visited.start);
                if (startOffset !== -1) foundStart = true;
            }
            if (!foundEnd && endIndex > visited.start && endIndex <= visited.start + visited.normLength) {
                endNode = visited.node;
                const normEndOffsetInNode = endIndex - visited.start;
                endOffset = mapNormalizedOffsetToOriginal(endNode.nodeValue, normEndOffsetInNode);
                if (endOffset !== -1) foundEnd = true;
            }
            if (foundStart && foundEnd) break;
        }

        if (foundStart && foundEnd && startNode && endNode) {
            const range = document.createRange();
            const maxStartOffset = startNode.nodeValue.length;
            const maxEndOffset = endNode.nodeValue.length;

            const clampedStartOffset = Math.min(startOffset, maxStartOffset);
            const clampedEndOffset = Math.min(endOffset, maxEndOffset);

            if (startNode === endNode && clampedStartOffset > clampedEndOffset) {
                return null;
            }

            range.setStart(startNode, clampedStartOffset);
            range.setEnd(endNode, clampedEndOffset);

            return range;
        }

        return null;

    } catch (error) {
        console.warn("Stickara: Error creating range from accumulated text:", error);
        return null;
    }
}

/**
 * Fallback to original deserialization method
 * @param {Object} data - Serialized data
 * @returns {Range|null} - Range or null
 */
function deserializeOriginalRange(data) {
    // Remove the isCrossParagraph flag and try original method
    const originalData = { ...data };
    delete originalData.isCrossParagraph;
    delete originalData.segmentCount;
    delete originalData.firstSegmentText;
    delete originalData.lastSegmentText;

    // Continue with original deserialization logic...
    const targetTextOriginal = originalData.text;
    const targetTextNorm = targetTextOriginal.replace(/\s+/g, ' ').trim();

    // Use the existing fallback logic from the original function
    if (targetTextNorm.length > 10) {
        const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => {
                const parent = node.parentElement;
                if (parent && (
                    parent.closest('script, style') ||
                    parent.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`) ||
                    parent.closest('[style*="display: none"], [hidden]') ||
                    parent.offsetParent === null
                )) return NodeFilter.FILTER_REJECT;
                if (!node.textContent.trim()) return NodeFilter.FILTER_SKIP;
                return NodeFilter.FILTER_ACCEPT;
            }
        });

        let accumulatedText = "";
        let nodesVisited = [];
        let currentNode;

        while (currentNode = walker.nextNode()) {
            const nodeTextOrig = currentNode.nodeValue;
            const nodeTextNorm = nodeTextOrig.replace(/\s+/g, ' ');
            if (!nodeTextNorm) continue;

            const nodeStartPos = accumulatedText.length;
            accumulatedText += nodeTextNorm;
            nodesVisited.push({ node: currentNode, start: nodeStartPos, normLength: nodeTextNorm.length });

            const textMatchIndex = accumulatedText.indexOf(targetTextNorm);
            if (textMatchIndex !== -1) {
                const range = createRangeFromAccumulatedText(textMatchIndex, targetTextNorm.length, nodesVisited);
                if (range) {
                    const reconstructedText = getTextFromRange(range);
                    const reconstructedNorm = reconstructedText.replace(/\s+/g, ' ').trim();

                    if (reconstructedNorm === targetTextNorm || isFuzzyTextMatch(reconstructedNorm, targetTextNorm)) {
                        return range;
                    }
                }
                break;
            }
        }
    }

    return null;
}

/**
 * Maps an offset from a normalized string back to the original string offset.
 * NEW APPROACH: Builds a direct mapping. Includes enhanced logging and STRICTER error return.
 * @param {string} originalString
 * @param {number} normalizedTargetOffset - The target offset in the conceptual "normalized" string.
 * @returns {number} The corresponding offset in the original string, or -1 on failure.
 */
function mapNormalizedOffsetToOriginal(originalString, normalizedTargetOffset) {
    if (normalizedTargetOffset < 0) return -1; // Invalid target
    if (normalizedTargetOffset === 0) return 0; // Target is start

    let originalIndex = 0;
    let normalizedIndex = 0;
    let inWhitespaceSequence = false;
    const normToOrigMap = { 0: 0 }; // Map: normalized index -> original index *after* char/sequence

    while (originalIndex < originalString.length) {
        const originalChar = originalString[originalIndex];
        const isWhitespace = /\s/.test(originalChar);

        if (!isWhitespace) {
            normalizedIndex++;
            normToOrigMap[normalizedIndex] = originalIndex + 1;
            inWhitespaceSequence = false;
        } else {
            if (!inWhitespaceSequence) {
                normalizedIndex++;
                normToOrigMap[normalizedIndex] = originalIndex + 1;
                inWhitespaceSequence = true;
            }
            // If already in whitespace, normToOrigMap[normalizedIndex] remains pointing
            // to the end of the *first* whitespace char in the sequence.
        }

        // Check if we just created the map entry for our target
        if (normalizedIndex === normalizedTargetOffset && normToOrigMap.hasOwnProperty(normalizedTargetOffset)) {
            // console.log(`---> mapNormalizedOffsetToOriginal v6: Target ${normalizedTargetOffset} mapped. Returning Original Idx: ${normToOrigMap[normalizedTargetOffset]}`); // Debug v6
            return normToOrigMap[normalizedTargetOffset];
        }

        originalIndex++;
    }

    // Loop finished. Did we ever reach the target normalized offset?
    const maxMappedNormIndex = Math.max(0, ...Object.keys(normToOrigMap).map(Number));

    if (normalizedTargetOffset > maxMappedNormIndex) {
        // Target offset is genuinely beyond the normalized content of this node.
        // This is NOT an error in mapping itself, but the target isn't here.
        // Let the calling function handle this (it means the range spans multiple nodes).
        // However, if the *calling function* expected the offset to be within *this node*
        // based on the accumulated text length, it indicates a mismatch.
        console.warn(`---> mapNormalizedOffsetToOriginal v6 INFO: Target offset (${normalizedTargetOffset}) is beyond this node's max mapped normalized index (${maxMappedNormIndex}). Returning -1.`); // Debug v6
        return -1; // Indicate target not found within this node's normalized length
    } else {
         // This means the target offset *should* have been reachable within the loop,
         // but we exited without finding it. This implies a potential logic issue
         // or that the target *perfectly* aligns with the very end.
         // Check the map one last time.
         if (normToOrigMap.hasOwnProperty(normalizedTargetOffset)) {
             console.log(`---> mapNormalizedOffsetToOriginal v6 NOTE: Target ${normalizedTargetOffset} found in map after loop finished. Returning mapped value: ${normToOrigMap[normalizedTargetOffset]}`); // Debug v6
             return normToOrigMap[normalizedTargetOffset];
         } else {
             // The target offset was within the theoretical range but wasn't mapped. Error.
             console.error(`---> mapNormalizedOffsetToOriginal v6 ERROR: Target ${normalizedTargetOffset} (<= max ${maxMappedNormIndex}) was NOT found in map. Logic error or unexpected structure. Returning -1.`); // Debug v6
             return -1; // Indicate mapping failure
        }
    }
}

/**
 * Performs fuzzy text matching to handle cases where content has minor changes
 * @param {string} text1 - First text to compare
 * @param {string} text2 - Second text to compare
 * @returns {boolean} - True if texts are similar enough
 */
function isFuzzyTextMatch(text1, text2) {
    if (!text1 || !text2) return false;
    if (text1 === text2) return true;

    // Calculate similarity threshold based on text length
    const minLength = Math.min(text1.length, text2.length);
    const maxLength = Math.max(text1.length, text2.length);

    // If length difference is too large, not a match
    if (maxLength - minLength > Math.max(10, minLength * 0.3)) {
        return false;
    }

    // Simple similarity check - count matching characters in order
    let matches = 0;
    let i = 0, j = 0;

    while (i < text1.length && j < text2.length) {
        if (text1[i] === text2[j]) {
            matches++;
            i++;
            j++;
        } else {
            // Try to skip one character in either string
            if (i + 1 < text1.length && text1[i + 1] === text2[j]) {
                i += 2;
                j++;
            } else if (j + 1 < text2.length && text1[i] === text2[j + 1]) {
                i++;
                j += 2;
            } else {
                i++;
                j++;
            }
        }
    }

    // Calculate similarity ratio
    const similarity = matches / maxLength;
    const threshold = 0.8; // 80% similarity required

    return similarity >= threshold;
}

/**
 * Attempts to reconstruct a Range object from serialized context data.
 * Uses TreeWalker and the revised offset mapping. Logs specific failure reasons.
 * Includes modification to use normalized text comparison for final validation.
 * Includes enhanced failure logging AND FUZZIER CONTEXT MATCHING + ELLIPSIS HANDLING.
 * V2: Adds more validation steps before range creation.
 * @param {object} data - The serialized range data object { prefix, text, suffix, id?, color? }.
 * @returns {Range|null} The reconstructed Range object or null on failure reason logged.
 */
function deserializeRangeByContext(data) {
    let failureReason = "Unknown";
    let localHighlightId = data?.id || '(no id)';

    // --- Initial Data Validation ---
    if (!data || typeof data.prefix !== 'string' || typeof data.text !== 'string' || typeof data.suffix !== 'string') {
        failureReason = "Invalid data structure"; console.warn(`Stickara Deserialize: ${failureReason} provided for highlight ID ${localHighlightId}.`, data); return null;
    }
    if (!data.text.trim()) {
        failureReason = "Empty text in data"; console.warn(`Stickara Deserialize: ${failureReason}. Skipping highlight ID ${localHighlightId}.`, data); return null;
    }

    // Check if this is a cross-paragraph highlight
    if (data.isCrossParagraph) {
        console.log(`Stickara: Attempting to deserialize cross-paragraph highlight ID ${localHighlightId} with ${data.segmentCount || 'unknown'} segments`);
        return deserializeCrossParagraphRange(data);
    }
    const targetTextOriginal = data.text;
    const prefixNorm = data.prefix.replace(/\s+/g, ' ').trim();
    const suffixNorm = data.suffix.replace(/\s+/g, ' ').trim();
    const targetTextNorm = targetTextOriginal.replace(/\s+/g, ' ').trim();
    if (!targetTextNorm) {
        failureReason = "Normalized target text is empty"; console.warn(`Stickara Deserialize: ${failureReason}. Skipping highlight ID ${localHighlightId}. Original text: "${data.text}"`); return null;
    }

    // --- Enhanced Ellipsis and Fuzzy Matching Handling ---
    const ellipsisChar = '…'; // Or use the actual character if different (e.g., three dots ...)
    const ellipsisInTarget = targetTextNorm.includes(ellipsisChar);
    let searchTextNorm = targetTextNorm;
    let targetLengthNorm = targetTextNorm.length; // Use this for calculating end offset
    let useFuzzyMatching = false;

    if (ellipsisInTarget) {
        // Use only the part *before* the ellipsis for the initial search
        searchTextNorm = targetTextNorm.split(ellipsisChar)[0].trimEnd(); // Search for text before ellipsis
        if (!searchTextNorm) {
             failureReason = "Text before ellipsis is empty";
             console.warn(`Stickara Deserialize: ${failureReason}. Skipping highlight ID ${localHighlightId}. Original text: "${data.text}"`);
             return null;
        }
        useFuzzyMatching = true;
        // console.log(`   Ellipsis detected in target ID ${localHighlightId}. Searching for start: "${searchTextNorm}"`);
    }

    // For dynamic content, also try fuzzy matching if exact match fails
    const shouldTryFuzzyMatching = useFuzzyMatching || searchTextNorm.length > 20; // Try fuzzy for longer texts

    // --- End Enhanced Ellipsis and Fuzzy Matching Handling ---


    // --- TreeWalker Setup ---
    const walker = document.createTreeWalker( document.body, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => {
                const parent = node.parentElement;
                // Exclude script, style, Stickara UI, hidden elements
                if (parent && (
                    parent.closest('script, style') ||
                    parent.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`) ||
                    parent.closest('[style*="display: none"], [hidden]') ||
                    parent.offsetParent === null // Exclude non-rendered elements
                )) return NodeFilter.FILTER_REJECT;
                // Skip nodes containing only whitespace
                if (!node.textContent.trim()) return NodeFilter.FILTER_SKIP;
                return NodeFilter.FILTER_ACCEPT;
            }
    });

    let accumulatedText = "";
    let nodesVisited = [];
    let currentMatchAttempt = 0;
    let currentNode;
    let mappingFailureReason = null; // Hoisted for use across attempts

    while (currentNode = walker.nextNode()) {
        const nodeTextOrig = currentNode.nodeValue;
        const nodeTextNorm = nodeTextOrig.replace(/\s+/g, ' ');
        if (!nodeTextNorm) continue;

        const nodeStartPos = accumulatedText.length;
        accumulatedText += nodeTextNorm;
        nodesVisited.push({ node: currentNode, start: nodeStartPos, normLength: nodeTextNorm.length });

        // --- Search and Context Check Loop ---
        let searchStartIndex = 0;
        while (searchStartIndex < accumulatedText.length) {
            // 1. Find the core search text
            const targetMatchIndex = accumulatedText.indexOf(searchTextNorm, searchStartIndex);
            if (targetMatchIndex === -1) break; // Target not found in remaining accumulated text

            currentMatchAttempt++;
            // console.log(`   Attempt #${currentMatchAttempt}: Found potential search text "${searchTextNorm}" at index ${targetMatchIndex}.`);

            // 2. Check Prefix (more lenient for dynamic content)
            const actualPrefixStart = Math.max(0, targetMatchIndex - prefixNorm.length - 20); // Increased tolerance
            const actualPrefixFull = accumulatedText.substring(actualPrefixStart, targetMatchIndex);
            const actualPrefixTrimmed = actualPrefixFull.trimEnd();

            // More lenient prefix matching
            let prefixMatch = (prefixNorm.length === 0) ||
                             (actualPrefixTrimmed.endsWith(prefixNorm)) ||
                             (prefixNorm.length > 10 && actualPrefixTrimmed.includes(prefixNorm.slice(-10))); // Partial match for long prefixes

            if (!prefixMatch) {
                // console.warn(`     Attempt #${currentMatchAttempt}: Prefix mismatch for ID ${localHighlightId}. Expected end: "${prefixNorm}", Got End: "${actualPrefixTrimmed.slice(-prefixNorm.length)}"`);
                searchStartIndex = targetMatchIndex + 1; // Start next search *after* current potential match
                continue; // Try next potential match
            }
            // console.log(`     Attempt #${currentMatchAttempt}: Prefix check passed.`);

            // 3. Check Suffix (more lenient for dynamic content)
            const actualSuffixStartIndex = targetMatchIndex + targetLengthNorm; // Use original target length here
            const actualSuffixLookahead = accumulatedText.substring(actualSuffixStartIndex, actualSuffixStartIndex + suffixNorm.length + 20); // Increased tolerance
            const actualSuffixTrimmed = actualSuffixLookahead.trimStart();

            // More lenient suffix matching
            let suffixMatch = (suffixNorm.length === 0) ||
                             (actualSuffixTrimmed.startsWith(suffixNorm)) ||
                             (suffixNorm.length > 10 && actualSuffixTrimmed.includes(suffixNorm.slice(0, 10))); // Partial match for long suffixes

            if (!suffixMatch) {
                 // console.warn(`     Attempt #${currentMatchAttempt}: Suffix mismatch for ID ${localHighlightId}. Expected start: "${suffixNorm}", Got Start: "${actualSuffixTrimmed.substring(0, suffixNorm.length)}"`);
                searchStartIndex = targetMatchIndex + 1; // Start next search *after* current potential match
                continue; // Try next potential match
            }
             // console.log(`     Attempt #${currentMatchAttempt}: Suffix check passed.`);
            // --- END Context Check ---


            // --- If Context Checks Pass, Proceed with Offset Mapping ---
            const textStartIndexAcc = targetMatchIndex;
            const textEndIndexAcc = textStartIndexAcc + targetLengthNorm; // Use original target length

            let startNode = null, startOffsetOrig = -1;
            let endNode = null, endOffsetOrig = -1;
            let foundStart = false, foundEnd = false;
            mappingFailureReason = null; // Reset reason for this attempt

            // Iterate over nodes involved in the current accumulated text scope
            for (const visited of nodesVisited) {
                // Map Start Offset
                 if (!foundStart && textStartIndexAcc >= visited.start && textStartIndexAcc < visited.start + visited.normLength) {
                    startNode = visited.node;
                    startOffsetOrig = mapNormalizedOffsetToOriginal(startNode.nodeValue, textStartIndexAcc - visited.start); // Use updated mapping function
                    if (startOffsetOrig === -1) { mappingFailureReason = "Start offset mapping failed (returned -1)"; break; }
                    foundStart = true;
                }
                // Map End Offset - Use end index based on original target length
                 if (!foundEnd && textEndIndexAcc > visited.start && textEndIndexAcc <= visited.start + visited.normLength) {
                    endNode = visited.node;
                    const normEndOffsetInNode = textEndIndexAcc - visited.start;
                    endOffsetOrig = mapNormalizedOffsetToOriginal(endNode.nodeValue, normEndOffsetInNode); // Use updated mapping function
                    if (endOffsetOrig === -1) { mappingFailureReason = "End offset mapping failed (returned -1)"; break; }
                    foundEnd = true;
                }
                // Optimization: if end node is found *before* start node in list, mapping is likely impossible for this combo
                if (foundEnd && !foundStart && nodesVisited.indexOf(visited) < nodesVisited.findIndex(n => n.node === startNode)) {
                     mappingFailureReason = "End node found before start node in visited list"; break;
                 }
                 if (foundStart && foundEnd) break; // Exit loop once both are found
            }

            // --- Validation Step 1: Check if mapping succeeded ---
            if (mappingFailureReason) {
                failureReason = mappingFailureReason; // Update overall failure reason if needed
                console.warn(`   Stickara Deserialize Attempt #${currentMatchAttempt}: ${failureReason} for ID ${localHighlightId}. Skipping this context match.`);
                searchStartIndex = targetMatchIndex + 1; // Try next context match
                continue;
            }
            if (!startNode || !endNode || startOffsetOrig === -1 || endOffsetOrig === -1) {
                failureReason = "Node/Offset mapping failed (nodes/offsets invalid)";
                console.warn(`   Stickara Deserialize Attempt #${currentMatchAttempt}: ${failureReason} for ID ${localHighlightId}. Skipping context match.`);
                searchStartIndex = targetMatchIndex + 1; // Try next context match
                continue;
            }

            // --- Validation Step 2: Check Node Types and if still in DOM ---
            if (startNode.nodeType !== Node.TEXT_NODE || endNode.nodeType !== Node.TEXT_NODE ||
                !document.body.contains(startNode) || !document.body.contains(endNode)) {
                 failureReason = "Mapped start/end node is invalid or not in DOM";
                 console.warn(`   Stickara Deserialize Attempt #${currentMatchAttempt}: ${failureReason} for ID ${localHighlightId}. Skipping context match.`);
                 searchStartIndex = targetMatchIndex + 1; // Try next context match
                 continue;
            }

            // --- Create Range with Robust Error Handling ---
            try {
                const range = document.createRange();
                const maxStartOffset = startNode.nodeValue.length;
                const maxEndOffset = endNode.nodeValue.length;

                // --- Validation Step 3: Check offset bounds ---
                if (startOffsetOrig > maxStartOffset || endOffsetOrig > maxEndOffset) {
                     failureReason = `Offset out of bounds (Start: ${startOffsetOrig}/${maxStartOffset}, End: ${endOffsetOrig}/${maxEndOffset})`;
                     console.warn(`     Attempt #${currentMatchAttempt}: ${failureReason} for ID ${localHighlightId}. Clamping offsets.`);
                     // Allow clamping for now, but log it clearly. Could choose to fail here too.
                }
                const clampedStartOffset = Math.min(startOffsetOrig, maxStartOffset);
                const clampedEndOffset = Math.min(endOffsetOrig, maxEndOffset);

                // --- Validation Step 4: Check for invalid start/end in same node ---
                if (startNode === endNode && clampedStartOffset > clampedEndOffset) {
                     failureReason = `Invalid offsets in same node (start ${clampedStartOffset} > end ${clampedEndOffset})`;
                     console.error(`     Attempt #${currentMatchAttempt}: FATAL ${failureReason} for ID ${localHighlightId}. Skipping.`);
                     searchStartIndex = targetMatchIndex + 1; // Move to next potential match
                     continue; // IMPORTANT: Skip this specific invalid match attempt
                }

                range.setStart(startNode, clampedStartOffset);
                range.setEnd(endNode, clampedEndOffset);

                // --- Enhanced Final Text Validation with Fuzzy Matching ---
                const reconstructedText = getTextFromRange(range);
                const reconstructedNorm = reconstructedText.replace(/\s+/g, ' ').trim();

                // Try exact match first
                if (ellipsisInTarget || reconstructedNorm === targetTextNorm) {
                    const reason = ellipsisInTarget ? "Ellipsis Relaxed" : "Exact Match";
                    console.log(`[SUCCESS ${reason}] Range deserialized for ID ${localHighlightId} on attempt #${currentMatchAttempt}!`);
                    return range; // SUCCESS! Found a valid range
                }

                // Try fuzzy matching for dynamic content scenarios
                if (shouldTryFuzzyMatching && isFuzzyTextMatch(reconstructedNorm, targetTextNorm)) {
                    console.log(`[SUCCESS Fuzzy Match] Range deserialized for ID ${localHighlightId} on attempt #${currentMatchAttempt}!`);
                    return range; // SUCCESS! Found a valid range with fuzzy matching
                }

                // If all validation fails
                failureReason = "Final text validation failed (normalized and fuzzy)";
                console.warn(`   Stickara Deserialize Attempt #${currentMatchAttempt}: ${failureReason} for ID ${localHighlightId}. Skipping context match.`);
                console.warn(`      > Expected (Norm): "${targetTextNorm}" (Len: ${targetTextNorm.length})`);
                console.warn(`      > Got (Norm):      "${reconstructedNorm}" (Len: ${reconstructedNorm.length})`);
                searchStartIndex = targetMatchIndex + 1; // Try next context match
                continue;
                // --- END Validation ---

            } catch (e) {
                failureReason = "Error creating/setting Range object";
                console.error(`   Stickara Deserialize Attempt #${currentMatchAttempt}: ${failureReason} for ID ${localHighlightId}`, { error: e });
                searchStartIndex = targetMatchIndex + 1; // Try next context match
                continue;
            }
        } // End of inner while loop (searching for target text in accumulatedText)
    } // End of outer while loop (iterating through text nodes)

    // --- Fallback: Try simple text matching without context ---
    if (targetTextNorm.length > 10) { // Only for substantial text
        console.log(`   Attempting fallback text-only matching for ID ${localHighlightId}...`);

        // Reset walker for fallback attempt
        const fallbackWalker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => {
                const parent = node.parentElement;
                if (parent && (
                    parent.closest('script, style') ||
                    parent.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`) ||
                    parent.closest('[style*="display: none"], [hidden]') ||
                    parent.offsetParent === null
                )) return NodeFilter.FILTER_REJECT;
                if (!node.textContent.trim()) return NodeFilter.FILTER_SKIP;
                return NodeFilter.FILTER_ACCEPT;
            }
        });

        let fallbackAccumulatedText = "";
        let fallbackNodesVisited = [];
        let fallbackCurrentNode;

        while (fallbackCurrentNode = fallbackWalker.nextNode()) {
            const nodeTextOrig = fallbackCurrentNode.nodeValue;
            const nodeTextNorm = nodeTextOrig.replace(/\s+/g, ' ');
            if (!nodeTextNorm) continue;

            const nodeStartPos = fallbackAccumulatedText.length;
            fallbackAccumulatedText += nodeTextNorm;
            fallbackNodesVisited.push({ node: fallbackCurrentNode, start: nodeStartPos, normLength: nodeTextNorm.length });

            // Look for exact text match
            const textMatchIndex = fallbackAccumulatedText.indexOf(targetTextNorm);
            if (textMatchIndex !== -1) {
                console.log(`   Found fallback text match at index ${textMatchIndex}`);

                // Try to create range using the same logic
                const textStartIndexAcc = textMatchIndex;
                const textEndIndexAcc = textStartIndexAcc + targetLengthNorm;

                let startNode = null, startOffsetOrig = -1;
                let endNode = null, endOffsetOrig = -1;
                let foundStart = false, foundEnd = false;

                for (const visited of fallbackNodesVisited) {
                    if (!foundStart && textStartIndexAcc >= visited.start && textStartIndexAcc < visited.start + visited.normLength) {
                        startNode = visited.node;
                        startOffsetOrig = mapNormalizedOffsetToOriginal(startNode.nodeValue, textStartIndexAcc - visited.start);
                        if (startOffsetOrig !== -1) foundStart = true;
                    }
                    if (!foundEnd && textEndIndexAcc > visited.start && textEndIndexAcc <= visited.start + visited.normLength) {
                        endNode = visited.node;
                        const normEndOffsetInNode = textEndIndexAcc - visited.start;
                        endOffsetOrig = mapNormalizedOffsetToOriginal(endNode.nodeValue, normEndOffsetInNode);
                        if (endOffsetOrig !== -1) foundEnd = true;
                    }
                    if (foundStart && foundEnd) break;
                }

                if (foundStart && foundEnd && startNode && endNode) {
                    try {
                        const range = document.createRange();
                        const maxStartOffset = startNode.nodeValue.length;
                        const maxEndOffset = endNode.nodeValue.length;

                        const clampedStartOffset = Math.min(startOffsetOrig, maxStartOffset);
                        const clampedEndOffset = Math.min(endOffsetOrig, maxEndOffset);

                        if (startNode === endNode && clampedStartOffset > clampedEndOffset) {
                            continue; // Invalid range
                        }

                        range.setStart(startNode, clampedStartOffset);
                        range.setEnd(endNode, clampedEndOffset);

                        const reconstructedText = getTextFromRange(range);
                        const reconstructedNorm = reconstructedText.replace(/\s+/g, ' ').trim();

                        if (reconstructedNorm === targetTextNorm || isFuzzyTextMatch(reconstructedNorm, targetTextNorm)) {
                            console.log(`[SUCCESS Fallback] Range deserialized for ID ${localHighlightId} using text-only matching!`);
                            return range;
                        }
                    } catch (e) {
                        console.warn(`   Fallback range creation failed for ID ${localHighlightId}:`, e);
                    }
                }
                break; // Only try first match in fallback
            }
        }
    }

    // --- Final Failure Logging ---
    if (failureReason === "Unknown") {
         failureReason = "Search text with matching context not found"; // More specific final reason
    }

    // Only log detailed errors for initial attempts or when debugging
    if (typeof dynamicHighlightState === 'undefined' || dynamicHighlightState.restorationAttempts <= 1) {
        console.warn(`   [FAIL DESERIALIZE Reason: ${failureReason}] Failed to deserialize highlight ID ${localHighlightId}.`);
        console.warn(`      Text: "${data.text?.substring(0, 50)}${data.text?.length > 50 ? '...' : ''}"`);
    } else {
        // Less verbose logging for automatic restoration attempts
        console.log(`   [FAIL DESERIALIZE] ${failureReason} for highlight ${localHighlightId}`);
    }

    return null; // Return null if no valid range found after all attempts
} // End of deserializeRangeByContext


/**
 * Enhanced cross-paragraph highlighting that properly handles selections spanning multiple elements
 * @param {Range} range - The range to highlight
 * @param {string} highlightId - Unique identifier for the highlight
 * @param {string|null} color - The color name or null
 * @param {string} style - The style name
 * @param {boolean} hasLinkedNote - Whether this highlight has a linked note
 * @returns {Object|null} - Object with mark element or null on failure
 */
function applyCrossParagraphHighlight(range, highlightId, color, style, hasLinkedNote) {
    if (!range || range.collapsed) {
        return null;
    }

    try {
        // Check if the range spans multiple block elements
        const startContainer = range.startContainer;
        const endContainer = range.endContainer;

        // If start and end are in the same text node, use simple approach
        if (startContainer === endContainer && startContainer.nodeType === Node.TEXT_NODE) {
            return applySimpleHighlight(range, highlightId, color, style, hasLinkedNote);
        }

        // For cross-element selections, we need to handle each text node separately
        // but group them under a common highlight ID
        const textNodes = getTextNodesInRange(range);

        if (textNodes.length === 0) {
            console.warn("Stickara: No text nodes found in range for cross-paragraph highlight");
            return null;
        }

        // Create individual highlights for each text node segment
        const highlightElements = [];
        let firstMark = null;

        for (let i = 0; i < textNodes.length; i++) {
            const nodeInfo = textNodes[i];
            const { node, startOffset, endOffset } = nodeInfo;

            // Create a range for this specific text node segment
            const nodeRange = document.createRange();
            nodeRange.setStart(node, startOffset);
            nodeRange.setEnd(node, endOffset);

            // Create the mark element
            const mark = document.createElement("mark");
            let classList = `${HIGHLIGHT_CLASS}`;

            if (style === 'color') {
                classList += ` color-${color}`;
                mark.dataset.style = 'color';
                mark.dataset.color = color;
            } else {
                classList += ` style-${style}`;
                mark.dataset.style = style;
            }

            // Add class for highlights with linked notes
            if (hasLinkedNote) {
                classList += ' has-linked-note';
                mark.dataset.hasLinkedNote = 'true';
            }

            // Add cross-paragraph class and segment info
            classList += ' cross-paragraph-segment';
            mark.dataset.segmentIndex = i.toString();
            mark.dataset.totalSegments = textNodes.length.toString();

            mark.className = classList;
            mark.dataset.highlightId = highlightId;

            // Extract and wrap the content
            const contents = nodeRange.extractContents();
            mark.appendChild(contents);
            nodeRange.insertNode(mark);

            highlightElements.push(mark);
            if (i === 0) {
                firstMark = mark; // Return the first mark as the primary element
            }
        }

        // Store references to all segments in each mark for easier management
        highlightElements.forEach(mark => {
            mark.dataset.segmentElements = JSON.stringify(
                highlightElements.map(el => el.dataset.segmentIndex)
            );
        });

        console.log(`Stickara: Applied cross-paragraph highlight with ${highlightElements.length} segments for ID ${highlightId}`);

        return {
            mark: firstMark,
            segments: highlightElements,
            isCrossParagraph: true
        };

    } catch (error) {
        console.error("Stickara: Error applying cross-paragraph highlight:", error);
        return null;
    }
}

/**
 * Simple highlight application for single text node ranges
 * @param {Range} range - The range to highlight
 * @param {string} highlightId - Unique identifier for the highlight
 * @param {string|null} color - The color name or null
 * @param {string} style - The style name
 * @param {boolean} hasLinkedNote - Whether this highlight has a linked note
 * @returns {Object|null} - Object with mark element or null on failure
 */
function applySimpleHighlight(range, highlightId, color, style, hasLinkedNote) {
    try {
        const mark = document.createElement("mark");
        let classList = `${HIGHLIGHT_CLASS}`;

        if (style === 'color') {
            classList += ` color-${color}`;
            mark.dataset.style = 'color';
            mark.dataset.color = color;
        } else {
            classList += ` style-${style}`;
            mark.dataset.style = style;
        }

        // Add class for highlights with linked notes
        if (hasLinkedNote) {
            classList += ' has-linked-note';
            mark.dataset.hasLinkedNote = 'true';
        }

        mark.className = classList;
        mark.dataset.highlightId = highlightId;

        // Use extractContents and insertNode for simple case
        const contents = range.extractContents();
        mark.appendChild(contents);
        range.insertNode(mark);

        return {
            mark: mark,
            segments: [mark],
            isCrossParagraph: false
        };

    } catch (error) {
        console.error("Stickara: Error applying simple highlight:", error);
        return null;
    }
}

/**
 * Gets all text nodes within a range and calculates their start/end offsets
 * @param {Range} range - The range to analyze
 * @returns {Array} - Array of objects with node, startOffset, endOffset
 */
function getTextNodesInRange(range) {
    const textNodes = [];
    const walker = document.createTreeWalker(
        range.commonAncestorContainer,
        NodeFilter.SHOW_TEXT,
        {
            acceptNode: function(node) {
                // Check if this text node intersects with our range
                if (range.intersectsNode && range.intersectsNode(node)) {
                    return NodeFilter.FILTER_ACCEPT;
                }

                // Fallback for browsers that don't support intersectsNode
                const nodeRange = document.createRange();
                nodeRange.selectNodeContents(node);

                // Check if ranges overlap
                if (range.compareBoundaryPoints(Range.END_TO_START, nodeRange) <= 0 &&
                    range.compareBoundaryPoints(Range.START_TO_END, nodeRange) >= 0) {
                    return NodeFilter.FILTER_ACCEPT;
                }

                return NodeFilter.FILTER_REJECT;
            }
        }
    );

    let node;
    while (node = walker.nextNode()) {
        // Skip empty text nodes
        if (!node.textContent.trim()) {
            continue;
        }

        // Calculate the actual start and end offsets for this node
        let startOffset = 0;
        let endOffset = node.textContent.length;

        // If this is the start container, use the range's start offset
        if (node === range.startContainer) {
            startOffset = range.startOffset;
        }

        // If this is the end container, use the range's end offset
        if (node === range.endContainer) {
            endOffset = range.endOffset;
        }

        // Only include if there's actual content to highlight
        if (startOffset < endOffset) {
            textNodes.push({
                node: node,
                startOffset: startOffset,
                endOffset: endOffset
            });
        }
    }

    return textNodes;
}

/**
 * Applies the visual highlight style (using a <mark> element) to a given Range.
 * ADDS the delete button with a click listener.
 * @param {Range} range
 * @param {string} highlightId
 * @param {string|null} [color] - The color name (e.g., 'yellow') or null/undefined.
 * @param {string} style - The style name (e.g., 'color', 'underline', 'wavy', 'border-thick').
 * @param {boolean} [hasLinkedNote=false] - Whether this highlight has a linked note.
 */
function applyHighlight(range, highlightId, color, style, hasLinkedNote = false) {
    if (!range || !highlightId || range.collapsed || !style) {
        return;
    }
    // Basic validation
    const validStyles = ['color', 'underline', 'wavy', 'border-thick', 'strikethrough', 'blur'];
    if (!validStyles.includes(style)) {
        console.warn(`Stickara: Invalid highlight style '${style}' provided. Cannot apply.`);
        return; // Stop if style is invalid
    }
    if (style === 'color' && (!color || typeof HIGHLIGHT_COLORS === 'undefined' || !Object.keys(HIGHLIGHT_COLORS).includes(color))) {
        console.warn(`Stickara: Style is 'color' but invalid/missing color '${color}' provided. Defaulting.`);
        color = DEFAULT_HIGHLIGHT_COLOR;
    }

    // Check if this highlight has a linked note from the highlightsData
    if (!hasLinkedNote && Array.isArray(highlightsData)) {
        const highlight = highlightsData.find(h => h.id === highlightId);
        if (highlight && highlight.hasLinkedNote) {
            hasLinkedNote = true;
        }
    }

    try {
        // Check if the range is already completely within an existing mark for this ID
        let existingMark = null;
        if (range.startContainer.nodeType === Node.ELEMENT_NODE && range.startContainer.matches(`mark.${HIGHLIGHT_CLASS}[data-highlight-id="${highlightId}"]`)) {
            existingMark = range.startContainer;
        } else {
            existingMark = range.startContainer.parentElement?.closest(`mark.${HIGHLIGHT_CLASS}[data-highlight-id="${highlightId}"]`);
        }
        if (existingMark) {
            let endMark = null;
            if (range.endContainer.nodeType === Node.ELEMENT_NODE && range.endContainer.matches(`mark.${HIGHLIGHT_CLASS}[data-highlight-id="${highlightId}"]`)) {
               endMark = range.endContainer;
            } else {
               endMark = range.endContainer.parentElement?.closest(`mark.${HIGHLIGHT_CLASS}[data-highlight-id="${highlightId}"]`);
            }
            if (endMark && existingMark === endMark) {
                // Already marked. Apply new style exclusively.
                let classList = `${HIGHLIGHT_CLASS}`;
                // Remove old style/color classes before adding new one
                existingMark.classList.remove(...Array.from(existingMark.classList).filter(c => c.startsWith('color-') || c.startsWith('style-')));

                if (style === 'color') {
                    classList += ` color-${color}`; // Assumes color is valid/defaulted above
                    existingMark.dataset.style = 'color';
                    existingMark.dataset.color = color;
                } else { // Apply style class only
                    classList += ` style-${style}`;
                    existingMark.dataset.style = style;
                    existingMark.dataset.color = ''; // Clear color data
                }

                // Add class for highlights with linked notes
                if (hasLinkedNote) {
                    classList += ' has-linked-note';
                    existingMark.dataset.hasLinkedNote = 'true';
                } else {
                    // Remove linked note class if it exists
                    existingMark.classList.remove('has-linked-note');
                    delete existingMark.dataset.hasLinkedNote;
                }

                // Apply new classes
                existingMark.classList.add(...classList.split(' ').filter(c => c !== HIGHLIGHT_CLASS));

                // --- Add delete button if it doesn't exist ---
                if (!existingMark.querySelector('.Stickara-delete-highlight-btn')) {
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'Stickara-delete-highlight-btn';
                    deleteBtn.innerHTML = '×'; // Use × symbol
                    deleteBtn.title = 'Remove highlight';
                    deleteBtn.setAttribute('aria-label', 'Remove highlight');
                    deleteBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        removeHighlight(highlightId, existingMark); // Call removeHighlight directly
                    });
                    existingMark.appendChild(deleteBtn);
                }

                // --- Add note button if it doesn't exist ---
                if (!existingMark.querySelector('.Stickara-note-highlight-btn')) {
                    const noteBtn = document.createElement('button');
                    noteBtn.className = 'Stickara-note-highlight-btn';
                    noteBtn.innerHTML = '📝'; // Use 📝 symbol
                    noteBtn.title = 'Add note to highlight';
                    noteBtn.setAttribute('aria-label', 'Add note to highlight');
                    noteBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        showNoteEditor(highlightId, existingMark.textContent);
                    });
                    existingMark.appendChild(noteBtn);
                }

                // --- Check if this highlight has a note and display it ---
                const highlight = highlightsData.find(h => h.id === highlightId);
                if (highlight && highlight.noteText) {
                    // Update or create note display
                    let noteDisplay = existingMark.querySelector('.Stickara-highlight-note-display');
                    if (!noteDisplay) {
                        noteDisplay = document.createElement('div');
                        noteDisplay.className = 'Stickara-highlight-note-display';

                        // Add drag handle
                        const dragHandle = document.createElement('div');
                        dragHandle.className = 'Stickara-note-drag-handle';
                        noteDisplay.appendChild(dragHandle);

                        // Apply stored position if available
                        if (highlight.notePosition) {
                            noteDisplay.style.top = highlight.notePosition.top;
                            noteDisplay.style.left = highlight.notePosition.left;
                            if (highlight.notePosition.marginTop) {
                                noteDisplay.style.marginTop = highlight.notePosition.marginTop;
                            }
                        }

                        // Make the note draggable
                        makeDraggable(noteDisplay, existingMark, highlightId);

                        existingMark.appendChild(noteDisplay);
                    }

                    // Clear existing content
                    while (noteDisplay.firstChild) {
                        if (noteDisplay.firstChild.classList &&
                            noteDisplay.firstChild.classList.contains('Stickara-note-drag-handle')) {
                            // Keep the drag handle
                            const dragHandle = noteDisplay.firstChild;
                            noteDisplay.innerHTML = '';
                            noteDisplay.appendChild(dragHandle);
                            break;
                        } else {
                            noteDisplay.removeChild(noteDisplay.firstChild);
                        }
                    }

                    // Add the text
                    noteDisplay.appendChild(document.createTextNode(highlight.noteText));

                    // Make sure we have a drag handle
                    if (!noteDisplay.querySelector('.Stickara-note-drag-handle')) {
                        const dragHandle = document.createElement('div');
                        dragHandle.className = 'Stickara-note-drag-handle';
                        noteDisplay.insertBefore(dragHandle, noteDisplay.firstChild);
                    }
                }
                return; // Don't re-wrap
            }
        }

        // Not already marked or partially marked, proceed to create new mark
        // Use enhanced cross-paragraph highlighting approach
        const highlightResult = applyCrossParagraphHighlight(range, highlightId, color, style, hasLinkedNote);
        if (!highlightResult) {
            console.warn(`Stickara: Failed to apply cross-paragraph highlight for ID ${highlightId}`);
            return;
        }

        const mark = highlightResult.mark;

        // --- Add the delete button to the newly created mark ---
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'Stickara-delete-highlight-btn';
        deleteBtn.innerHTML = '×'; // Use × symbol
        deleteBtn.title = 'Remove highlight';
        deleteBtn.setAttribute('aria-label', 'Remove highlight');
        deleteBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            removeHighlight(highlightId, mark); // Call removeHighlight directly
        });
        mark.appendChild(deleteBtn);

        // --- Add the note button to the newly created mark ---
        const noteBtn = document.createElement('button');
        noteBtn.className = 'Stickara-note-highlight-btn';
        noteBtn.innerHTML = '📝'; // Use 📝 symbol
        noteBtn.title = 'Add note to highlight';
        noteBtn.setAttribute('aria-label', 'Add note to highlight');
        noteBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            showNoteEditor(highlightId, mark.textContent);
        });
        mark.appendChild(noteBtn);

        // --- Check if this highlight has a note and display it ---
        const highlight = highlightsData.find(h => h.id === highlightId);
        if (highlight && highlight.noteText) {
            const noteDisplay = document.createElement('div');
            noteDisplay.className = 'Stickara-highlight-note-display';

            // Add drag handle
            const dragHandle = document.createElement('div');
            dragHandle.className = 'Stickara-note-drag-handle';
            noteDisplay.appendChild(dragHandle);

            // Set the note text - Use createTextNode to prevent XSS
            noteDisplay.appendChild(document.createTextNode(highlight.noteText));

            // Apply stored position if available
            if (highlight.notePosition) {
                noteDisplay.style.top = highlight.notePosition.top;
                noteDisplay.style.left = highlight.notePosition.left;
                if (highlight.notePosition.marginTop) {
                    noteDisplay.style.marginTop = highlight.notePosition.marginTop;
                }
            }

            // Make the note draggable
            makeDraggable(noteDisplay, mark, highlightId);

            mark.appendChild(noteDisplay);
        }
        // -----------------------------------------------------

    } catch (e) {
        console.error(`ApplyHighlight Error (ID: ${highlightId}, Style: ${style}):`, { error: e });
    }
}


/**
 * Removes a highlight from data storage and the DOM.
 * Enhanced to handle cross-paragraph highlights with multiple segments.
 * @param {string} highlightId - The ID of the highlight to remove.
 * @param {HTMLElement} markElement - The <mark> element in the DOM.
 */
function removeHighlight(highlightId, markElement) {
    if (!highlightId || !markElement) return;

    // 1. Remove from data array
    const index = highlightsData.findIndex(h => h.id === highlightId);
    if (index !== -1) {
        highlightsData.splice(index, 1);
        saveHighlights(); // Save the change
        console.log(`Stickara: Removed highlight ${highlightId} from data.`);
    } else {
        console.warn(`Stickara: Highlight ID ${highlightId} not found in data for removal.`);
        // Proceed with DOM removal anyway, might be an orphaned mark
    }

    // 2. Remove from DOM - handle both single and cross-paragraph highlights
    try {
        // Check if this is a cross-paragraph highlight
        if (markElement.classList.contains('cross-paragraph-segment')) {
            // Find all segments of this cross-paragraph highlight
            const allSegments = document.querySelectorAll(`mark.${HIGHLIGHT_CLASS}[data-highlight-id="${highlightId}"]`);

            console.log(`Stickara: Removing cross-paragraph highlight with ${allSegments.length} segments for ID ${highlightId}`);

            // Remove all segments
            allSegments.forEach(segment => {
                removeSingleHighlightSegment(segment);
            });
        } else {
            // Single highlight segment
            removeSingleHighlightSegment(markElement);
        }

        console.log(`Stickara: Removed highlight ${highlightId} mark(s) from DOM.`);
    } catch (err) {
        console.error(`Stickara: Error removing highlight ${highlightId} mark from DOM:`, err);
        // Data was likely removed, but DOM state might be inconsistent.
    }
}

/**
 * Removes a single highlight segment from the DOM
 * @param {HTMLElement} markElement - The mark element to remove
 */
function removeSingleHighlightSegment(markElement) {
    if (!markElement || !markElement.parentNode) {
        return;
    }

    try {
        // Replace the mark element with its child nodes (the original content)
        // Filter out the delete button, note button, and note display before replacing
        const childNodes = Array.from(markElement.childNodes).filter(node =>
            !node.classList ||
            (!node.classList.contains('Stickara-delete-highlight-btn') &&
             !node.classList.contains('Stickara-note-highlight-btn') &&
             !node.classList.contains('Stickara-highlight-note-display'))
        );

        const parent = markElement.parentNode;
        markElement.replaceWith(...childNodes);

        // Normalize adjacent text nodes
        if (parent) {
            parent.normalize();
        }
    } catch (err) {
        console.error("Stickara: Error removing single highlight segment:", err);
        // Fallback: just remove the element
        markElement.remove();
    }
}


/**
 * Saves the current `highlightsData` array to chrome.storage.local.
 */
function saveHighlights() {
    // Check if highlightKey is defined (should be by the time this runs)
    if (typeof highlightKey === 'undefined') {
        console.error("saveHighlights Error: highlightKey is not defined. Cannot save."); return;
    }
    if (!Array.isArray(highlightsData)) {
        console.error("!!! saveHighlights FATAL: highlightsData is NOT an array!", highlightsData);
        highlightsData = []; // Attempt to recover by resetting
    }
    // Create a deep copy to avoid potential issues with ongoing modifications
    const dataToSave = JSON.parse(JSON.stringify(highlightsData));
    // console.log(`>>> saveHighlights: Saving ${dataToSave.length} highlights to key '${highlightKey}'.`); // Less noisy
    chrome.storage.local.set({ [highlightKey]: dataToSave }, () => {
        if (chrome.runtime.lastError) {
            console.error("saveHighlights: Error saving highlights locally:", highlightKey, chrome.runtime.lastError.message);
        }
        // else console.log("saveHighlights: Highlights saved successfully."); // Less noisy
    });
}

/**
 * Loads highlight data from storage and applies the highlights to the page.
 * Calls the modified applyHighlight which adds the delete button.
 */
function loadAndApplyHighlights() {
    console.log("Stickara: loadAndApplyHighlights Starting...");

    // Temporarily disable threat detection during highlight loading to prevent false positives
    const threatDetectionWasActive = window.StickaraThreatDetection && window.StickaraThreatDetection.isActive();
    if (threatDetectionWasActive) {
        window.StickaraThreatDetection.pause();
        console.log("Stickara: Temporarily paused threat detection for highlight loading");
    }

    // Also pause threat detection for cross-paragraph deserialization
    if (!threatDetectionWasActive && window.StickaraThreatDetection) {
        window.StickaraThreatDetection.pause();
        console.log("Stickara: Paused threat detection for cross-paragraph highlight restoration");
    }

    // 1. Clear existing marks from *this extension* only (including cross-paragraph segments)
    let clearedCount = 0;
    document.querySelectorAll(`mark.${HIGHLIGHT_CLASS}`).forEach(mark => {
        removeSingleHighlightSegment(mark);
        clearedCount++;
    });
    console.log(`Stickara: Cleared ${clearedCount} existing Stickara marks.`);

    if (typeof highlightKey === 'undefined') {
        console.error("Stickara: highlightKey not defined. Cannot load highlights.");
        highlightsData = []; return;
    }

    // 2. Load data from local storage
    chrome.storage.local.get([highlightKey], (result) => {
        if (chrome.runtime.lastError) {
            console.error("Stickara: Error loading highlights:", highlightKey, chrome.runtime.lastError.message);
            highlightsData = []; return;
        }

        let loadedData = result[highlightKey];
        // Validate loaded data structure
        if (typeof loadedData === 'undefined' || loadedData === null) {
            console.log("Stickara: No highlights found in storage for this page.");
            highlightsData = [];
        } else if (!Array.isArray(loadedData)) {
            console.warn("Stickara: Loaded highlight data is NOT an array! Resetting.", loadedData);
            highlightsData = [];
            chrome.storage.local.remove(highlightKey); // Clear invalid data from storage
        } else {
            highlightsData = loadedData; // Assign loaded data to cache
        }

        console.log(`Stickara: Processing ${highlightsData.length} highlights loaded from storage for key ${highlightKey}.`);

        // 3. Deserialize and apply highlights
        let appliedCount = 0;
        let failedCount = 0;
        const validHighlightsApplied = []; // Keep track of successfully applied highlights
        let needsSave = false; // Flag if data was modified (ID generated, color/style defaulted)

        highlightsData.forEach((data, index) => {
            // Basic data validation
            if (!data || typeof data.text !== 'string' || typeof data.prefix !== 'string' || typeof data.suffix !== 'string') {
                console.warn(`   [INVALID DATA] Skipping highlight index ${index}, invalid data structure:`, data);
                failedCount++;
                return; // Skip this item
            }

            // Ensure each highlight has a unique ID, generate if missing
            if (!data.id) {
                 data.id = `hl-${Date.now()}-${Math.random().toString(16).slice(2)}`;
                 console.warn(`   Highlight index ${index} was missing an ID. Generated: ${data.id}`);
                 needsSave = true; // Mark that data needs saving
             }

             const highlightStyle = data.style || 'color'; // Default old data to 'color'
             let highlightColor = data.color;

             // --- Data migration/correction ---
             if (highlightStyle === 'color' && !highlightColor) {
                 highlightColor = DEFAULT_HIGHLIGHT_COLOR; // Default missing color if style is color
                 data.color = highlightColor; // Update data object
                 needsSave = true;
             }
             if (highlightStyle !== 'color' && highlightColor) {
                 highlightColor = null; // Ensure color is null if style isn't color
                 data.color = null; // Update data object
                 needsSave = true;
             }
             if (!data.style) {
                 needsSave = true; // Mark for save if style was missing
             }
             data.style = highlightStyle; // Ensure style is set in data object
             // --- End migration ---

            // console.log(`--- Processing Highlight ${index + 1} / ${highlightsData.length} (ID: ${data.id}) ---`); // Less noisy
            const range = deserializeRangeByContext(data);

            if (range) {
                try {
                    // Pass style, color, and hasLinkedNote flag
                    applyHighlight(range, data.id, highlightColor, highlightStyle, data.hasLinkedNote);

                    // Handle snippet prefixes if this is a snippet highlight
                    if (data.snippetType) {
                        const highlightElement = document.querySelector(`mark.Stickara-highlight[data-highlight-id="${data.id}"]`);
                        if (highlightElement) {
                            // Create a prefix element
                            const prefixElement = document.createElement('span');
                            prefixElement.className = 'Stickara-snippet-prefix';

                            // Set prefix text and color based on snippet type
                            if (data.snippetType === 'review') {
                                const reviewType = data.reviewType || 'pro'; // Default to pro if not specified
                                const prefix = reviewType.toLowerCase() === 'pro' ? '[Review Pro]: ' : '[Review Con]: ';
                                prefixElement.textContent = prefix;
                                prefixElement.style.fontWeight = 'bold';
                                prefixElement.style.color = reviewType.toLowerCase() === 'pro' ? '#4CAF50' : '#F44336'; // Green for pro, red for con
                            } else if (data.snippetType === 'spec') {
                                prefixElement.textContent = '[Spec]: ';
                                prefixElement.style.fontWeight = 'bold';
                                prefixElement.style.color = '#2196F3'; // Blue for spec
                            }

                            // Insert the prefix at the beginning of the highlight
                            highlightElement.insertBefore(prefixElement, highlightElement.firstChild);
                        }
                    }

                    appliedCount++;
                    validHighlightsApplied.push(data); // Add to list of successful ones
                } catch (applyError) {
                    console.error(`   [FAILED APPLY] Error applying highlight ID ${data.id}:`, applyError);
                    failedCount++;
                }
            } else {
                failedCount++;
                // Failure reason is logged inside deserializeRangeByContext now
            }
        });
        console.log(`Stickara: Finished Applying Highlights. Applied=${appliedCount}, Failed/Skipped=${failedCount}.`);

        // Show UI feedback if any highlights failed to restore
        if (failedCount > 0) {
            // Create a more detailed warning message at the top of the page
            showHighlightRestorationWarning(failedCount, appliedCount, highlightKey);
        }

        // 4. Update cache/storage if any highlights failed to load OR if data was modified
        if (failedCount > 0 || needsSave) {
            // Only update/save if the list of successful highlights differs from the original list OR if modifications were made
            if (highlightsData.length !== validHighlightsApplied.length || needsSave) {
                 console.log(`Stickara: Updating cache/storage, keeping ${validHighlightsApplied.length} valid highlights (Reasons: Failed=${failedCount > 0}, Modified=${needsSave}).`);
                 highlightsData = validHighlightsApplied; // Update the in-memory cache
                 saveHighlights(); // Save the cleaned/updated list back to storage
            }
        }

        // Re-enable threat detection after highlight loading is complete
        if (window.StickaraThreatDetection) {
            setTimeout(() => {
                window.StickaraThreatDetection.resume();
                console.log("Stickara: Resumed threat detection after highlight loading");
            }, 2000); // Wait 2 seconds to ensure all highlights are applied and DOM is stable
        }
    });
}


/**
 * Handles context menu request OR palette button click to highlight selection.
 * Calls the modified applyHighlight.
 * @param {string|null} color - The color name, or null/undefined if style is not 'color'.
 * @param {string} style - The style name (e.g., 'color', 'underline', 'wavy', 'border-thick').
 */
function handleHighlightSelection(color, style) {
    if (!style) {
        console.error("Stickara: handleHighlightSelection called without a style.");
        return;
    }

    // Pause threat detection immediately to prevent false positives during highlighting
    if (window.StickaraThreatDetection) {
        window.StickaraThreatDetection.pause();
        console.log("Stickara: Paused threat detection for highlighting operation");
    }

    const selection = window.getSelection();
    if (!selection || selection.isCollapsed || !selection.rangeCount) {
         console.log("Stickara: Highlight cancelled - no selection.");
         // Resume threat detection before returning
         if (window.StickaraThreatDetection) {
             setTimeout(() => {
                 window.StickaraThreatDetection.resume();
                 console.log("Stickara: Resumed threat detection after cancelled highlight");
             }, 500);
         }
         return;
     }
     // Prevent highlighting within Stickara's own UI elements
     const container = selection.anchorNode?.parentElement;
     if (container?.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
         // Highlight cancelled - selection is inside Stickara UI
         selection.collapseToEnd(); // Clear selection visually
         // Resume threat detection before returning
         if (window.StickaraThreatDetection) {
             setTimeout(() => {
                 window.StickaraThreatDetection.resume();
                 // Resumed threat detection after cancelled highlight
             }, 500);
         }
         return;
     }

    const range = selection.getRangeAt(0);

    // Detect if this is a cross-paragraph highlight
    const startContainer = range.startContainer;
    const endContainer = range.endContainer;
    const isCrossParagraph = startContainer !== endContainer ||
                           (startContainer.nodeType !== Node.TEXT_NODE || endContainer.nodeType !== Node.TEXT_NODE);

    const serialized = serializeRangeWithContext(range, isCrossParagraph); // Use context serialization

    if (serialized) {
        serialized.id = `hl-${Date.now()}-${Math.random().toString(16).slice(2)}`;
        serialized.style = style;
        // Store color ONLY if style is 'color', otherwise null
        serialized.color = (style === 'color' && color) ? color : null;

        if (!Array.isArray(highlightsData)) {
             console.warn("Stickara: highlightsData was not an array, resetting.");
             highlightsData = [];
         }
        // Simple check for exact duplicate text+context
        const isDuplicate = highlightsData.some(h =>
            h.text === serialized.text &&
            h.prefix === serialized.prefix &&
            h.suffix === serialized.suffix
        );

        if (!isDuplicate) {
            highlightsData.push(serialized);
            saveHighlights();
            // Pass potentially null color
            applyHighlight(range, serialized.id, serialized.color, serialized.style);
            selection.collapseToEnd();
            console.log(`Stickara: Highlight added (Style: ${style}${serialized.color ? ', Color: ' + serialized.color : ''}). ID:`, serialized.id);
        } else {
             console.log("Stickara: Highlight text/context already exists.");
             // Find existing highlight ID to apply the *new* style
             const existing = highlightsData.find(h => h.text === serialized.text && h.prefix === serialized.prefix && h.suffix === serialized.suffix);
             if (existing) {
                 // Update existing highlight's style and clear color if needed
                 const newColor = (style === 'color' && color) ? color : null;
                 if (existing.style !== style || existing.color !== newColor) {
                     existing.style = style;
                     existing.color = newColor;
                     needsSave = true; // Mark for saving
                     saveHighlights(); // Save the updated style/color
                 }
                 // Pass potentially null color
                 applyHighlight(range, existing.id, newColor, style);
             } else {
                // This case shouldn't ideally happen if isDuplicate is true, but handle defensively
                highlightsData.push(serialized); // Add as new if somehow not found
                saveHighlights();
                applyHighlight(range, serialized.id, newColor, style);
             }
             selection.collapseToEnd(); // Clear selection visually
        }
    } else {
        console.warn("Stickara: Failed to serialize range for highlighting (Context). Range was:", range.toString());
        selection.collapseToEnd(); // Clear selection visually
    }

    // Resume threat detection after highlighting operation is complete
    if (window.StickaraThreatDetection) {
        setTimeout(() => {
            window.StickaraThreatDetection.resume();
            console.log("Stickara: Resumed threat detection after highlighting operation");
        }, 1000); // Wait 1 second to ensure DOM operations are complete
    }
}

/**
 * Shows a detailed warning at the top of the page when highlights cannot be restored.
 * @param {number} failedCount - Number of highlights that failed to restore
 * @param {number} appliedCount - Number of highlights that were successfully applied
 * @param {string} highlightKey - The storage key for the highlights
 */
function showHighlightRestorationWarning(failedCount, appliedCount, highlightKey) {
    // Remove any existing warning
    const existingWarning = document.getElementById('Stickara-highlight-warning');
    if (existingWarning) {
        existingWarning.remove();
    }

    // Create the warning container
    const warningContainer = document.createElement('div');
    warningContainer.id = 'Stickara-highlight-warning';
    warningContainer.style.position = 'fixed';
    warningContainer.style.top = '0';
    warningContainer.style.left = '0';
    warningContainer.style.right = '0';
    warningContainer.style.backgroundColor = 'rgba(255, 204, 0, 0.95)';
    warningContainer.style.color = '#333';
    warningContainer.style.padding = '10px 20px';
    warningContainer.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
    warningContainer.style.zIndex = '2147483646'; // Very high but below max
    warningContainer.style.fontSize = '14px';
    warningContainer.style.fontFamily = 'Arial, sans-serif';
    warningContainer.style.display = 'flex';
    warningContainer.style.justifyContent = 'space-between';
    warningContainer.style.alignItems = 'center';
    warningContainer.style.opacity = '0'; // Start invisible for fade-in
    warningContainer.style.transition = 'opacity 0.3s ease';
    // Add some margin to push page content down
    document.body.style.marginTop = document.body.style.marginTop ?
        (parseInt(document.body.style.marginTop) + 80) + 'px' :
        '80px';

    // Create the warning message
    const messageDiv = document.createElement('div');

    // Create the title with icon
    const titleSpan = document.createElement('span');
    titleSpan.style.fontWeight = 'bold';
    titleSpan.style.fontSize = '16px';
    titleSpan.style.display = 'block';
    titleSpan.style.marginBottom = '5px';
    titleSpan.innerHTML = '⚠️ Highlight Restoration Warning';
    messageDiv.appendChild(titleSpan);

    // Create the main message
    const totalHighlights = failedCount + appliedCount;
    const messageSpan = document.createElement('span');
    messageSpan.innerHTML = `${failedCount} of ${totalHighlights} highlight${totalHighlights > 1 ? 's' : ''} could not be restored due to page changes.<br>`;

    // Add details about the page
    let pageUrl = highlightKey.replace('Stickara_highlights_', '');
    try {
        // Only decode if it looks like valid percent-encoding
        if (pageUrl.includes('%')) {
            const validPercentPattern = /^[^%]*(%[0-9A-Fa-f]{2})*[^%]*$/;
            if (validPercentPattern.test(pageUrl)) {
                pageUrl = decodeURIComponent(pageUrl);
            } else {
                console.warn(`Invalid percent-encoding pattern in highlight key: ${pageUrl}`);
                // Keep original if invalid pattern
            }
        }
    } catch (e) {
        console.warn(`Failed to decode highlight key URL: ${e.message}`);
        // Keep original if decoding fails
    }
    const urlSpan = document.createElement('span');
    urlSpan.style.fontSize = '12px';
    urlSpan.style.color = '#555';
    urlSpan.innerHTML = `This may happen when the page content has been updated since the highlights were created.<br>`;
    urlSpan.innerHTML += `Page URL: ${pageUrl.substring(0, 50)}${pageUrl.length > 50 ? '...' : ''}`;

    messageSpan.appendChild(urlSpan);
    messageDiv.appendChild(messageSpan);
    warningContainer.appendChild(messageDiv);

    // Create close button
    const closeButton = document.createElement('button');
    closeButton.innerHTML = '×';
    closeButton.style.background = 'none';
    closeButton.style.border = 'none';
    closeButton.style.fontSize = '24px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.color = '#333';
    closeButton.style.marginLeft = '15px';
    closeButton.style.padding = '0 5px';
    closeButton.title = 'Dismiss';
    closeButton.setAttribute('aria-label', 'Dismiss highlight warning');
    closeButton.addEventListener('click', () => {
        warningContainer.style.opacity = '0';
        // Restore body margin
        if (document.body.style.marginTop) {
            document.body.style.marginTop = Math.max(0, parseInt(document.body.style.marginTop) - 80) + 'px';
        }
        setTimeout(() => {
            if (warningContainer.parentNode) {
                warningContainer.parentNode.removeChild(warningContainer);
            }
        }, 300);
    });
    warningContainer.appendChild(closeButton);

    // Add to the document
    document.body.appendChild(warningContainer);

    // Auto-dismiss after 10 seconds
    setTimeout(() => {
        warningContainer.style.opacity = '0';
        // Restore body margin
        if (document.body.style.marginTop) {
            document.body.style.marginTop = Math.max(0, parseInt(document.body.style.marginTop) - 80) + 'px';
        }
        setTimeout(() => {
            if (warningContainer.parentNode) {
                warningContainer.parentNode.removeChild(warningContainer);
            }
        }, 300);
    }, 10000);

    // Fade in the warning after a short delay
    setTimeout(() => {
        warningContainer.style.opacity = '1';
    }, 10);
}

/**
 * Handles context menu request to add selection to note.
 */
function handleNoteFromSelection() {
     const selection = window.getSelection();
    if (!selection || selection.isCollapsed) return;
    // Prevent adding from Stickara's own UI elements
     const container = selection.anchorNode?.parentElement;
     if (container?.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
          console.log("Stickara: Add to Note cancelled - selection is inside Stickara UI.");
         return;
     }
    const selectedText = selection.toString();
    if (!selectedText.trim()) return; // Ignore whitespace-only selections

    // Ensure note UI exists and is visible before adding text
    if (!noteContainer || !noteContainer.classList.contains('visible')) {
        showNote(); // Defined in interactions.js
        // Wait a short moment for the UI to become visible before adding text
        setTimeout(() => addTextToNote(selectedText), 150);
    } else {
         addTextToNote(selectedText); // Add immediately if already visible
    }
    selection.collapseToEnd(); // Clear selection visually
}

/**
 * Appends text to the note editor, wrapping it in a blockquote.
 * @param {string} text - The text to add to the note
 * @param {string|null} [highlightId=null] - Optional highlight ID to link the note to
 */
function addTextToNote(text, highlightId = null) {
    if (!noteText) return;
    // Escape HTML characters within the text itself to prevent XSS or broken HTML
    // Use the global escapeHtml function for consistent security
    const escapedText = window.escapeHtml(text);

    // Create the HTML to insert
    let htmlToInsert = '';

    // If this note is linked to a highlight, add a special class and data attribute
    // but don't add the "Linked to highlight" header
    if (highlightId) {
        htmlToInsert = `<blockquote class="Stickara-linked-note" data-highlight-id="${highlightId}">
            ${escapedText.replace(/\n/g, '<br>')}
        </blockquote><br>`;
    } else {
        // Regular blockquote for normal notes
        htmlToInsert = `<blockquote>${escapedText.replace(/\n/g, '<br>')}</blockquote><br>`;
    }

    insertHtmlAtCursor(htmlToInsert); // Utility function inserts and calls scheduleSave
    // Scroll to the bottom of the note text area after insertion
    noteText.scrollTop = noteText.scrollHeight;
    noteText.focus(); // Keep focus in the editor
}

/**
 * Appends formatted text to the note editor without wrapping in a blockquote.
 * This version allows HTML formatting to be preserved (for snippet prefixes).
 * @param {string} formattedText - The formatted HTML text to add to the note
 * @param {string|null} [highlightId=null] - Optional highlight ID to link the note to
 */
function addFormattedTextToNote(formattedText, highlightId = null) {
    if (!noteText) return;

    // Determine snippet type
    let snippetType = 'normal';
    if (formattedText.includes('👍 Pro:') || formattedText.includes('color: #4CAF50')) {
        snippetType = 'pro';
    } else if (formattedText.includes('👎 Con:') || formattedText.includes('color: #F44336')) {
        snippetType = 'con';
    } else if (formattedText.includes('📋 Spec:') || formattedText.includes('color: #2196F3')) {
        snippetType = 'spec';
    }

    // Create the HTML to insert - no blockquote, just a div with appropriate styling
    let htmlToInsert = '';

    // If this note is linked to a highlight, add a special class and data attribute
    if (highlightId) {
        htmlToInsert = `<div class="Stickara-snippet-note" data-highlight-id="${highlightId}" data-snippet-type="${snippetType}">
            ${formattedText.replace(/\n/g, '<br>')}
        </div><br>`;
    } else {
        // Regular div for normal notes with snippet type if applicable
        htmlToInsert = `<div class="Stickara-snippet-note" ${snippetType !== 'normal' ? `data-snippet-type="${snippetType}"` : ''}>
            ${formattedText.replace(/\n/g, '<br>')}
        </div><br>`;
    }

    insertHtmlAtCursor(htmlToInsert); // Utility function inserts and calls scheduleSave
    // Scroll to the bottom of the note text area after insertion
    noteText.scrollTop = noteText.scrollHeight;
    noteText.focus(); // Keep focus in the editor

    // Check if we need to create a Pro/Con comparison view
    if ((snippetType === 'pro' || snippetType === 'con') && typeof checkForProConSnippets === 'function') {
        console.log(`Stickara: Added a ${snippetType} snippet, checking for comparison view`);
        setTimeout(checkForProConSnippets, 300);
    }
}

/**
 * Handles the "Highlight with Note" context menu action.
 * This creates a highlight and then adds a linked note.
 * @param {string|null} color - The color name, or null/undefined if style is not 'color'.
 * @param {string} style - The style name (e.g., 'color', 'underline', 'wavy', 'border-thick').
 */
function handleHighlightWithNote(color, style) {
    // Pause threat detection immediately to prevent false positives during highlighting
    if (window.StickaraThreatDetection) {
        window.StickaraThreatDetection.pause();
        console.log("Stickara: Paused threat detection for highlight with note operation");
    }

    const selection = window.getSelection();
    if (!selection || selection.isCollapsed) {
        // Resume threat detection before returning
        if (window.StickaraThreatDetection) {
            setTimeout(() => {
                window.StickaraThreatDetection.resume();
                console.log("Stickara: Resumed threat detection after cancelled highlight with note");
            }, 500);
        }
        return;
    }

    // Prevent highlighting from Stickara's own UI elements
    const container = selection.anchorNode?.parentElement;
    if (container?.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
        // Highlight with Note cancelled - selection is inside Stickara UI
        // Resume threat detection before returning
        if (window.StickaraThreatDetection) {
            setTimeout(() => {
                window.StickaraThreatDetection.resume();
                // Resumed threat detection after cancelled highlight with note
            }, 500);
        }
        return;
    }

    const selectedText = selection.toString();
    if (!selectedText.trim()) return; // Ignore whitespace-only selections

    // Create a range from the selection
    const range = selection.getRangeAt(0);
    if (!range) return;

    // Serialize the range for storage and retrieval
    const serialized = serializeRange(range);
    if (!serialized) {
        console.warn("Stickara: Failed to serialize range for highlighting with note.");
        return;
    }

    // Generate a unique ID for this highlight
    const highlightId = `hl-${Date.now()}-${Math.random().toString(16).slice(2)}`;
    serialized.id = highlightId;
    serialized.style = style;
    serialized.color = (style === 'color' && color) ? color : null;
    serialized.hasLinkedNote = true; // Mark that this highlight has a linked note

    // Add the highlight to storage
    if (!Array.isArray(highlightsData)) {
        console.warn("Stickara: highlightsData was not an array, resetting.");
        highlightsData = [];
    }

    // Check for duplicates
    const isDuplicate = highlightsData.some(h =>
        h.text === serialized.text &&
        h.prefix === serialized.prefix &&
        h.suffix === serialized.suffix
    );

    if (!isDuplicate) {
        // Add the new highlight
        highlightsData.push(serialized);
        saveHighlights();

        // Apply the highlight to the page with the linked note flag
        applyHighlight(range, highlightId, serialized.color, serialized.style, true);

        // We no longer add the note to the Stickara as requested by the user
        // The note is only displayed on the highlight itself

        // Clear the selection
        selection.collapseToEnd();

        console.log(`Stickara: Highlight with Note added. ID: ${highlightId}`);
    } else {
        console.log("Stickara: Highlight text/context already exists. Adding note only.");
        // Find existing highlight to link the note to
        const existing = highlightsData.find(h =>
            h.text === serialized.text &&
            h.prefix === serialized.prefix &&
            h.suffix === serialized.suffix
        );

        if (existing) {
            // Mark the existing highlight as having a linked note
            existing.hasLinkedNote = true;
            saveHighlights();

            // We no longer add the note to the Stickara as requested by the user
            // The note is only displayed on the highlight itself

            // Clear the selection
            selection.collapseToEnd();
        }
    }

    // Resume threat detection after highlight with note operation is complete
    if (window.StickaraThreatDetection) {
        setTimeout(() => {
            window.StickaraThreatDetection.resume();
            console.log("Stickara: Resumed threat detection after highlight with note operation");
        }, 1000); // Wait 1 second to ensure DOM operations are complete
    }
}


/**
 * Shows a note editor for adding a note to a highlight
 * @param {string} highlightId - The ID of the highlight to add a note to
 * @param {string} highlightText - The text of the highlight
 */
function showNoteEditor(highlightId, highlightText) {
    if (!highlightId) {
        console.error("Stickara: Cannot show note editor - missing highlight ID");
        return;
    }

    // Create a modal overlay
    const overlay = document.createElement('div');
    overlay.className = 'Stickara-note-editor-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    overlay.style.zIndex = '10001';
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';
    overlay.style.opacity = '0';
    overlay.style.transition = 'opacity 0.2s ease';

    // Add click handler to close when clicking outside
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            document.body.removeChild(overlay);
        }
    });

    // Fade in the overlay after it's added to the DOM
    setTimeout(() => {
        overlay.style.opacity = '1';
    }, 10);

    // Create the editor container
    const editor = document.createElement('div');
    editor.className = 'Stickara-note-editor';
    editor.style.backgroundColor = '#fffef5'; // Slightly warmer background
    editor.style.borderRadius = '12px';
    editor.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2), 0 0 2px rgba(0, 0, 0, 0.3)';
    editor.style.width = '450px';
    editor.style.maxWidth = '90%';
    editor.style.maxHeight = '80%';
    editor.style.display = 'flex';
    editor.style.flexDirection = 'column';
    editor.style.overflow = 'hidden';
    editor.style.animation = 'Stickara-note-editor-fade-in 0.3s ease-out';
    editor.style.border = '1px solid #ffd54f';

    // Create the header
    const header = document.createElement('div');
    header.style.padding = '16px 20px';
    header.style.borderBottom = '1px solid #ffd54f';
    header.style.display = 'flex';
    header.style.justifyContent = 'space-between';
    header.style.alignItems = 'center';
    header.style.backgroundColor = '#fff9c4';

    const title = document.createElement('h3');

    // FIXED: Show different title for editing vs creating new note
    const existingHighlight = highlightsData.find(h => h.id === highlightId);
    const isEditing = existingHighlight && existingHighlight.noteText;
    title.innerHTML = isEditing ? '✏️ Edit Note' : '📝 Add Note to Highlight';

    title.style.margin = '0';
    title.style.fontSize = '18px';
    title.style.fontWeight = '600';
    title.style.color = '#111827';

    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '×';
    closeBtn.style.background = 'none';
    closeBtn.style.border = 'none';
    closeBtn.style.fontSize = '24px';
    closeBtn.style.cursor = 'pointer';
    closeBtn.style.padding = '0';
    closeBtn.style.width = '32px';
    closeBtn.style.height = '32px';
    closeBtn.style.display = 'flex';
    closeBtn.style.alignItems = 'center';
    closeBtn.style.justifyContent = 'center';
    closeBtn.style.borderRadius = '50%';
    closeBtn.style.transition = 'background-color 0.2s ease';
    closeBtn.style.color = '#4b5563';

    // Add hover effect
    closeBtn.addEventListener('mouseover', () => {
        closeBtn.style.backgroundColor = '#f3f4f6';
        closeBtn.style.color = '#111827';
    });
    closeBtn.addEventListener('mouseout', () => {
        closeBtn.style.backgroundColor = 'transparent';
        closeBtn.style.color = '#4b5563';
    });

    closeBtn.addEventListener('click', () => {
        document.body.removeChild(overlay);
    });

    header.appendChild(title);
    header.appendChild(closeBtn);

    // Create the content area
    const content = document.createElement('div');
    content.style.padding = '20px';
    content.style.overflowY = 'auto';

    // Add a label for the highlighted text
    const previewLabel = document.createElement('div');
    previewLabel.textContent = 'Highlighted Text:';
    previewLabel.style.fontSize = '14px';
    previewLabel.style.fontWeight = '500';
    previewLabel.style.color = '#4b5563';
    previewLabel.style.marginBottom = '6px';

    // Show the highlighted text
    const highlightPreview = document.createElement('div');
    highlightPreview.style.padding = '12px 16px';
    highlightPreview.style.backgroundColor = '#fff9c4';
    highlightPreview.style.border = '1px solid #ffd54f';
    highlightPreview.style.borderRadius = '6px';
    highlightPreview.style.marginBottom = '16px';
    highlightPreview.style.fontSize = '15px';
    highlightPreview.style.color = '#333';
    highlightPreview.style.fontStyle = 'normal';
    highlightPreview.style.lineHeight = '1.5';
    highlightPreview.style.maxHeight = '100px';
    highlightPreview.style.overflowY = 'auto';
    highlightPreview.textContent = highlightText || 'Selected text';

    // Add a label for the note textarea
    const noteLabel = document.createElement('div');
    noteLabel.textContent = 'Your Note:';
    noteLabel.style.fontSize = '14px';
    noteLabel.style.fontWeight = '500';
    noteLabel.style.color = '#4b5563';
    noteLabel.style.marginBottom = '6px';

    // Create the textarea for the note
    const textarea = document.createElement('textarea');
    textarea.placeholder = 'Write your note here...';
    textarea.style.width = '100%';
    textarea.style.minHeight = '150px';
    textarea.style.padding = '12px 16px';
    textarea.style.borderRadius = '6px';
    textarea.style.border = '1px solid #d1d5db';
    textarea.style.resize = 'vertical';
    textarea.style.fontSize = '15px';
    textarea.style.lineHeight = '1.5';
    textarea.style.boxSizing = 'border-box';
    textarea.style.fontFamily = 'inherit';
    textarea.style.transition = 'border-color 0.2s ease, box-shadow 0.2s ease';

    // FIXED: Pre-fill textarea with existing note text if editing
    if (existingHighlight && existingHighlight.noteText) {
        textarea.value = existingHighlight.noteText;
        console.log("Stickara: Pre-filled note editor with existing text:", existingHighlight.noteText);
    }

    // Create character counter
    const charCounter = document.createElement('div');
    charCounter.style.fontSize = '12px';
    charCounter.style.color = '#6b7280';
    charCounter.style.textAlign = 'right';
    charCounter.style.marginTop = '4px';
    charCounter.style.paddingRight = '4px';

    // FIXED: Initialize character counter with existing text length
    const initialCount = textarea.value.length;
    charCounter.textContent = `${initialCount} character${initialCount !== 1 ? 's' : ''}`;
    if (initialCount > 200) {
        charCounter.style.color = '#f59e0b';
    }

    // Update character counter on input
    textarea.addEventListener('input', () => {
        const count = textarea.value.length;
        charCounter.textContent = `${count} character${count !== 1 ? 's' : ''}`;

        // Change color if getting long
        if (count > 200) {
            charCounter.style.color = '#f59e0b';
        } else {
            charCounter.style.color = '#6b7280';
        }
    });

    // Add focus effect
    textarea.addEventListener('focus', () => {
        textarea.style.outline = 'none';
        textarea.style.borderColor = '#3b82f6';
        textarea.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.2)';
    });

    textarea.addEventListener('blur', () => {
        textarea.style.boxShadow = 'none';
        textarea.style.borderColor = '#d1d5db';
    });

    // Add all elements to content area
    content.appendChild(previewLabel);
    content.appendChild(highlightPreview);
    content.appendChild(noteLabel);
    content.appendChild(textarea);
    content.appendChild(charCounter);

    // Create the footer with action buttons
    const footer = document.createElement('div');
    footer.style.padding = '16px 20px';
    footer.style.borderTop = '1px solid #ffd54f';
    footer.style.display = 'flex';
    footer.style.justifyContent = 'flex-end';
    footer.style.gap = '12px';
    footer.style.backgroundColor = '#fff9c4';

    const cancelBtn = document.createElement('button');
    cancelBtn.textContent = 'Cancel';
    cancelBtn.style.padding = '8px 16px';
    cancelBtn.style.borderRadius = '6px';
    cancelBtn.style.border = '1px solid #d1d5db';
    cancelBtn.style.backgroundColor = '#ffffff';
    cancelBtn.style.color = '#4b5563';
    cancelBtn.style.fontSize = '14px';
    cancelBtn.style.fontWeight = '500';
    cancelBtn.style.cursor = 'pointer';
    cancelBtn.style.transition = 'all 0.2s ease';

    // Add hover effect
    cancelBtn.addEventListener('mouseover', () => {
        cancelBtn.style.backgroundColor = '#f3f4f6';
        cancelBtn.style.borderColor = '#9ca3af';
    });

    cancelBtn.addEventListener('mouseout', () => {
        cancelBtn.style.backgroundColor = '#ffffff';
        cancelBtn.style.borderColor = '#d1d5db';
    });

    cancelBtn.addEventListener('click', () => {
        document.body.removeChild(overlay);
    });

    const saveBtn = document.createElement('button');

    // FIXED: Show different button text for editing vs creating
    saveBtn.innerHTML = isEditing ? '✏️ Update Note' : '📝 Save Note';

    saveBtn.style.padding = '8px 16px';
    saveBtn.style.borderRadius = '6px';
    saveBtn.style.border = '1px solid #f57c00';
    saveBtn.style.backgroundColor = '#ffb74d';
    saveBtn.style.color = '#333';
    saveBtn.style.fontSize = '14px';
    saveBtn.style.fontWeight = '600';
    saveBtn.style.cursor = 'pointer';
    saveBtn.style.transition = 'all 0.2s ease';

    // Add hover effect
    saveBtn.addEventListener('mouseover', () => {
        saveBtn.style.backgroundColor = '#ffa726';
        saveBtn.style.transform = 'translateY(-1px)';
        saveBtn.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
    });

    saveBtn.addEventListener('mouseout', () => {
        saveBtn.style.backgroundColor = '#ffb74d';
        saveBtn.style.transform = 'translateY(0)';
        saveBtn.style.boxShadow = 'none';
    });
    saveBtn.addEventListener('click', () => {
        const noteText = textarea.value.trim();
        if (noteText) {
            // Find the highlight in the data
            const highlight = highlightsData.find(h => h.id === highlightId);
            if (highlight) {
                // Mark the highlight as having a linked note
                highlight.hasLinkedNote = true;
                saveHighlights();

                // Store the note text with the highlight
                highlight.noteText = noteText;

                // Store position if it exists
                if (highlight.notePosition) {
                    // Keep existing position
                } else {
                    // Default position (below the highlight)
                    highlight.notePosition = {
                        top: '100%',
                        left: '0',
                        marginTop: '6px'
                    };
                }

                saveHighlights();

                // Add a visible note to the highlight
                const highlightElement = document.querySelector(`mark.Stickara-highlight[data-highlight-id="${highlightId}"]`);
                if (highlightElement) {
                    // Add or update the note display
                    let noteDisplay = highlightElement.querySelector('.Stickara-highlight-note-display');
                    if (!noteDisplay) {
                        noteDisplay = document.createElement('div');
                        noteDisplay.className = 'Stickara-highlight-note-display';

                        // Add drag handle
                        const dragHandle = document.createElement('div');
                        dragHandle.className = 'Stickara-note-drag-handle';
                        noteDisplay.appendChild(dragHandle);

                        // Apply stored position if available
                        if (highlight.notePosition) {
                            noteDisplay.style.top = highlight.notePosition.top;
                            noteDisplay.style.left = highlight.notePosition.left;
                            if (highlight.notePosition.marginTop) {
                                noteDisplay.style.marginTop = highlight.notePosition.marginTop;
                            }
                        }

                        // Make the note draggable
                        makeDraggable(noteDisplay, highlightElement, highlightId);

                        highlightElement.appendChild(noteDisplay);
                    }
                    noteDisplay.textContent = noteText;

                    // Re-add the drag handle after setting text content
                    if (!noteDisplay.querySelector('.Stickara-note-drag-handle')) {
                        const dragHandle = document.createElement('div');
                        dragHandle.className = 'Stickara-note-drag-handle';
                        noteDisplay.insertBefore(dragHandle, noteDisplay.firstChild);
                    }
                }

                // We no longer add the note to the Stickara as requested by the user
                // The note is only displayed on the highlight itself
            }
        }
        document.body.removeChild(overlay);
    });

    footer.appendChild(cancelBtn);
    footer.appendChild(saveBtn);

    // Assemble the editor
    editor.appendChild(header);
    editor.appendChild(content);
    editor.appendChild(footer);

    overlay.appendChild(editor);
    document.body.appendChild(overlay);

    // Focus the textarea
    setTimeout(() => {
        textarea.focus();
    }, 100);
}



/**
 * Makes an element draggable
 * @param {HTMLElement} element - The element to make draggable
 * @param {HTMLElement} container - The container element (highlight)
 * @param {string} highlightId - The ID of the highlight
 */
function makeDraggable(element, container, highlightId) {
    let isDragging = false;
    let startX, startY;
    let startLeft, startTop;

    // Function to handle mouse down event
    function handleMouseDown(e) {
        // Only start drag on left mouse button
        if (e.button !== 0) return;

        e.preventDefault();
        e.stopPropagation();

        // Get initial positions
        startX = e.clientX;
        startY = e.clientY;

        // Get the current position of the element
        const rect = element.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        // Calculate the position relative to the container
        startLeft = rect.left - containerRect.left;
        startTop = rect.top - containerRect.top;

        // Create a ghost element to show the original position
        const ghost = document.createElement('div');
        ghost.className = 'Stickara-highlight-note-ghost';
        ghost.style.position = 'absolute';
        ghost.style.top = element.style.top || `${startTop}px`;
        ghost.style.left = element.style.left || `${startLeft}px`;
        ghost.style.width = `${rect.width}px`;
        ghost.style.height = `${rect.height}px`;
        ghost.style.borderRadius = '8px';
        ghost.style.border = '1px dashed #ffd54f';
        ghost.style.backgroundColor = 'rgba(255, 249, 196, 0.3)';
        ghost.style.zIndex = '9998';
        ghost.style.pointerEvents = 'none';
        container.appendChild(ghost);

        // Store the ghost element for later removal
        element._ghost = ghost;

        // Add dragging class
        element.classList.add('dragging');

        // Add event listeners for mouse move and mouse up
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    }

    // Function to handle mouse move event
    function handleMouseMove(e) {
        if (!isDragging) {
            isDragging = true;

            // Add a visual guide for dragging
            const guide = document.createElement('div');
            guide.className = 'Stickara-drag-guide';
            guide.style.position = 'fixed';
            guide.style.top = '10px';
            guide.style.left = '50%';
            guide.style.transform = 'translateX(-50%)';
            guide.style.padding = '8px 16px';
            guide.style.backgroundColor = 'rgba(255, 213, 79, 0.9)';
            guide.style.color = '#333';
            guide.style.borderRadius = '20px';
            guide.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.2)';
            guide.style.fontFamily = 'Arial, sans-serif';
            guide.style.fontSize = '14px';
            guide.style.fontWeight = 'bold';
            guide.style.zIndex = '10001';
            guide.style.pointerEvents = 'none';
            guide.style.opacity = '0';
            guide.style.transition = 'opacity 0.3s ease';
            guide.textContent = '✨ Drag to position your note ✨';
            document.body.appendChild(guide);

            // Store the guide element for later removal
            element._guide = guide;

            // Fade in the guide
            setTimeout(() => {
                if (guide.parentNode) {
                    guide.style.opacity = '1';
                }
            }, 10);
        }

        e.preventDefault();

        // Calculate the new position
        const dx = e.clientX - startX;
        const dy = e.clientY - startY;

        // Update the element's position
        element.style.left = `${startLeft + dx}px`;
        element.style.top = `${startTop + dy}px`;

        // Remove margin-top when dragging
        element.style.marginTop = '0';
    }

    // Function to handle mouse up event
    function handleMouseUp() {
        // Remove dragging class
        element.classList.add('drop-animation');
        element.classList.remove('dragging');

        // Remove the ghost element if it exists
        if (element._ghost && element._ghost.parentNode) {
            element._ghost.parentNode.removeChild(element._ghost);
            element._ghost = null;
        }

        // Remove the guide element if it exists
        if (element._guide && element._guide.parentNode) {
            // Fade out the guide
            element._guide.style.opacity = '0';

            // Remove after fade out
            setTimeout(() => {
                if (element._guide && element._guide.parentNode) {
                    element._guide.parentNode.removeChild(element._guide);
                    element._guide = null;
                }
            }, 300);
        }

        // Remove event listeners
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);

        // If we actually dragged (not just clicked)
        if (isDragging) {
            // Save the new position
            const highlight = highlightsData.find(h => h.id === highlightId);
            if (highlight) {
                highlight.notePosition = {
                    top: element.style.top,
                    left: element.style.left,
                    marginTop: '0'
                };
                saveHighlights();
            }

            // Remove the drop animation class after animation completes
            setTimeout(() => {
                element.classList.remove('drop-animation');
            }, 300);

            isDragging = false;
        } else {
            element.classList.remove('drop-animation');
        }
    }

    // Add event listener for mouse down
    element.addEventListener('mousedown', handleMouseDown);

    // Store the event listener reference for potential cleanup
    element._dragListener = handleMouseDown;
}

/**
 * Handles the "Highlight as Review Snippet" context menu action.
 * This creates a highlight with a prefix indicating whether it's a pro or con review point.
 * Also adds the highlighted text with prefix to the Stickara.
 * @param {string} snippetType - The type of review snippet ('pro' or 'con')
 * @param {string|null} color - The color name, or null/undefined if style is not 'color'.
 * @param {string} style - The style name (e.g., 'color', 'underline', 'wavy', 'border-thick').
 */
function handleHighlightReviewSnippet(snippetType, color, style) {
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed || !selection.rangeCount) {
        console.log("Stickara: Review Snippet highlight cancelled - no selection.");
        return;
    }

    // Prevent highlighting within Stickara's own UI elements
    const container = selection.anchorNode?.parentElement;
    if (container?.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
        console.log("Stickara: Review Snippet highlight cancelled - selection is inside Stickara UI.");
        selection.collapseToEnd(); // Clear selection visually
        return;
    }

    // Get the selected text
    const selectedText = selection.toString().trim();
    if (!selectedText) {
        console.log("Stickara: Review Snippet highlight cancelled - empty selection.");
        return;
    }

    // Create a range from the selection
    const range = selection.getRangeAt(0);
    if (!range) return;

    // Determine the prefix based on the snippet type
    const prefix = snippetType.toLowerCase() === 'pro' ? '👍 Pro: ' : '👎 Con: ';

    // Create a new range that includes the prefix
    const newRange = document.createRange();
    newRange.setStart(range.startContainer, range.startOffset);
    newRange.setEnd(range.endContainer, range.endOffset);

    // Serialize the range for storage
    const serialized = serializeRangeWithContext(newRange);
    if (!serialized) {
        console.warn("Stickara: Failed to serialize range for review snippet highlighting.");
        return;
    }

    // Generate a unique ID for this highlight
    const highlightId = `hl-${Date.now()}-${Math.random().toString(16).slice(2)}`;
    serialized.id = highlightId;
    serialized.style = style;
    serialized.color = (style === 'color' && color) ? color : null;
    serialized.snippetType = 'review';
    serialized.reviewType = snippetType.toLowerCase();
    serialized.prefix = prefix; // Store the prefix for display purposes

    // Add the highlight to storage
    if (!Array.isArray(highlightsData)) {
        console.warn("Stickara: highlightsData was not an array, resetting.");
        highlightsData = [];
    }

    // Check for duplicates
    const isDuplicate = highlightsData.some(h =>
        h.text === serialized.text &&
        h.prefix === serialized.prefix &&
        h.suffix === serialized.suffix
    );

    if (!isDuplicate) {
        // Add the new highlight
        highlightsData.push(serialized);
        saveHighlights();

        // Apply the highlight to the page
        applyHighlight(range, highlightId, serialized.color, serialized.style);

        // Add the prefix to the highlighted text in the DOM
        const highlightElement = document.querySelector(`mark.Stickara-highlight[data-highlight-id="${highlightId}"]`);
        if (highlightElement) {
            // Create a prefix element
            const prefixElement = document.createElement('span');
            prefixElement.className = 'Stickara-snippet-prefix';
            prefixElement.textContent = prefix;
            prefixElement.style.fontWeight = 'bold';
            prefixElement.style.color = snippetType.toLowerCase() === 'pro' ? '#4CAF50' : '#F44336'; // Green for pro, red for con

            // Insert the prefix at the beginning of the highlight
            highlightElement.insertBefore(prefixElement, highlightElement.firstChild);
        }

        // Add the highlighted text with prefix to the Stickara
        // Ensure note UI exists and is visible before adding text
        if (!noteContainer || !noteContainer.classList.contains('visible')) {
            showNote(); // Defined in interactions.js
            // Wait a short moment for the UI to become visible before adding text
            setTimeout(() => {
                // Create formatted text with appropriate styling for the Stickara
                const textWithPrefix = `<span style="font-weight: bold; color: ${snippetType.toLowerCase() === 'pro' ? '#4CAF50' : '#F44336'}">${prefix}</span>${selectedText}`;
                addFormattedTextToNote(textWithPrefix, highlightId);
            }, 150);
        } else {
            // Create formatted text with appropriate styling for the Stickara
            const textWithPrefix = `<span style="font-weight: bold; color: ${snippetType.toLowerCase() === 'pro' ? '#4CAF50' : '#F44336'}">${prefix}</span>${selectedText}`;
            addFormattedTextToNote(textWithPrefix, highlightId);
        }

        // Clear the selection
        selection.collapseToEnd();

        console.log(`Stickara: Review ${snippetType} Snippet highlight added. ID: ${highlightId}`);
    } else {
        console.log("Stickara: Highlight text/context already exists.");
        selection.collapseToEnd(); // Clear selection visually
    }
}

/**
 * Handles the "Highlight as Spec Snippet" context menu action.
 * This creates a highlight with a prefix indicating it's a specification detail.
 * Also adds the highlighted text with prefix to the Stickara.
 * @param {string|null} color - The color name, or null/undefined if style is not 'color'.
 * @param {string} style - The style name (e.g., 'color', 'underline', 'wavy', 'border-thick').
 */
function handleHighlightSpecSnippet(color, style) {
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed || !selection.rangeCount) {
        console.log("Stickara: Spec Snippet highlight cancelled - no selection.");
        return;
    }

    // Prevent highlighting within Stickara's own UI elements
    const container = selection.anchorNode?.parentElement;
    if (container?.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
        console.log("Stickara: Spec Snippet highlight cancelled - selection is inside Stickara UI.");
        selection.collapseToEnd(); // Clear selection visually
        return;
    }

    // Get the selected text
    const selectedText = selection.toString().trim();
    if (!selectedText) {
        console.log("Stickara: Spec Snippet highlight cancelled - empty selection.");
        return;
    }

    // Create a range from the selection
    const range = selection.getRangeAt(0);
    if (!range) return;

    // Define the prefix
    const prefix = '📋 Spec: ';

    // Create a new range that includes the prefix
    const newRange = document.createRange();
    newRange.setStart(range.startContainer, range.startOffset);
    newRange.setEnd(range.endContainer, range.endOffset);

    // Serialize the range for storage
    const serialized = serializeRangeWithContext(newRange);
    if (!serialized) {
        console.warn("Stickara: Failed to serialize range for spec snippet highlighting.");
        return;
    }

    // Generate a unique ID for this highlight
    const highlightId = `hl-${Date.now()}-${Math.random().toString(16).slice(2)}`;
    serialized.id = highlightId;
    serialized.style = style;
    serialized.color = (style === 'color' && color) ? color : null;
    serialized.snippetType = 'spec';
    serialized.prefix = prefix; // Store the prefix for display purposes

    // Add the highlight to storage
    if (!Array.isArray(highlightsData)) {
        console.warn("Stickara: highlightsData was not an array, resetting.");
        highlightsData = [];
    }

    // Check for duplicates
    const isDuplicate = highlightsData.some(h =>
        h.text === serialized.text &&
        h.prefix === serialized.prefix &&
        h.suffix === serialized.suffix
    );

    if (!isDuplicate) {
        // Add the new highlight
        highlightsData.push(serialized);
        saveHighlights();

        // Apply the highlight to the page
        applyHighlight(range, highlightId, serialized.color, serialized.style);

        // Add the prefix to the highlighted text in the DOM
        const highlightElement = document.querySelector(`mark.Stickara-highlight[data-highlight-id="${highlightId}"]`);
        if (highlightElement) {
            // Create a prefix element
            const prefixElement = document.createElement('span');
            prefixElement.className = 'Stickara-snippet-prefix';
            prefixElement.textContent = prefix;
            prefixElement.style.fontWeight = 'bold';
            prefixElement.style.color = '#2196F3'; // Blue for spec

            // Insert the prefix at the beginning of the highlight
            highlightElement.insertBefore(prefixElement, highlightElement.firstChild);
        }

        // Add the highlighted text with prefix to the Stickara
        // Ensure note UI exists and is visible before adding text
        if (!noteContainer || !noteContainer.classList.contains('visible')) {
            showNote(); // Defined in interactions.js
            // Wait a short moment for the UI to become visible before adding text
            setTimeout(() => {
                // Create formatted text with appropriate styling for the Stickara
                const textWithPrefix = `<span style="font-weight: bold; color: #2196F3">${prefix}</span>${selectedText}`;
                addFormattedTextToNote(textWithPrefix, highlightId);
            }, 150);
        } else {
            // Create formatted text with appropriate styling for the Stickara
            const textWithPrefix = `<span style="font-weight: bold; color: #2196F3">${prefix}</span>${selectedText}`;
            addFormattedTextToNote(textWithPrefix, highlightId);
        }

        // Clear the selection
        selection.collapseToEnd();

        console.log(`Stickara: Spec Snippet highlight added. ID: ${highlightId}`);
    } else {
        console.log("Stickara: Highlight text/context already exists.");
        selection.collapseToEnd(); // Clear selection visually
    }
}

/**
 * Handles the "Highlight as Review Snippet with Note" context menu action.
 * This creates a highlight with a prefix indicating whether it's a pro or con review point,
 * and opens a note editor for adding a note to the highlight.
 * @param {string} snippetType - The type of review snippet ('pro' or 'con')
 * @param {string|null} color - The color name, or null/undefined if style is not 'color'.
 * @param {string} style - The style name (e.g., 'color', 'underline', 'wavy', 'border-thick').
 */
function handleHighlightReviewSnippetWithNote(snippetType, color, style) {
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed || !selection.rangeCount) {
        console.log("Stickara: Review Snippet highlight with note cancelled - no selection.");
        return;
    }

    // Prevent highlighting within Stickara's own UI elements
    const container = selection.anchorNode?.parentElement;
    if (container?.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
        console.log("Stickara: Review Snippet highlight with note cancelled - selection is inside Stickara UI.");
        selection.collapseToEnd(); // Clear selection visually
        return;
    }

    // Get the selected text
    const selectedText = selection.toString().trim();
    if (!selectedText) {
        console.log("Stickara: Review Snippet highlight with note cancelled - empty selection.");
        return;
    }

    // Create a range from the selection
    const range = selection.getRangeAt(0);
    if (!range) return;

    // Determine the prefix based on the snippet type
    const prefix = snippetType.toLowerCase() === 'pro' ? '👍 Pro: ' : '👎 Con: ';

    // Create a new range that includes the prefix
    const newRange = document.createRange();
    newRange.setStart(range.startContainer, range.startOffset);
    newRange.setEnd(range.endContainer, range.endOffset);

    // Serialize the range for storage
    const serialized = serializeRangeWithContext(newRange);
    if (!serialized) {
        console.warn("Stickara: Failed to serialize range for review snippet highlighting with note.");
        return;
    }

    // Generate a unique ID for this highlight
    const highlightId = `hl-${Date.now()}-${Math.random().toString(16).slice(2)}`;
    serialized.id = highlightId;
    serialized.style = style;
    serialized.color = (style === 'color' && color) ? color : null;
    serialized.snippetType = 'review';
    serialized.reviewType = snippetType.toLowerCase();
    serialized.prefix = prefix; // Store the prefix for display purposes
    serialized.hasLinkedNote = true; // Mark that this highlight has a linked note

    // Add the highlight to storage
    if (!Array.isArray(highlightsData)) {
        console.warn("Stickara: highlightsData was not an array, resetting.");
        highlightsData = [];
    }

    // Check for duplicates
    const isDuplicate = highlightsData.some(h =>
        h.text === serialized.text &&
        h.prefix === serialized.prefix &&
        h.suffix === serialized.suffix
    );

    if (!isDuplicate) {
        // Add the new highlight
        highlightsData.push(serialized);
        saveHighlights();

        // Apply the highlight to the page
        applyHighlight(range, highlightId, serialized.color, serialized.style, true);

        // Add the prefix to the highlighted text in the DOM
        const highlightElement = document.querySelector(`mark.Stickara-highlight[data-highlight-id="${highlightId}"]`);
        if (highlightElement) {
            // Create a prefix element
            const prefixElement = document.createElement('span');
            prefixElement.className = 'Stickara-snippet-prefix';
            prefixElement.textContent = prefix;
            prefixElement.style.fontWeight = 'bold';
            prefixElement.style.color = snippetType.toLowerCase() === 'pro' ? '#4CAF50' : '#F44336'; // Green for pro, red for con

            // Insert the prefix at the beginning of the highlight
            highlightElement.insertBefore(prefixElement, highlightElement.firstChild);
        }

        // Show the note editor for this highlight
        showNoteEditor(highlightId, selectedText);

        console.log(`Stickara: Review ${snippetType} Snippet highlight with note added. ID: ${highlightId}`);
    } else {
        console.log("Stickara: Highlight text/context already exists.");
        selection.collapseToEnd(); // Clear selection visually
    }
}

/**
 * Handles the "Highlight as Spec Snippet with Note" context menu action.
 * This creates a highlight with a prefix indicating it's a specification detail,
 * and opens a note editor for adding a note to the highlight.
 * @param {string|null} color - The color name, or null/undefined if style is not 'color'.
 * @param {string} style - The style name (e.g., 'color', 'underline', 'wavy', 'border-thick').
 */
function handleHighlightSpecSnippetWithNote(color, style) {
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed || !selection.rangeCount) {
        console.log("Stickara: Spec Snippet highlight with note cancelled - no selection.");
        return;
    }

    // Prevent highlighting within Stickara's own UI elements
    const container = selection.anchorNode?.parentElement;
    if (container?.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
        console.log("Stickara: Spec Snippet highlight with note cancelled - selection is inside Stickara UI.");
        selection.collapseToEnd(); // Clear selection visually
        return;
    }

    // Get the selected text
    const selectedText = selection.toString().trim();
    if (!selectedText) {
        console.log("Stickara: Spec Snippet highlight with note cancelled - empty selection.");
        return;
    }

    // Create a range from the selection
    const range = selection.getRangeAt(0);
    if (!range) return;

    // Define the prefix
    const prefix = '📋 Spec: ';

    // Create a new range that includes the prefix
    const newRange = document.createRange();
    newRange.setStart(range.startContainer, range.startOffset);
    newRange.setEnd(range.endContainer, range.endOffset);

    // Serialize the range for storage
    const serialized = serializeRangeWithContext(newRange);
    if (!serialized) {
        console.warn("Stickara: Failed to serialize range for spec snippet highlighting with note.");
        return;
    }

    // Generate a unique ID for this highlight
    const highlightId = `hl-${Date.now()}-${Math.random().toString(16).slice(2)}`;
    serialized.id = highlightId;
    serialized.style = style;
    serialized.color = (style === 'color' && color) ? color : null;
    serialized.snippetType = 'spec';
    serialized.prefix = prefix; // Store the prefix for display purposes
    serialized.hasLinkedNote = true; // Mark that this highlight has a linked note

    // Add the highlight to storage
    if (!Array.isArray(highlightsData)) {
        console.warn("Stickara: highlightsData was not an array, resetting.");
        highlightsData = [];
    }

    // Check for duplicates
    const isDuplicate = highlightsData.some(h =>
        h.text === serialized.text &&
        h.prefix === serialized.prefix &&
        h.suffix === serialized.suffix
    );

    if (!isDuplicate) {
        // Add the new highlight
        highlightsData.push(serialized);
        saveHighlights();

        // Apply the highlight to the page
        applyHighlight(range, highlightId, serialized.color, serialized.style, true);

        // Add the prefix to the highlighted text in the DOM
        const highlightElement = document.querySelector(`mark.Stickara-highlight[data-highlight-id="${highlightId}"]`);
        if (highlightElement) {
            // Create a prefix element
            const prefixElement = document.createElement('span');
            prefixElement.className = 'Stickara-snippet-prefix';
            prefixElement.textContent = prefix;
            prefixElement.style.fontWeight = 'bold';
            prefixElement.style.color = '#2196F3'; // Blue for spec

            // Insert the prefix at the beginning of the highlight
            highlightElement.insertBefore(prefixElement, highlightElement.firstChild);
        }

        // Show the note editor for this highlight
        showNoteEditor(highlightId, selectedText);

        console.log(`Stickara: Spec Snippet highlight with note added. ID: ${highlightId}`);
    } else {
        console.log("Stickara: Highlight text/context already exists.");
        selection.collapseToEnd(); // Clear selection visually
    }
}

/**
 * Creates a side-by-side comparison container for Pro and Con snippets
 * This function is called when both Pro and Con snippets are present in the note
 * It also hides the original Pro and Con snippets
 */
function createProConComparisonView() {
    if (!noteText) return;

    console.log("Stickara: Creating Pro/Con comparison view...");

    // Remove any existing comparison view
    const existingComparison = noteText.querySelector('.Stickara-comparison-container');
    const existingTitle = noteText.querySelector('.Stickara-comparison-title');

    if (existingComparison) {
        existingComparison.remove();
    }

    if (existingTitle) {
        existingTitle.remove();
    }

    // Find all Pro and Con snippets in the note
    const proSnippets = Array.from(noteText.querySelectorAll('.Stickara-snippet-note[data-snippet-type="pro"]'));
    const conSnippets = Array.from(noteText.querySelectorAll('.Stickara-snippet-note[data-snippet-type="con"]'));

    console.log(`Stickara: Found ${proSnippets.length} Pro snippets and ${conSnippets.length} Con snippets`);

    // Only proceed if we have both Pro and Con snippets
    if (proSnippets.length === 0 || conSnippets.length === 0) {
        console.log("Stickara: Not enough snippets for comparison view");

        // Show all Pro and Con snippets if comparison view is not created
        proSnippets.forEach(snippet => {
            snippet.style.display = '';
        });

        conSnippets.forEach(snippet => {
            snippet.style.display = '';
        });

        return;
    }

    // Create a comparison container
    const comparisonContainer = document.createElement('div');
    comparisonContainer.className = 'Stickara-comparison-container';
    comparisonContainer.id = 'Stickara-comparison-container';

    // Create Pro column
    const proColumn = document.createElement('div');
    proColumn.className = 'Stickara-comparison-column pro-column';

    // Create Pro header
    const proHeader = document.createElement('div');
    proHeader.className = 'Stickara-comparison-header pro-header';
    proHeader.innerHTML = '<span style="font-weight: bold; color: #4CAF50">👍 Pros</span>';
    proColumn.appendChild(proHeader);

    // Create Con column
    const conColumn = document.createElement('div');
    conColumn.className = 'Stickara-comparison-column con-column';

    // Create Con header
    const conHeader = document.createElement('div');
    conHeader.className = 'Stickara-comparison-header con-header';
    conHeader.innerHTML = '<span style="font-weight: bold; color: #F44336">👎 Cons</span>';
    conColumn.appendChild(conHeader);

    // Add Pro snippets to Pro column with numbers
    proSnippets.forEach((snippet, index) => {
        // Extract the text content (without the prefix)
        const snippetText = snippet.textContent.replace(/👍 Pro: /, '').trim();

        // Create a new item for the comparison view with number
        const proItem = document.createElement('div');
        proItem.className = 'Stickara-comparison-item pro-item';

        // Add number span
        const numberSpan = document.createElement('span');
        numberSpan.className = 'Stickara-comparison-number';
        numberSpan.textContent = `${index + 1}. `;

        // Add text span
        const textSpan = document.createElement('span');
        textSpan.textContent = snippetText;

        // Add spans to item
        proItem.appendChild(numberSpan);
        proItem.appendChild(textSpan);

        // Add the item to the Pro column
        proColumn.appendChild(proItem);

        // Hide the original snippet
        snippet.style.display = 'none';

        // Also hide the <br> element that follows the snippet
        const nextBr = snippet.nextElementSibling;
        if (nextBr && nextBr.tagName.toLowerCase() === 'br') {
            nextBr.style.display = 'none';
        }
    });

    // Add Con snippets to Con column with numbers
    conSnippets.forEach((snippet, index) => {
        // Extract the text content (without the prefix)
        const snippetText = snippet.textContent.replace(/👎 Con: /, '').trim();

        // Create a new item for the comparison view with number
        const conItem = document.createElement('div');
        conItem.className = 'Stickara-comparison-item con-item';

        // Add number span
        const numberSpan = document.createElement('span');
        numberSpan.className = 'Stickara-comparison-number';
        numberSpan.textContent = `${index + 1}. `;

        // Add text span
        const textSpan = document.createElement('span');
        textSpan.textContent = snippetText;

        // Add spans to item
        conItem.appendChild(numberSpan);
        conItem.appendChild(textSpan);

        // Add the item to the Con column
        conColumn.appendChild(conItem);

        // Hide the original snippet
        snippet.style.display = 'none';

        // Also hide the <br> element that follows the snippet
        const nextBr = snippet.nextElementSibling;
        if (nextBr && nextBr.tagName.toLowerCase() === 'br') {
            nextBr.style.display = 'none';
        }
    });

    // Add columns to the comparison container
    comparisonContainer.appendChild(proColumn);
    comparisonContainer.appendChild(conColumn);

    // Add a title for the comparison view
    const comparisonTitle = document.createElement('div');
    comparisonTitle.className = 'Stickara-comparison-title';
    comparisonTitle.innerHTML = '<span style="font-weight: bold;">Pro/Con Comparison</span>';

    // Insert the comparison title and container at the end of the note
    noteText.appendChild(document.createElement('br'));
    noteText.appendChild(comparisonTitle);
    noteText.appendChild(comparisonContainer);

    console.log("Stickara: Pro/Con comparison view created");

    // Save the note content
    scheduleSave();
}

// Add a function to check for Pro/Con snippets and create comparison view when needed
function checkForProConSnippets() {
    if (!noteText) {
        console.log("Stickara: checkForProConSnippets - noteText not found");
        return;
    }

    console.log("Stickara: Checking for Pro/Con snippets...");

    // Find all Pro and Con snippets in the note
    const proSnippets = noteText.querySelectorAll('.Stickara-snippet-note[data-snippet-type="pro"]');
    const conSnippets = noteText.querySelectorAll('.Stickara-snippet-note[data-snippet-type="con"]');

    console.log(`Stickara: Found ${proSnippets.length} Pro snippets and ${conSnippets.length} Con snippets`);

    // Always create or update the comparison view if we have both Pro and Con snippets
    if (proSnippets.length > 0 && conSnippets.length > 0) {
        console.log("Stickara: Creating Pro/Con comparison view");
        createProConComparisonView();
    } else {
        console.log("Stickara: Not enough snippets for comparison view");

        // Remove any existing comparison view if we don't have both Pro and Con snippets
        const existingComparison = noteText.querySelector('.Stickara-comparison-container');
        const existingTitle = noteText.querySelector('.Stickara-comparison-title');

        if (existingComparison) {
            existingComparison.remove();
            console.log("Stickara: Removed existing comparison container");
        }

        if (existingTitle) {
            existingTitle.remove();
            console.log("Stickara: Removed existing comparison title");
        }

        // Show all Pro and Con snippets if comparison view is removed
        proSnippets.forEach(snippet => {
            snippet.style.display = '';

            // Also show the <br> element that follows the snippet
            const nextBr = snippet.nextElementSibling;
            if (nextBr && nextBr.tagName.toLowerCase() === 'br') {
                nextBr.style.display = '';
            }
        });

        conSnippets.forEach(snippet => {
            snippet.style.display = '';

            // Also show the <br> element that follows the snippet
            const nextBr = snippet.nextElementSibling;
            if (nextBr && nextBr.tagName.toLowerCase() === 'br') {
                nextBr.style.display = '';
            }
        });
    }
}

// We no longer need to override addFormattedTextToNote since we've updated the original function
// to handle Pro/Con comparison view creation

// Also check for Pro/Con snippets when the note is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log("Stickara: DOM loaded, setting up Pro/Con snippet checker");

    // Wait for the note to be loaded
    setTimeout(function() {
        if (noteText) {
            console.log("Stickara: Initial check for Pro/Con snippets");
            checkForProConSnippets();
        }
    }, 1000);

    // Also check periodically for Pro/Con snippets
    setInterval(function() {
        if (noteText && noteContainer && noteContainer.classList.contains('visible')) {
            console.log("Stickara: Periodic check for Pro/Con snippets");
            checkForProConSnippets();
        }
    }, 5000); // Check every 5 seconds while the note is visible
});

// --- Dynamic Content Change Detection for Highlights ---

/**
 * Global state for dynamic highlight restoration
 */
const dynamicHighlightState = {
    observer: null,
    isObserving: false,
    lastRestoreTime: 0,
    restoreInProgress: false,
    contentChangeCount: 0,
    restorationAttempts: 0,
    maxRestorationAttempts: 5,
    observerConfig: {
        childList: true,
        subtree: true,
        characterData: true,
        attributes: false // Don't watch attributes to reduce noise
    }
};

/**
 * Checks if a DOM change is significant enough to warrant highlight restoration
 * @param {MutationRecord[]} mutations - Array of mutation records
 * @returns {boolean} - True if changes are significant
 */
function isSignificantContentChange(mutations) {
    let significantChanges = 0;
    let textChanges = 0;
    let nodeChanges = 0;
    let highlightRelatedChanges = 0;

    for (const mutation of mutations) {
        // Skip changes to Stickara's own elements
        if (mutation.target.closest && mutation.target.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
            continue;
        }

        // Skip changes to highlight elements themselves (they might be getting applied)
        if (mutation.target.classList && mutation.target.classList.contains(HIGHLIGHT_CLASS)) {
            highlightRelatedChanges++;
            continue;
        }

        // Skip changes that are likely from our own highlight application
        if (mutation.type === 'childList') {
            const hasHighlightNodes = [...mutation.addedNodes, ...mutation.removedNodes].some(node => {
                return node.nodeType === Node.ELEMENT_NODE &&
                       node.classList && node.classList.contains(HIGHLIGHT_CLASS);
            });
            if (hasHighlightNodes) {
                highlightRelatedChanges++;
                continue;
            }
        }

        // Count different types of changes
        if (mutation.type === 'characterData') {
            // Only count text changes that are substantial
            if (mutation.target.textContent && mutation.target.textContent.trim().length > 5) {
                textChanges++;
            }
        } else if (mutation.type === 'childList') {
            // Check if nodes were added or removed
            if (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0) {
                // Filter out script, style, and other non-content nodes
                const contentNodes = [...mutation.addedNodes, ...mutation.removedNodes].filter(node => {
                    if (node.nodeType === Node.TEXT_NODE) {
                        return node.textContent.trim().length > 10; // Require more substantial text
                    } else if (node.nodeType === Node.ELEMENT_NODE) {
                        const tagName = node.tagName?.toLowerCase();
                        return tagName &&
                               !['script', 'style', 'meta', 'link', 'noscript', 'br', 'hr'].includes(tagName) &&
                               !node.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`) &&
                               !node.classList?.contains(HIGHLIGHT_CLASS);
                    }
                    return false;
                });

                if (contentNodes.length > 0) {
                    nodeChanges++;
                }
            }
        }
    }

    significantChanges = textChanges + nodeChanges;

    // Be more conservative - require more substantial changes
    const isSignificant = (significantChanges >= 3) || (textChanges >= 5) || (nodeChanges >= 2);

    if (isSignificant) {
        console.log(`Stickara: Detected significant content change (text: ${textChanges}, nodes: ${nodeChanges}, highlight-related: ${highlightRelatedChanges})`);
    }

    return isSignificant;
}

/**
 * Checks if highlights actually need restoration by comparing expected vs current
 * @returns {boolean} - True if restoration is needed
 */
function shouldRestoreHighlights() {
    if (!Array.isArray(highlightsData) || highlightsData.length === 0) {
        return false; // No highlights to restore
    }

    const existingHighlights = document.querySelectorAll(`.${HIGHLIGHT_CLASS}`);
    const existingHighlightIds = new Set();

    // Collect IDs of existing highlights (excluding Stickara UI)
    existingHighlights.forEach(mark => {
        if (!mark.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
            const id = mark.getAttribute('data-highlight-id');
            if (id) {
                existingHighlightIds.add(id);
            }
        }
    });

    // Check if any expected highlights are missing
    const missingHighlights = highlightsData.filter(data =>
        data && data.id && !existingHighlightIds.has(data.id)
    );

    const needsRestoration = missingHighlights.length > 0;

    if (needsRestoration) {
        console.log(`Stickara: Missing ${missingHighlights.length}/${highlightsData.length} highlights, restoration needed`);
    }

    return needsRestoration;
}

/**
 * Restores highlights after content changes with intelligent filtering
 * @param {boolean} force - Force restoration even if recently attempted
 */
function restoreHighlightsAfterContentChange(force = false) {
    const now = Date.now();

    // Check if restoration is actually needed (unless forced)
    if (!force && !shouldRestoreHighlights()) {
        console.log("Stickara: Skipping highlight restoration - no missing highlights detected");
        return;
    }

    // Prevent excessive restoration attempts
    if (!force && (now - dynamicHighlightState.lastRestoreTime < 2000)) {
        console.log("Stickara: Skipping highlight restoration - too recent");
        return;
    }

    // Prevent concurrent restoration
    if (dynamicHighlightState.restoreInProgress) {
        console.log("Stickara: Skipping highlight restoration - already in progress");
        return;
    }

    // Check restoration attempt limits
    if (dynamicHighlightState.restorationAttempts >= dynamicHighlightState.maxRestorationAttempts) {
        console.log("Stickara: Maximum restoration attempts reached, stopping automatic restoration");
        return;
    }

    dynamicHighlightState.restoreInProgress = true;
    dynamicHighlightState.lastRestoreTime = now;
    dynamicHighlightState.restorationAttempts++;

    console.log(`Stickara: Attempting highlight restoration (attempt ${dynamicHighlightState.restorationAttempts}/${dynamicHighlightState.maxRestorationAttempts})`);

    // Temporarily disable threat detection during restoration to prevent false positives
    const threatDetectionWasActive = window.StickaraThreatDetection && window.StickaraThreatDetection.isActive();
    if (threatDetectionWasActive) {
        window.StickaraThreatDetection.pause();
    }

    try {
        // Get existing highlights to avoid clearing ones that are working
        const existingHighlights = document.querySelectorAll(`.${HIGHLIGHT_CLASS}`);
        const existingHighlightIds = new Set();

        existingHighlights.forEach(mark => {
            if (!mark.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
                const id = mark.getAttribute('data-highlight-id');
                if (id) {
                    existingHighlightIds.add(id);
                }
            }
        });

        // Only restore missing highlights, don't clear existing ones
        if (Array.isArray(highlightsData) && highlightsData.length > 0) {
            let restoredCount = 0;
            let failedCount = 0;
            let skippedCount = 0;

            highlightsData.forEach((data) => {
                if (!data || typeof data.text !== 'string' || !data.id) {
                    failedCount++;
                    return;
                }

                // Skip if highlight already exists
                if (existingHighlightIds.has(data.id)) {
                    skippedCount++;
                    return;
                }

                const range = deserializeRangeByContext(data);
                if (range) {
                    try {
                        const highlightStyle = data.style || 'color';
                        const highlightColor = (highlightStyle === 'color') ? (data.color || DEFAULT_HIGHLIGHT_COLOR) : null;

                        applyHighlight(range, data.id, highlightColor, highlightStyle, data.hasLinkedNote);
                        restoredCount++;
                    } catch (error) {
                        console.warn(`Stickara: Error applying restored highlight ${data.id}:`, error);
                        failedCount++;
                    }
                } else {
                    failedCount++;
                }
            });

            console.log(`Stickara: Highlight restoration complete - restored: ${restoredCount}, failed: ${failedCount}, skipped: ${skippedCount}`);

            // Reset attempt counter if we had good success rate
            if (restoredCount > 0 && (failedCount === 0 || restoredCount / (restoredCount + failedCount) > 0.7)) {
                dynamicHighlightState.restorationAttempts = 0;
                console.log("Stickara: Reset restoration attempt counter due to successful restoration");
            }
        }

    } catch (error) {
        console.error("Stickara: Error during highlight restoration:", error);
    } finally {
        dynamicHighlightState.restoreInProgress = false;

        // Re-enable threat detection after restoration is complete
        if (threatDetectionWasActive && window.StickaraThreatDetection) {
            setTimeout(() => {
                window.StickaraThreatDetection.resume();
            }, 500); // Wait 500ms to ensure all highlights are applied
        }
    }
}

/**
 * Debounced version of highlight restoration to prevent excessive calls
 */
const debouncedHighlightRestoration = window.debounce ?
    window.debounce(restoreHighlightsAfterContentChange, 500) :
    restoreHighlightsAfterContentChange;

/**
 * Handles mutations detected by the content change observer
 * @param {MutationRecord[]} mutations - Array of mutation records
 */
function handleContentMutations(mutations) {
    // Filter out mutations that are not relevant
    const relevantMutations = mutations.filter(mutation => {
        // Skip mutations on Stickara elements
        if (mutation.target.closest && mutation.target.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
            return false;
        }

        // Skip mutations on highlight elements themselves
        if (mutation.target.classList && mutation.target.classList.contains(HIGHLIGHT_CLASS)) {
            return false;
        }

        return true;
    });

    if (relevantMutations.length === 0) {
        return;
    }

    dynamicHighlightState.contentChangeCount++;

    // Check if changes are significant enough to warrant restoration
    if (isSignificantContentChange(relevantMutations)) {
        console.log(`Stickara: Content change detected (total changes: ${dynamicHighlightState.contentChangeCount}), scheduling highlight restoration`);
        debouncedHighlightRestoration();
    }
}

/**
 * Starts observing content changes for dynamic highlight restoration
 */
function startDynamicHighlightObserver() {
    if (dynamicHighlightState.isObserving || !document.body) {
        return;
    }

    try {
        // Create the mutation observer
        dynamicHighlightState.observer = new MutationObserver(handleContentMutations);

        // Start observing
        dynamicHighlightState.observer.observe(document.body, dynamicHighlightState.observerConfig);
        dynamicHighlightState.isObserving = true;

        console.log("Stickara: Started dynamic highlight restoration observer");

        // Reset counters
        dynamicHighlightState.contentChangeCount = 0;
        dynamicHighlightState.restorationAttempts = 0;

        // Set initial grace period to avoid interfering with page load
        dynamicHighlightState.lastRestoreTime = Date.now();

    } catch (error) {
        console.error("Stickara: Error starting dynamic highlight observer:", error);
    }
}

/**
 * Stops observing content changes
 */
function stopDynamicHighlightObserver() {
    if (dynamicHighlightState.observer) {
        dynamicHighlightState.observer.disconnect();
        dynamicHighlightState.observer = null;
        dynamicHighlightState.isObserving = false;
        console.log("Stickara: Stopped dynamic highlight restoration observer");
    }
}

/**
 * Resets the dynamic highlight restoration state
 */
function resetDynamicHighlightState() {
    dynamicHighlightState.lastRestoreTime = 0;
    dynamicHighlightState.restoreInProgress = false;
    dynamicHighlightState.contentChangeCount = 0;
    dynamicHighlightState.restorationAttempts = 0;
    console.log("Stickara: Reset dynamic highlight restoration state");
}

/**
 * Temporarily disables the observer during highlight operations
 * @param {Function} operation - The operation to perform while observer is disabled
 * @param {number} delay - How long to keep observer disabled after operation (ms)
 */
function withObserverDisabled(operation, delay = 1000) {
    const wasObserving = dynamicHighlightState.isObserving;

    if (wasObserving) {
        stopDynamicHighlightObserver();
    }

    try {
        operation();
    } finally {
        if (wasObserving) {
            setTimeout(() => {
                startDynamicHighlightObserver();
            }, delay);
        }
    }
}

/**
 * Enhanced version of loadAndApplyHighlights that also starts dynamic observation
 */
function loadAndApplyHighlightsEnhanced() {
    // Temporarily disable observer during initial load to prevent interference
    withObserverDisabled(() => {
        loadAndApplyHighlights();
    }, 3000); // Keep observer disabled for 3 seconds after initial load

    // Start dynamic observation after a longer delay to allow initial highlights to fully settle
    setTimeout(() => {
        startDynamicHighlightObserver();
        setupPageVisibilityHandling();
        setupManualRestorationTriggers();
        handleSPANavigation();
    }, 3000); // Increased delay to 3 seconds to avoid interfering with initial load
}

/**
 * Sets up page visibility change handling to restore highlights when page becomes visible
 */
function setupPageVisibilityHandling() {
    if (typeof document.addEventListener !== 'function') {
        return;
    }

    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden && dynamicHighlightState.isObserving) {
            // Page became visible, check if highlights need restoration
            setTimeout(() => {
                const existingHighlights = document.querySelectorAll(`.${HIGHLIGHT_CLASS}`);
                const expectedHighlights = Array.isArray(highlightsData) ? highlightsData.length : 0;

                if (expectedHighlights > 0 && existingHighlights.length < expectedHighlights) {
                    console.log(`Stickara: Page visibility restored, missing highlights detected (${existingHighlights.length}/${expectedHighlights}), restoring...`);
                    restoreHighlightsAfterContentChange(true); // Force restoration
                }
            }, 500);
        }
    });

    // Handle focus events as additional trigger
    window.addEventListener('focus', () => {
        if (dynamicHighlightState.isObserving) {
            setTimeout(() => {
                const existingHighlights = document.querySelectorAll(`.${HIGHLIGHT_CLASS}`);
                const expectedHighlights = Array.isArray(highlightsData) ? highlightsData.length : 0;

                if (expectedHighlights > 0 && existingHighlights.length === 0) {
                    console.log("Stickara: Window focus restored, no highlights found, restoring...");
                    restoreHighlightsAfterContentChange(true); // Force restoration
                }
            }, 300);
        }
    });

    console.log("Stickara: Set up page visibility and focus handling for highlight restoration");
}

/**
 * Sets up manual restoration triggers for user-initiated restoration
 */
function setupManualRestorationTriggers() {
    // Add keyboard shortcut for manual restoration (Ctrl+Shift+H)
    document.addEventListener('keydown', (event) => {
        if (event.ctrlKey && event.shiftKey && event.key === 'H') {
            event.preventDefault();
            console.log("Stickara: Manual highlight restoration triggered by keyboard shortcut");
            resetDynamicHighlightState();
            restoreHighlightsAfterContentChange(true);
        }
    });

    // Expose global function for manual restoration
    window.StickaraRestoreHighlights = function() {
        console.log("Stickara: Manual highlight restoration triggered via global function");
        resetDynamicHighlightState();

        // Use observer disabling during manual restoration
        withObserverDisabled(() => {
            restoreHighlightsAfterContentChange(true);
        }, 2000); // Keep observer disabled for 2 seconds after manual restoration

        return {
            expectedHighlights: Array.isArray(highlightsData) ? highlightsData.length : 0,
            currentHighlights: document.querySelectorAll(`.${HIGHLIGHT_CLASS}`).length,
            observerActive: dynamicHighlightState.isObserving
        };
    };

    // Expose function to get highlight restoration status
    window.StickaraHighlightStatus = function() {
        return {
            expectedHighlights: Array.isArray(highlightsData) ? highlightsData.length : 0,
            currentHighlights: document.querySelectorAll(`.${HIGHLIGHT_CLASS}`).length,
            observerActive: dynamicHighlightState.isObserving,
            contentChangeCount: dynamicHighlightState.contentChangeCount,
            restorationAttempts: dynamicHighlightState.restorationAttempts,
            lastRestoreTime: dynamicHighlightState.lastRestoreTime,
            restoreInProgress: dynamicHighlightState.restoreInProgress
        };
    };

    console.log("Stickara: Set up manual restoration triggers (Ctrl+Shift+H, window.StickaraRestoreHighlights())");
}

/**
 * Handles cases where the page URL changes without a full reload (SPA navigation)
 */
function handleSPANavigation() {
    // Store original pushState and replaceState
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    // Override pushState
    history.pushState = function() {
        originalPushState.apply(history, arguments);
        handleURLChange();
    };

    // Override replaceState
    history.replaceState = function() {
        originalReplaceState.apply(history, arguments);
        handleURLChange();
    };

    // Handle popstate events
    window.addEventListener('popstate', handleURLChange);

    // Handle hash changes (for hash-based routing)
    window.addEventListener('hashchange', handleURLChange);

    // Handle page visibility changes (for PWA/service worker navigation)
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
            // Page became visible, check if URL changed while hidden
            setTimeout(handleURLChange, 100);
        }
    });

    // Handle focus events (for multi-tab/window scenarios)
    window.addEventListener('focus', () => {
        setTimeout(handleURLChange, 100);
    });



    function handleURLChange() {
        // URL changed, stop current observer and restart with new URL context
        setTimeout(() => {
            console.log("Stickara: URL change detected, restarting highlight system");
            stopDynamicHighlightObserver();
            resetDynamicHighlightState();

            // Reload highlights for new URL if needed
            setTimeout(() => {
                if (typeof loadAndApplyHighlightsEnhanced === 'function') {
                    loadAndApplyHighlightsEnhanced();
                }
            }, 500);
        }, 100);
    }

    console.log("Stickara: Set up SPA navigation handling for highlight restoration");
}

// --- End Dynamic Content Change Detection ---

console.log("Stickara: Highlighting Logic Updated (Hover Delete Button Method, +Deserialize Logging, Fuzzy Context, Ellipsis Handling, Robust Deserialize V2, Highlight with Note, Enhanced Note UI, Draggable Notes, Review and Spec Snippets, Snippet with Note, Pro/Con Comparison, Dynamic Content Change Detection)");
// --- END OF FILE content-highlighting.js ---