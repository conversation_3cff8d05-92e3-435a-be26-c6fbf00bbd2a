// --- START OF FILE secure-messaging.js ---

/**
 * Secure Messaging System for Stickara
 *
 * This module provides encrypted communication between different parts of the extension
 * (background script, content scripts, popup) using the Web Crypto API.
 */

// Create a namespace to avoid global pollution
window.StickaraSecureMessaging = (function() {

    /**
     * Cryptographic keys
     */
    let encryptionKey = null;
    let signingKey = null;

    /**
     * Message sequence number to prevent replay attacks
     */
    let sequenceNumber = 0;

    /**
     * Recently processed message IDs to prevent replay attacks
     */
    const processedMessageIds = new Set();

    /**
     * Maximum number of processed message IDs to store
     */
    const MAX_PROCESSED_IDS = 1000;

    /**
     * Initializes the secure messaging system
     * @returns {Promise<void>}
     */
    async function initialize() {
        try {
            // Check if we're in a context where crypto is available
            if (!window.crypto || !window.crypto.subtle) {
                console.log("Stickara: Web Crypto API not available, using fallback messaging");
                return;
            }

            try {
                // Generate or retrieve encryption key
                encryptionKey = await getOrGenerateKey('encryption', 'AES-GCM', { length: 256 });

                // Generate or retrieve signing key
                signingKey = await getOrGenerateKey('signing', 'HMAC', { hash: 'SHA-256', length: 256 });

                console.log("Stickara: Secure Messaging initialized");
            } catch (e) {
                console.log("Stickara: Error generating crypto keys, using fallback messaging:", e);
            }
        } catch (e) {
            console.log("Stickara: Error initializing Secure Messaging, using fallback messaging:", e);
        }
    }

    /**
     * Gets or generates a cryptographic key
     * @param {string} keyName - The name of the key
     * @param {string} algorithm - The algorithm to use
     * @param {Object} options - Algorithm options
     * @returns {Promise<CryptoKey>} The cryptographic key
     */
    async function getOrGenerateKey(keyName, algorithm, options) {
        try {
            // Try to retrieve the key from storage
            const result = await new Promise(resolve => {
                chrome.storage.local.get(`secureMessaging_${keyName}Key`, resolve);
            });

            const storedKey = result[`secureMessaging_${keyName}Key`];

            if (storedKey) {
                // Import the stored key
                const keyData = hexToArrayBuffer(storedKey);

                let keyAlgorithm;
                let keyUsages;

                if (algorithm === 'AES-GCM') {
                    keyAlgorithm = { name: algorithm, length: options.length };
                    keyUsages = ['encrypt', 'decrypt'];
                } else if (algorithm === 'HMAC') {
                    keyAlgorithm = { name: algorithm, hash: options.hash, length: options.length };
                    keyUsages = ['sign', 'verify'];
                } else {
                    throw new Error(`Unsupported algorithm: ${algorithm}`);
                }

                return await crypto.subtle.importKey(
                    'raw',
                    keyData,
                    keyAlgorithm,
                    false, // non-extractable
                    keyUsages
                );
            } else {
                // Generate a new key
                let keyAlgorithm;
                let keyUsages;

                if (algorithm === 'AES-GCM') {
                    keyAlgorithm = { name: algorithm, length: options.length };
                    keyUsages = ['encrypt', 'decrypt'];
                } else if (algorithm === 'HMAC') {
                    keyAlgorithm = { name: algorithm, hash: options.hash, length: options.length };
                    keyUsages = ['sign', 'verify'];
                } else {
                    throw new Error(`Unsupported algorithm: ${algorithm}`);
                }

                const key = await crypto.subtle.generateKey(
                    keyAlgorithm,
                    true, // extractable
                    keyUsages
                );

                // Export the key for storage
                const exportedKey = await crypto.subtle.exportKey('raw', key);
                const keyHex = arrayBufferToHex(exportedKey);

                // Store the key
                await new Promise(resolve => {
                    chrome.storage.local.set({ [`secureMessaging_${keyName}Key`]: keyHex }, resolve);
                });

                return key;
            }
        } catch (e) {
            console.error(`Stickara: Error getting/generating ${keyName} key:`, e);
            throw e;
        }
    }

    /**
     * Converts an ArrayBuffer to a hex string
     * @param {ArrayBuffer} buffer - The buffer to convert
     * @returns {string} The hex string
     */
    function arrayBufferToHex(buffer) {
        return Array.from(new Uint8Array(buffer))
            .map(b => b.toString(16).padStart(2, '0'))
            .join('');
    }

    /**
     * Converts a hex string to an ArrayBuffer
     * @param {string} hex - The hex string to convert
     * @returns {ArrayBuffer} The array buffer
     */
    function hexToArrayBuffer(hex) {
        const bytes = new Uint8Array(hex.length / 2);
        for (let i = 0; i < hex.length; i += 2) {
            bytes[i / 2] = parseInt(hex.substring(i, i + 2), 16);
        }
        return bytes.buffer;
    }

    /**
     * Generates a random initialization vector (IV)
     * @returns {Uint8Array} The random IV
     */
    function generateIV() {
        return crypto.getRandomValues(new Uint8Array(12));
    }

    /**
     * Generates a unique message ID
     * @returns {string} The message ID
     */
    function generateMessageId() {
        const random = crypto.getRandomValues(new Uint8Array(16));
        return arrayBufferToHex(random);
    }

    /**
     * Encrypts a message
     * @param {Object} message - The message to encrypt
     * @returns {Promise<Object>} The encrypted message
     */
    async function encryptMessage(message) {
        try {
            if (!encryptionKey || !signingKey) {
                await initialize();
            }

            // Add metadata to the message
            const messageWithMetadata = {
                ...message,
                _metadata: {
                    timestamp: Date.now(),
                    sequenceNumber: sequenceNumber++,
                    messageId: generateMessageId()
                }
            };

            // Convert message to JSON string
            const messageString = JSON.stringify(messageWithMetadata);

            // Convert message to ArrayBuffer
            const encoder = new TextEncoder();
            const messageData = encoder.encode(messageString);

            // Generate IV
            const iv = generateIV();

            // Encrypt the message
            const encryptedData = await crypto.subtle.encrypt(
                {
                    name: 'AES-GCM',
                    iv: iv
                },
                encryptionKey,
                messageData
            );

            // Create the encrypted message object
            const encryptedMessage = {
                iv: arrayBufferToHex(iv),
                encryptedData: arrayBufferToHex(encryptedData)
            };

            // Sign the encrypted message
            const encryptedMessageString = JSON.stringify(encryptedMessage);
            const encryptedMessageData = encoder.encode(encryptedMessageString);

            const signature = await crypto.subtle.sign(
                'HMAC',
                signingKey,
                encryptedMessageData
            );

            // Return the complete secure message
            return {
                type: 'secureMessage',
                encryptedMessage,
                signature: arrayBufferToHex(signature)
            };
        } catch (e) {
            console.error("Stickara: Error encrypting message:", e);
            throw e;
        }
    }

    /**
     * Decrypts a message
     * @param {Object} secureMessage - The secure message to decrypt
     * @returns {Promise<Object>} The decrypted message
     */
    async function decryptMessage(secureMessage) {
        try {
            if (!encryptionKey || !signingKey) {
                await initialize();
            }

            // Verify the message type
            if (secureMessage.type !== 'secureMessage') {
                throw new Error('Invalid secure message type');
            }

            // Extract components
            const { encryptedMessage, signature } = secureMessage;

            // Verify the signature
            const encoder = new TextEncoder();
            const encryptedMessageString = JSON.stringify(encryptedMessage);
            const encryptedMessageData = encoder.encode(encryptedMessageString);

            const signatureData = hexToArrayBuffer(signature);

            const isValid = await crypto.subtle.verify(
                'HMAC',
                signingKey,
                signatureData,
                encryptedMessageData
            );

            if (!isValid) {
                throw new Error('Invalid message signature');
            }

            // Decrypt the message
            const iv = hexToArrayBuffer(encryptedMessage.iv);
            const encryptedData = hexToArrayBuffer(encryptedMessage.encryptedData);

            const decryptedData = await crypto.subtle.decrypt(
                {
                    name: 'AES-GCM',
                    iv: new Uint8Array(iv)
                },
                encryptionKey,
                encryptedData
            );

            // Convert decrypted data to string
            const decoder = new TextDecoder();
            const decryptedString = decoder.decode(decryptedData);

            // Parse the decrypted message
            const decryptedMessage = JSON.parse(decryptedString);

            // Verify metadata
            if (!decryptedMessage._metadata) {
                throw new Error('Missing message metadata');
            }

            const { messageId, timestamp } = decryptedMessage._metadata;

            // Check for replay attacks
            if (processedMessageIds.has(messageId)) {
                throw new Error('Message replay detected');
            }

            // Check message age (5 minute maximum)
            const messageAge = Date.now() - timestamp;
            if (messageAge > 5 * 60 * 1000) {
                throw new Error('Message too old');
            }

            // Add message ID to processed set
            processedMessageIds.add(messageId);

            // Trim processed IDs if needed
            if (processedMessageIds.size > MAX_PROCESSED_IDS) {
                const idsToRemove = Array.from(processedMessageIds).slice(0, processedMessageIds.size - MAX_PROCESSED_IDS);
                idsToRemove.forEach(id => processedMessageIds.delete(id));
            }

            // Remove metadata from the message
            const { _metadata, ...messageContent } = decryptedMessage;

            return messageContent;
        } catch (e) {
            console.error("Stickara: Error decrypting message:", e);
            throw e;
        }
    }

    /**
     * Sends a secure message to the background script
     * @param {Object} message - The message to send
     * @returns {Promise<Object>} The response
     */
    async function sendToBackground(message) {
        try {
            // Check if encryption is available
            if (!encryptionKey || !signingKey) {
                // Fall back to standard messaging if encryption is not available
                console.log("Stickara: Encryption not available, using standard messaging");
                return new Promise((resolve) => {
                    try {
                        chrome.runtime.sendMessage(message, response => {
                            const error = chrome.runtime.lastError;
                            if (error) {
                                console.log("Stickara: Error in standard messaging:", error);
                                // Resolve with null instead of rejecting to avoid breaking functionality
                                resolve(null);
                            } else {
                                resolve(response);
                            }
                        });
                    } catch (e) {
                        console.log("Stickara: Exception in standard messaging:", e);
                        // Resolve with null instead of rejecting
                        resolve(null);
                    }
                });
            }

            // Try to encrypt the message
            let secureMessage;
            try {
                secureMessage = await encryptMessage(message);
            } catch (e) {
                console.log("Stickara: Error encrypting message, using standard messaging:", e);
                // Fall back to standard messaging
                return new Promise((resolve) => {
                    try {
                        chrome.runtime.sendMessage(message, response => {
                            const error = chrome.runtime.lastError;
                            if (error) {
                                console.log("Stickara: Error in standard messaging:", error);
                                resolve(null);
                            } else {
                                resolve(response);
                            }
                        });
                    } catch (e) {
                        console.log("Stickara: Exception in standard messaging:", e);
                        resolve(null);
                    }
                });
            }

            // Send the secure message
            return new Promise((resolve) => {
                try {
                    chrome.runtime.sendMessage(secureMessage, response => {
                        const error = chrome.runtime.lastError;
                        if (error) {
                            console.log("Stickara: Error in secure messaging:", error);
                            // Resolve with null instead of rejecting
                            resolve(null);
                            return;
                        }

                        // If the response is a secure message, try to decrypt it
                        if (response && response.type === 'secureMessage') {
                            try {
                                decryptMessage(response)
                                    .then(resolve)
                                    .catch(e => {
                                        console.log("Stickara: Error decrypting response:", e);
                                        resolve(null);
                                    });
                            } catch (e) {
                                console.log("Stickara: Exception decrypting response:", e);
                                resolve(null);
                            }
                        } else {
                            // Otherwise, return the response as-is
                            resolve(response);
                        }
                    });
                } catch (e) {
                    console.log("Stickara: Exception sending secure message:", e);
                    resolve(null);
                }
            });
        } catch (e) {
            console.log("Stickara: Error in sendToBackground:", e);
            // Return null instead of throwing to avoid breaking functionality
            return null;
        }
    }

    /**
     * Sends a secure message to a tab
     * @param {number} tabId - The ID of the tab to send to
     * @param {Object} message - The message to send
     * @returns {Promise<Object>} The response
     */
    async function sendToTab(tabId, message) {
        try {
            // Check if encryption is available
            if (!encryptionKey || !signingKey) {
                // Fall back to standard messaging if encryption is not available
                console.log("Stickara: Encryption not available, using standard messaging for tab");
                return new Promise((resolve) => {
                    try {
                        chrome.tabs.sendMessage(tabId, message, response => {
                            const error = chrome.runtime.lastError;
                            if (error) {
                                console.log(`Stickara: Error in standard messaging to tab ${tabId}:`, error);
                                // Resolve with null instead of rejecting to avoid breaking functionality
                                resolve(null);
                            } else {
                                resolve(response);
                            }
                        });
                    } catch (e) {
                        console.log(`Stickara: Exception in standard messaging to tab ${tabId}:`, e);
                        // Resolve with null instead of rejecting
                        resolve(null);
                    }
                });
            }

            // Try to encrypt the message
            let secureMessage;
            try {
                secureMessage = await encryptMessage(message);
            } catch (e) {
                console.log(`Stickara: Error encrypting message for tab ${tabId}, using standard messaging:`, e);
                // Fall back to standard messaging
                return new Promise((resolve) => {
                    try {
                        chrome.tabs.sendMessage(tabId, message, response => {
                            const error = chrome.runtime.lastError;
                            if (error) {
                                console.log(`Stickara: Error in standard messaging to tab ${tabId}:`, error);
                                resolve(null);
                            } else {
                                resolve(response);
                            }
                        });
                    } catch (e) {
                        console.log(`Stickara: Exception in standard messaging to tab ${tabId}:`, e);
                        resolve(null);
                    }
                });
            }

            // Send the secure message
            return new Promise((resolve) => {
                try {
                    chrome.tabs.sendMessage(tabId, secureMessage, response => {
                        const error = chrome.runtime.lastError;
                        if (error) {
                            console.log(`Stickara: Error in secure messaging to tab ${tabId}:`, error);
                            // Resolve with null instead of rejecting
                            resolve(null);
                            return;
                        }

                        // If the response is a secure message, try to decrypt it
                        if (response && response.type === 'secureMessage') {
                            try {
                                decryptMessage(response)
                                    .then(resolve)
                                    .catch(e => {
                                        console.log(`Stickara: Error decrypting response from tab ${tabId}:`, e);
                                        resolve(null);
                                    });
                            } catch (e) {
                                console.log(`Stickara: Exception decrypting response from tab ${tabId}:`, e);
                                resolve(null);
                            }
                        } else {
                            // Otherwise, return the response as-is
                            resolve(response);
                        }
                    });
                } catch (e) {
                    console.log(`Stickara: Exception sending secure message to tab ${tabId}:`, e);
                    resolve(null);
                }
            });
        } catch (e) {
            console.log(`Stickara: Error in sendToTab for tab ${tabId}:`, e);
            // Return null instead of throwing to avoid breaking functionality
            return null;
        }
    }

    /**
     * Sets up a secure message listener
     * @param {Function} callback - The callback function(message, sender, sendResponse)
     */
    function setupMessageListener(callback) {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            // Check if this is a secure message
            if (message && message.type === 'secureMessage') {
                // Check if decryption is available
                if (!encryptionKey || !signingKey) {
                    console.log("Stickara: Encryption not available, cannot process secure message");
                    sendResponse({ error: 'Encryption not available' });
                    return true;
                }

                // Try to decrypt the message
                try {
                    decryptMessage(message)
                        .then(decryptedMessage => {
                            try {
                                // Call the callback with the decrypted message
                                return callback(decryptedMessage, sender);
                            } catch (e) {
                                console.log("Stickara: Error in message callback:", e);
                                return null;
                            }
                        })
                        .then(async response => {
                            // If the callback returned a response, try to encrypt it
                            if (response !== undefined) {
                                try {
                                    const secureResponse = await encryptMessage(response);
                                    sendResponse(secureResponse);
                                } catch (e) {
                                    console.log("Stickara: Error encrypting response:", e);
                                    sendResponse({ error: 'Error encrypting response' });
                                }
                            }
                        })
                        .catch(e => {
                            console.log("Stickara: Error handling secure message:", e);
                            sendResponse({ error: 'Error processing secure message' });
                        });
                } catch (e) {
                    console.log("Stickara: Exception handling secure message:", e);
                    sendResponse({ error: 'Exception processing secure message' });
                }

                // Return true to indicate we'll send a response asynchronously
                return true;
            }

            // Not a secure message, let other listeners handle it
            return false;
        });
    }

    // Return the public API
    return {
        initialize,
        encryptMessage,
        decryptMessage,
        sendToBackground,
        sendToTab,
        setupMessageListener
    };
})();

// Initialize the secure messaging system
window.StickaraSecureMessaging.initialize().catch(e => {
    console.error("Stickara: Error initializing Secure Messaging:", e);
});

console.log("Stickara: Secure Messaging System Loaded");
// --- END OF FILE secure-messaging.js ---
