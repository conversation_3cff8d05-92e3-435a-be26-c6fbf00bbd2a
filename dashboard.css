/* --- START OF FILE dashboard.css --- */
:root {
    /* --- Color Palette --- */
    --primary-color: #4caf50;
    --primary-color-hover: #388e3c;
    --primary-color-darker: #1b5e20;
    --primary-color-light: #e8f5e9; /* Light tint for backgrounds */

    /* Secondary Accent (Cool Gray/Subtle Blue) */
    --secondary-color: #607d8b; /* Blue Gray */
    --secondary-color-hover: #546e7a;
    --secondary-color-darker: #455a64;
    --secondary-color-light: #eceff1; /* Light tint for backgrounds */

    /* Danger/Delete */
    --danger-color: #f44336;
    --danger-color-hover: #d32f2f;
    --danger-color-darker: #b71c1c;
    --danger-color-light: #ffebee; /* Light tint */

    /* Neutral & Text Colors */
    --text-primary: #263238;   /* Darker Gray for main text */
    --text-secondary: #546e7a; /* Lighter <PERSON> for secondary text/details */
    --text-on-primary: #ffffff;
    --text-on-danger: #ffffff;
    --text-on-secondary: #ffffff; /* Text on dark secondary hover */

    --border-color: #cfd8dc;   /* Lighter border */
    --card-bg: #ffffff;
    --dashboard-bg: #f4f6f8; /* Slightly blue/gray cool background */
    --white: #ffffff;

    /* Status Colors */
    --success-color: #43a047;
    --error-color: #e53935;
    --info-color: var(--text-secondary);

    /* Shadows & Transitions */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-focus-ring: 0 0 0 3px rgba(76, 175, 80, 0.3); /* Primary color focus */

    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;

    --transition-speed: 0.2s; /* Kept for other transitions */
    --transition-func: ease-in-out;

    /* Using system fonts to avoid CSP issues */
    --Stickara-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* --- Base Styles --- */
html, body {
    margin: 0;
    padding: 0;
    font-family: var(--Stickara-font-family);
    background: linear-gradient(135deg, var(--dashboard-bg) 0%, #e8f4f8 100%);
    color: var(--text-primary);
    min-height: 100vh;
    line-height: 1.6;
}

.container {
    max-width: 1000px;
    margin: 20px auto;
    padding: 30px 40px;
    background-color: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg), 0 0 0 1px rgba(255, 255, 255, 0.1);
    overflow: visible;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* --- Header --- */
.dashboard-header {
    text-align: center;
    margin-bottom: 40px;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 30px;
    position: relative;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.05), rgba(96, 125, 139, 0.03));
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
    padding: 30px 20px;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 1px;
}

.header-content {
    margin-bottom: 25px;
}

.dashboard-header h1 {
    margin: 0 0 15px;
    font-size: 38px;
    color: var(--primary-color);
    font-weight: 700;
    letter-spacing: -0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.dashboard-icon {
    font-size: 32px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.dashboard-description {
    font-size: 17px;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
    font-weight: 400;
    line-height: 1.6;
}

/* Header Stats */
.header-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
    min-width: 80px;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.15);
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Sidebar Header */
.sidebar-header {
    padding: 20px 15px;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 15px;
}

.sidebar-header h2 {
    margin: 0 0 15px;
    font-size: 18px;
    color: var(--text-primary);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.add-notebook-btn {
    width: 100%;
    padding: 10px 15px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-hover));
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.add-notebook-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
    background: linear-gradient(135deg, var(--primary-color-hover), var(--primary-color-darker));
}

.btn-icon {
    font-size: 16px;
}

.btn-text {
    font-weight: 600;
}

/* Notebook buttons */
.notebook-button {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
    padding: 12px 15px;
    background: transparent;
    border: none;
    text-align: left;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: var(--border-radius-sm);
    margin-bottom: 2px;
}

.notebook-button:hover {
    background: linear-gradient(135deg, var(--primary-color-light), rgba(76, 175, 80, 0.08));
    transform: translateX(3px);
}

.notebook-button.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-hover));
    color: white;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.notebook-icon {
    font-size: 16px;
    flex-shrink: 0;
}

.notebook-name {
    font-weight: 500;
    flex-grow: 1;
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* --- Controls Area --- */
.controls {
    margin-bottom: 25px;
    display: flex;
    flex-direction: column;
    gap: 18px;
    overflow: visible; /* Keep this */
}

.search-filter-area {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
}

/* Input & Select Styling */
.search-filter-area input[type="text"],
.sort-area select {
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: 14px;
    background-color: var(--white);
    color: var(--text-primary);
    box-sizing: border-box;
    transition: border-color var(--transition-speed) var(--transition-func),
                box-shadow var(--transition-speed) var(--transition-func);
    flex-grow: 1;
    min-width: 180px;
}
.sort-area select {
    cursor: pointer;
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8"><path fill="%23546e7a" d="M1.41.59L6 5.17 10.59.59 12 2 6 8 0 2z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 12px 8px;
    padding-right: 35px;
}

.search-filter-area input[type="text"]:focus,
.sort-area select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: var(--shadow-focus-ring);
}

/* Checkbox Styling */
.search-filter-area input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    accent-color: var(--primary-color);
    vertical-align: middle;
    cursor: pointer;
}

.search-filter-area label.filter-label {
     font-size: 14px;
     color: var(--text-secondary);
     cursor: pointer;
     display: inline-flex;
     align-items: center;
     user-select: none;
     transition: color var(--transition-speed) var(--transition-func);
}
.search-filter-area label.filter-label:hover {
    color: var(--text-primary);
}

.sort-area {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    color: var(--text-secondary);
}

/* Default Hidden State - Target by ID */
/* Bulk Actions Container */
.bulk-actions-container {
    position: relative;
    height: 60px; /* Fixed height to prevent layout shifts */
    margin-bottom: 25px;
    pointer-events: none; /* Allow clicks to pass through the container */
}

#bulk-actions-bar {
    background-color: var(--primary-color-light);
    padding: 12px 18px;
    border-radius: var(--border-radius-md);
    display: flex !important; /* Force display flex */
    justify-content: space-between;
    align-items: center;
    gap: 15px;
    border: 1px solid #c8e6c9;
    position: absolute; /* Position absolutely within container */
    top: 0;
    left: 0;
    right: 0;
    z-index: 10; /* Keep for layering */

    /* Hide using opacity and visibility with transitions */
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(-15px) !important;
    pointer-events: none !important;
    transition: opacity 0.3s ease-in-out,
                transform 0.3s ease-in-out,
                visibility 0s linear 0.3s !important; /* Delay visibility hide */
}

/* Visible State - Target by ID */
#bulk-actions-bar.visible {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
    transition: opacity 0.3s ease-in-out,
                transform 0.3s ease-in-out,
                visibility 0s linear 0s !important; /* Make visible immediately */
    z-index: 100; /* Ensure it's above other elements */
}
/* *** END AGGRESSIVE DEBUGGING *** */


.bulk-actions span { /* Keep styling for content */
    font-weight: 600;
    color: var(--primary-color-darker);
    font-size: 14px;
    margin-right: auto;
}

/* Dropdown styling for Move To buttons */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 180px;
    background-color: var(--white);
    box-shadow: var(--shadow-md);
    border-radius: var(--border-radius-md);
    z-index: 1000;
    padding: 5px;
    border: 1px solid var(--border-color);
    max-height: 300px;
    overflow-y: auto;
}

/* Show dropdown when .show class is added */
.dropdown-content.show {
    display: block;
}

.dropdown-content button {
    display: block;
    width: 100%;
    text-align: left;
    padding: 8px 10px;
    border: none;
    background-color: var(--white);
    cursor: pointer;
    font-size: 14px;
    font-weight: normal;
    border-radius: var(--border-radius-sm);
    transition: background-color 0.2s ease;
    color: #333333 !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.dropdown-content button:hover {
    background-color: #e0f0ff !important;
    color: #0066cc !important;
    font-weight: 500;
}

/* Keep styling for select and buttons within .bulk-actions */
.bulk-actions select.export-format-select,
.bulk-actions select#bulk-export-format-select,
.bulk-actions select#highlights-export-format-select {
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: 14px;
    background-color: var(--white);
    color: var(--text-primary);
    box-sizing: border-box;
    cursor: pointer;
    margin-right: 10px;
    min-width: 150px;
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8"><path fill="%23546e7a" d="M1.41.59L6 5.17 10.59.59 12 2 6 8 0 2z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 12px 8px;
    padding-right: 35px;
    transition: border-color var(--transition-speed) var(--transition-func),
                box-shadow var(--transition-speed) var(--transition-func);
}
.bulk-actions select.export-format-select:focus,
.bulk-actions select#bulk-export-format-select:focus,
.bulk-actions select#highlights-export-format-select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: var(--shadow-focus-ring);
}
.bulk-actions select.export-format-select:disabled,
.bulk-actions select#bulk-export-format-select:disabled,
.bulk-actions select#highlights-export-format-select:disabled {
    background-color: #e0e0e0 !important;
    border-color: #e0e0e0 !important;
    color: #9e9e9e !important;
    cursor: not-allowed;
    opacity: 0.7;
}

/* --- Base Button Styling --- */
button {
    padding: 10px 18px;
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.4;
    transition: transform var(--transition-speed) var(--transition-func),
                box-shadow var(--transition-speed) var(--transition-func),
                background-color var(--transition-speed) var(--transition-func),
                color var(--transition-speed) var(--transition-func);
    box-shadow: var(--shadow-sm);
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    flex-shrink: 0;
    background-color: var(--secondary-color);
    color: var(--text-on-secondary);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}
button:focus-visible {
    outline: none;
    box-shadow: var(--shadow-focus-ring);
}

button:active {
    transform: translateY(0);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    filter: brightness(95%);
}

button:disabled {
    background-color: #bdbdbd !important;
    border-color: #bdbdbd !important;
    color: #757575 !important;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
    opacity: 0.6;
}

/* --- Specific Button Types --- */
button.danger {
    background-color: var(--danger-color);
    color: var(--text-on-danger);
}
button.danger:hover {
    background-color: var(--danger-color-hover);
    box-shadow: var(--shadow-md);
}
button.danger:active {
     background-color: var(--danger-color-darker);
     filter: none;
}

button.secondary {
    background-color: var(--white);
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
    box-shadow: none;
}
button.secondary:hover {
    background-color: var(--secondary-color-light);
    border-color: var(--secondary-color-hover);
    color: var(--secondary-color-darker);
    box-shadow: var(--shadow-sm);
}
button.secondary:active {
    background-color: #dce1e4;
    color: var(--secondary-color-darker);
    filter: none;
}

/* Compact button style */
button.compact {
    padding: 8px 12px;
    font-size: 13px;
    min-width: 80px;
}



/* --- Dashboard Results --- */
#dashboard-results {
    margin-top: 25px;
    font-size: 14px;
    min-height: 200px;
    position: relative;
    overflow: visible; /* Keep this */
}

/* Side-by-Side Comparison View */
#dashboard-results.side-by-side-view {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: space-between;
}

#dashboard-results.side-by-side-view .note-item {
    flex: 0 0 calc(50% - 10px);
    margin-bottom: 20px;
    max-width: calc(50% - 10px);
    box-sizing: border-box;
}

/* Comparison View Toggle Button */
.comparison-view-toggle button {
    display: flex;
    align-items: center;
    gap: 6px;
}

.comparison-view-toggle button .btn-icon {
    font-size: 16px;
}

.comparison-view-toggle button.active {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    border-color: var(--primary-color);
}



/* Loading / No Results Messages */
#dashboard-results .loading-message,
#dashboard-results .no-results {
    text-align: center;
    color: var(--text-secondary);
    padding: 50px 20px;
    font-style: italic;
    font-size: 16px;
    line-height: 1.6;
    background-color: var(--secondary-color-light);
    border-radius: var(--border-radius-md);
}
#dashboard-results .no-results.error {
    color: var(--error-color);
    background-color: var(--danger-color-light);
    font-weight: 500;
    font-style: normal;
}



/* Individual Note Item Card */
#dashboard-results .note-item {
    background: linear-gradient(135deg, var(--card-bg), rgba(255, 255, 255, 0.95));
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: 20px 24px;
    margin-bottom: 20px;
    display: flex;
    gap: 20px;
    align-items: flex-start;
    box-shadow: var(--shadow-sm), 0 0 0 1px rgba(255, 255, 255, 0.5);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(5px);
}

#dashboard-results .note-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

#dashboard-results .note-item:hover {
    transform: translateY(-3px) scale(1.01);
    box-shadow: var(--shadow-md), 0 8px 25px rgba(76, 175, 80, 0.15);
    border-color: var(--primary-color);
}

#dashboard-results .note-item:hover::before {
    transform: scaleX(1);
}

/* Global Note Styling */
#dashboard-results .note-item.global-note {
    border-left: 4px solid #3498db; /* Blue border for global notes */
    background-color: rgba(52, 152, 219, 0.05); /* Very light blue background */
}

/* Global Notes Button in Sidebar */
.notebook-button[data-notebook-id="GLOBAL"] .notebook-name {
    color: #3498db; /* Blue color for global notes text */
}



#dashboard-results .note-item input[type="checkbox"] {
    margin-top: 4px;
    flex-shrink: 0;
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: var(--primary-color);
}

#dashboard-results .note-content {
    flex-grow: 1;
    overflow: hidden;
}

/* Note URL Link */
#dashboard-results .note-url {
    color: var(--primary-color);
    font-weight: 600;
    cursor: pointer;
    display: block;
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-decoration: none;
    font-size: 16px;
    transition: color var(--transition-speed) var(--transition-func);
}
#dashboard-results .note-url:hover,
#dashboard-results .note-url:focus-visible {
    color: var(--primary-color-hover);
    text-decoration: underline;
    outline: none;
}

/* Note Details Section */
#dashboard-results .note-details {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 10px;
    line-height: 1.5;
}
#dashboard-results .note-details span {
    margin-right: 12px;
    display: inline-block;
    white-space: nowrap;
    margin-bottom: 3px;
}
#dashboard-results .note-details .detail-index { font-weight: 600; color: var(--text-primary); }
#dashboard-results .note-details .detail-tags {
    font-style: normal;
    background-color: var(--secondary-color-light);
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
    color: var(--secondary-color-darker);
}
#dashboard-results .note-details .detail-reminder {
    font-weight: 600;
    color: #ef6c00;
}
#dashboard-results .note-details .detail-saved {
  font-weight: 500;
  color: var(--text-secondary);
}
#dashboard-results .note-details span span[title="Globally Pinned"] {
    display: inline-block;
    margin-left: 5px;
    font-size: 1.1em;
    vertical-align: text-bottom;
    filter: none;
    color: var(--primary-color);
}

/* Note Snippet */
#dashboard-results .note-snippet {
    margin: 0;
    color: var(--text-primary);
    word-break: break-word;
    line-height: 1.6;
    max-height: 6.4em;
    overflow: hidden;
    position: relative;
}
#dashboard-results .note-snippet.potentially-long::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 75%;
    height: 1.6em;
    background: linear-gradient(to right, rgba(255, 255, 255, 0), var(--card-bg) 70%);
    pointer-events: none;
    transition: background var(--transition-speed) var(--transition-func);
}
#dashboard-results .note-item:hover .note-snippet.potentially-long::after {
    background: linear-gradient(to right, rgba(255, 255, 255, 0), var(--card-bg) 70%);
}

/* Enhanced Highlighted Text within Snippet */
#dashboard-results mark {
    background-color: #fff59d;
    color: #424242;
    padding: 0 4px;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    box-shadow: 0 0 0 1px rgba(66, 66, 66, 0.1); /* Subtle border */
    transition: background-color var(--transition-speed) var(--transition-func);
}
#dashboard-results mark:hover {
    background-color: #ffeb3b; /* More vibrant on hover */
}

/* --- Footer and Status Message --- */
footer {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

#dashboard-status-message {
    font-size: 14px;
    min-height: 1.5em;
    color: var(--info-color);
    font-weight: 500;
    transition: opacity 0.4s ease, color 0.3s ease, transform 0.3s ease;
    opacity: 0;
    transform: translateY(5px);
}
#dashboard-status-message.visible {
    opacity: 1;
    transform: translateY(0);
}
#dashboard-status-message.success { color: var(--success-color); }
#dashboard-status-message.error { color: var(--error-color); }
#dashboard-status-message.info { color: var(--info-color); }

/* --- Tab Navigation --- */
.dashboard-tabs {
    display: flex;
    margin-bottom: 25px;
    border-bottom: 2px solid var(--border-color);
    background: linear-gradient(135deg, var(--secondary-color-light), rgba(236, 239, 241, 0.8));
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
    padding: 8px 8px 0;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dashboard-tabs::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-color), transparent);
}

.tab-btn {
    padding: 14px 28px;
    background-color: transparent;
    border: none;
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-secondary);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-right: 4px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(5px);
}

.tab-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.1), transparent);
    transition: left 0.5s ease;
}

.tab-btn:hover::before {
    left: 100%;
}

.tab-btn::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 2px 2px 0 0;
}

.tab-btn:hover {
    color: var(--primary-color);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.15);
}

.tab-btn:hover::after {
    transform: scaleX(0.6);
}

.tab-btn.active {
    color: var(--primary-color);
    background: linear-gradient(135deg, white, rgba(255, 255, 255, 0.95));
    font-weight: 600;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08), 0 4px 16px rgba(76, 175, 80, 0.2);
    transform: translateY(-2px);
}

.tab-btn.active::after {
    transform: scaleX(1);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeInSlide 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInSlide {
    from {
        opacity: 0;
        transform: translateY(10px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* --- Highlights Specific Styles --- */
.highlight-filters {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
    background-color: var(--secondary-color-light);
    padding: 10px;
    border-radius: var(--border-radius-md);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
}

.filter-dropdown {
    display: flex;
    flex-direction: column;
    gap: 3px;
    margin-right: 10px;
}

.filter-dropdown label {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
}

.filter-select {
    padding: 6px 10px;
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
    background-color: var(--white);
    font-size: 13px;
    min-width: 120px;
    cursor: pointer;
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>');
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 12px;
    padding-right: 28px;
}

.filter-select:hover {
    border-color: var(--primary-color-light);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(128, 90, 213, 0.2);
}

/* Color and Style Select Styling */
.color-select option,
.style-select option {
    padding-left: 25px;
}

/* Color indicators for select options */
.filter-group-inline {
    display: inline-flex;
    align-items: center;
    margin-right: 15px;
}

.filter-group-inline label {
    margin-right: 8px;
    font-size: 14px;
    color: var(--text-secondary);
}

/* Filter dropdown styling */
.filter-dropdown {
    position: relative;
    margin-right: 10px;
}

.filter-dropdown select {
    font-size: 14px;
    padding: 6px 10px;
}

/* Make emojis in dropdowns look better */
.filter-dropdown select option {
    font-size: 14px;
    padding: 5px;
}

/* Add padding to the select elements to make room for the icons */
.color-select,
.style-select {
    padding-left: 30px;
}

/* Color Filter Options */
.color-filter-options {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
    padding: 3px;
}

.color-option {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.color-option:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    z-index: 2;
}

.color-option.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(128, 90, 213, 0.3), 0 2px 4px rgba(0, 0, 0, 0.2);
    transform: scale(1.05);
}

.color-option.selected::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: rgba(0, 0, 0, 0.8);
    font-size: 12px;
    font-weight: bold;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
}

.color-yellow {
    background-color: #fff352;
    background-image: linear-gradient(135deg, #fff352, #ffec00);
}
.color-pink {
    background-color: #ffafcc;
    background-image: linear-gradient(135deg, #ffafcc, #ff8fab);
}
.color-blue {
    background-color: #a0c4ff;
    background-image: linear-gradient(135deg, #a0c4ff, #7da6ff);
}
.color-green {
    background-color: #b9fbc0;
    background-image: linear-gradient(135deg, #b9fbc0, #98f5a1);
}
.color-purple {
    background-color: #d6bfff;
    background-image: linear-gradient(135deg, #d6bfff, #c4a0ff);
}
.color-all {
    background: conic-gradient(
        #fff352 0deg 72deg,
        #ffafcc 72deg 144deg,
        #a0c4ff 144deg 216deg,
        #b9fbc0 216deg 288deg,
        #d6bfff 288deg 360deg
    );
}

/* Style Filter Options */
.style-filter-options {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
    padding: 3px;
}

.style-option {
    padding: 5px 8px;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
    font-size: 12px;
    background-color: var(--white);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
}

.style-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color);
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1;
}

.style-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.style-option:hover::before {
    opacity: 0.05;
}

.style-option.selected {
    background-color: var(--primary-color-light);
    border-color: var(--primary-color);
    color: var(--primary-color-darker);
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(128, 90, 213, 0.2);
    transform: translateY(-1px);
}

.style-option.selected::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
}

.style-color {
    background-color: #fff352;
    color: rgba(0, 0, 0, 0.7);
}
.style-underline {
    text-decoration: underline;
    text-decoration-thickness: 2px;
    text-underline-offset: 3px;
}
.style-wavy {
    text-decoration: wavy underline;
    text-decoration-thickness: 2px;
    text-underline-offset: 3px;
}
.style-border {
    border: 2px solid #000 !important;
    padding: 6px 12px !important;
}
.style-strikethrough {
    text-decoration: line-through;
    text-decoration-thickness: 2px;
}
.style-all {
    font-weight: 600;
    background-image: linear-gradient(135deg, rgba(128, 90, 213, 0.1), rgba(128, 90, 213, 0.05));
}

.highlights-area-wrapper {
    display: none;
    margin-top: 20px;
}

#highlights-bulk-actions-bar {
    background-color: var(--primary-color-light);
    padding: 12px 18px;
    border-radius: var(--border-radius-md);
    display: flex !important;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
    border: 1px solid #c8e6c9;
    position: absolute; /* Position absolutely within container */
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;

    /* Hide using opacity and visibility with transitions */
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(-15px) !important;
    pointer-events: none !important;
    transition: opacity 0.3s ease-in-out,
                transform 0.3s ease-in-out,
                visibility 0s linear 0.3s !important;
}

#highlights-bulk-actions-bar.visible {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
    transition: opacity 0.3s ease-in-out,
                transform 0.3s ease-in-out,
                visibility 0s linear 0s !important;
    z-index: 100; /* Ensure it's above other elements */
}

.highlight-item {
    display: flex;
    padding: 18px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    margin-bottom: 15px;
    background-color: var(--white);
    transition: all var(--transition-speed) var(--transition-func);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    cursor: grab;
}

/* Highlight with note */
.highlight-item.has-note {
    border-left: 3px solid #ffd54f;
}

.highlight-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: var(--primary-color-light);
    opacity: 0.7;
}

.highlight-item:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    border-color: var(--primary-color-light);
}

.highlight-item.dragging {
    opacity: 0.6;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    cursor: grabbing;
    transform: scale(1.02);
    z-index: 100;
}

.highlight-checkbox-container {
    flex: 0 0 30px;
    display: flex;
    align-items: flex-start;
    padding-top: 3px;
}

.highlight-checkbox {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
    cursor: pointer;
    border-radius: 3px;
}

.highlight-content {
    flex: 1;
    margin-left: 5px;
}

.highlight-text {
    font-size: 16px;
    margin-bottom: 12px;
    line-height: 1.6;
    word-break: break-word;
    padding: 8px 12px;
    border-radius: var(--border-radius-sm);
    position: relative;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.highlight-text.color-yellow {
    background-color: #fff352;
    box-shadow: 0 1px 3px rgba(255, 243, 82, 0.5);
}
.highlight-text.color-pink {
    background-color: #ffafcc;
    box-shadow: 0 1px 3px rgba(255, 175, 204, 0.5);
}
.highlight-text.color-blue {
    background-color: #a0c4ff;
    box-shadow: 0 1px 3px rgba(160, 196, 255, 0.5);
}
.highlight-text.color-green {
    background-color: #b9fbc0;
    box-shadow: 0 1px 3px rgba(185, 251, 192, 0.5);
}
.highlight-text.color-purple {
    background-color: #d6bfff;
    box-shadow: 0 1px 3px rgba(214, 191, 255, 0.5);
}

.highlight-text.style-underline {
    text-decoration: underline;
    background-color: transparent;
    box-shadow: none;
    border: 1px dashed var(--border-color);
}
.highlight-text.style-wavy {
    text-decoration: wavy underline;
    background-color: transparent;
    box-shadow: none;
    border: 1px dashed var(--border-color);
}
.highlight-text.style-border-thick {
    border: 2px solid #000;
    padding: 8px 12px;
    background-color: transparent;
    box-shadow: none;
}
.highlight-text.style-strikethrough {
    text-decoration: line-through;
    background-color: transparent;
    box-shadow: none;
    border: 1px dashed var(--border-color);
    opacity: 0.8;
}

/* Privacy highlight styles */
.highlight-text.style-blur {
    color: transparent;
    text-shadow: 0 0 8px rgba(0,0,0,0.5);
    background-color: rgba(0,0,0,0.05);
    border: 1px dashed var(--border-color);
    user-select: none;
}

/* Ensure links inside blur highlights are also blurred */
.highlight-text.style-blur a,
.highlight-text.style-blur a:link,
.highlight-text.style-blur a:visited,
.highlight-text.style-blur a:hover,
.highlight-text.style-blur a:active {
    color: transparent;
    text-shadow: 0 0 8px rgba(0,0,0,0.5);
    text-decoration: none;
    pointer-events: none;
}

.highlight-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--text-secondary);
    background-color: var(--secondary-color-light);
    padding: 8px 12px;
    border-radius: var(--border-radius-sm);
    margin-top: 5px;
}

.highlight-url {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 10px;
}

.highlight-url a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
}

.highlight-url a::before {
    content: '🔗';
    margin-right: 5px;
    font-size: 14px;
}

.highlight-url a:hover {
    text-decoration: underline;
    color: var(--primary-color-darker);
}

.highlight-notebook {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    margin: 0 15px;
    font-weight: 500;
    color: var(--primary-color);
    background-color: var(--primary-color-lighter);
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.highlight-date {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
}

.highlight-date::before {
    content: '🕒';
    margin-right: 5px;
    font-size: 14px;
}

/* Highlight note */
.highlight-note {
    margin-top: 12px;
    padding: 10px 15px;
    background-color: #fff9c4;
    border: 1px solid #ffd54f;
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    line-height: 1.5;
    position: relative;
}

.highlight-note .note-label {
    font-weight: 600;
    color: #f57c00;
    margin-right: 5px;
}

.highlight-note .note-content {
    color: #333;
}

.highlight-actions {
    display: flex;
    gap: 5px;
    margin-left: 15px;
}

.highlight-action-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 5px 8px;
    font-size: 14px;
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.highlight-action-btn:hover {
    color: var(--primary-color);
    background-color: var(--primary-color-light);
    transform: scale(1.1);
}

.highlight-action-btn.delete:hover {
    color: var(--error-color);
    background-color: var(--error-color-light);
}

/* Export Format Select Styles */
.export-format-select {
    position: relative;
    padding: 10px 30px 10px 15px !important;
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>');
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    font-weight: 500;
    color: var(--text-color);
    border-radius: var(--border-radius-md) !important;
}

.export-format-select:hover {
    border-color: var(--primary-color-light);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.export-format-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(128, 90, 213, 0.2);
}

.export-format-select option {
    padding: 10px;
    font-weight: normal;
}

/* --- Pagination Styles --- */
.pagination-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    padding: 10px 0;
    border-top: 1px solid var(--border-color);
}

.pagination-btn {
    padding: 8px 15px;
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: 14px;
    transition: all var(--transition-speed) var(--transition-func);
    margin: 0 5px;
    color: var(--text-primary); /* Ensure text color is visible */
    font-weight: 500; /* Make text slightly bolder */
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--primary-color-light);
    border-color: var(--primary-color);
    color: var(--primary-color-darker); /* Darker text on hover */
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    color: var(--text-secondary); /* Lighter color for disabled state */
}

#page-indicator, #highlights-page-indicator {
    margin: 0 15px;
    font-size: 14px;
    color: var(--text-secondary);
}

#page-size-select, #highlights-page-size-select {
    margin-left: 15px;
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--white);
    font-size: 14px;
    cursor: pointer;
}

/* --- Select All Checkbox --- */
.select-all-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    background-color: var(--secondary-color-light);
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
    margin-bottom: 10px;
    border: 1px solid var(--border-color);
}

.select-all-label {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
}

.select-all-label input[type="checkbox"] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

#notes-count-display, #highlights-count-display {
    font-size: 14px;
    color: var(--text-secondary);
}

/* --- Add to dashboard.css --- */

/* Layout */
.dashboard-wrapper {
    display: flex;
    gap: 25px; /* Space between sidebar and main content */
    max-width: 1200px; /* Adjust max width for wider layout */
    margin: 30px auto;
    padding: 0 20px; /* Add horizontal padding to wrapper */
}

.notebook-sidebar {
    width: 200px; /* Fixed width sidebar */
    flex-shrink: 0;
    background-color: var(--secondary-color-lighter); /* Sidebar background */
    padding: 20px 15px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    height: fit-content; /* Make sidebar only as tall as needed */
    position: sticky; /* Make sidebar sticky */
    top: 30px; /* Adjust sticky top position */
    align-self: flex-start; /* Align sidebar to the top */
    transition: all 0.3s ease; /* Smooth transitions for drag states */
    z-index: 10; /* Ensure sidebar stays above content during drag */
}
.notebook-sidebar h3 {
    text-align: left;
    font-size: 16px;
    margin-bottom: 15px;
    color: var(--secondary-color-darker);
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.notebook-actions {
    margin-bottom: 15px;
    text-align: center;
}
.notebook-actions button {
    width: 100%; /* Full width add button */
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    font-weight: 600;
}
.notebook-actions button:hover {
     background-color: var(--primary-color-hover);
}


#notebook-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

#notebook-list li {
    margin-bottom: 4px; /* Space between items */
    position: relative; /* For rename/delete buttons */
}

.notebook-button,
.notebook-controls button /* Target controls within notebook list items */
 {
    width: 100%;
    text-align: left;
    padding: 8px 12px;
    background-color: transparent;
    color: var(--secondary-color-darker);
    border: none;
    border-radius: var(--border-radius-md);
    font-size: 14px;
    cursor: pointer;
    display: flex; /* Use flex for alignment */
    justify-content: space-between; /* Push controls to right */
    align-items: center;
    transition: background-color var(--transition-speed) var(--transition-func),
                color var(--transition-speed) var(--transition-func),
                box-shadow var(--transition-speed) var(--transition-func);
}

 .notebook-button .notebook-name {
    flex-grow: 1; /* Allow name to take space */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 5px; /* Space before controls */
}


.notebook-button:hover {
    background-color: var(--secondary-color-light);
    color: var(--text-primary);
    box-shadow: var(--shadow-sm); /* Subtle shadow on hover */
    transform: none; /* No translate on hover */
}
.notebook-button.active {
    background-color: var(--primary-color-light);
    color: var(--primary-color-darker);
    font-weight: 600;
}
.notebook-button.active:hover {
     background-color: var(--primary-color-light); /* Keep active color on hover */
     color: var(--primary-color-darker);
}
.notebook-button:focus-visible {
    outline: none;
    box-shadow: var(--shadow-focus-ring);
}

 .notebook-controls {
     display: flex;
     gap: 2px;
     opacity: 0; /* Hidden by default */
     transition: opacity 0.15s ease-in-out;
 }

 #notebook-list li:hover .notebook-controls {
     opacity: 1; /* Show controls on list item hover */
 }

 .notebook-controls button {
    width: auto; /* Auto width for small icons */
    padding: 2px 4px; /* Minimal padding */
    font-size: 12px;
    line-height: 1;
    color: var(--secondary-color);
    background-color: transparent;
    box-shadow: none;
    border-radius: var(--border-radius-sm);
 }
 .notebook-controls button:hover {
    background-color: #d1d5db; /* Slightly darker hover */
    color: var(--text-primary);
 }


.main-content {
    flex-grow: 1;
    min-width: 0; /* Allow content to shrink */
    /* Container inside already has styling */
}

/* Adjust existing container if needed (likely not necessary if using wrapper) */
/* .container { max-width: none; margin: 0; padding: 25px 35px; } */

/* --- Styles for Drag and Drop --- */
 #dashboard-results .note-item {
    cursor: grab; /* Indicate draggable */
    user-select: none; /* Prevent text selection during drag */
    position: relative; /* Needed for the dragging pseudo-element or class */
 }
 #dashboard-results .note-item.dragging {
    opacity: 0.5;
    border: 2px dashed var(--primary-color);
    cursor: grabbing;
 }
 /* Style for when dragging OVER a notebook button */
.notebook-button.drag-over {
    background-color: var(--primary-color); /* Highlight */
    color: var(--text-on-primary);
    outline: 2px solid var(--primary-color-darker);
    outline-offset: -2px;
}
.notebook-button[data-notebook-id="ALL"].drag-over { /* Don't highlight 'All Notes' */
    background-color: transparent;
    outline: none;
}

 /* Style for Drop Target Overlay */
.drop-overlay {
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background-color: rgba(52, 211, 153, 0.1); /* Light primary overlay */
     border: 3px dashed var(--primary-color);
     display: flex;
     justify-content: center;
     align-items: center;
     font-size: 1.2em;
     font-weight: 600;
     color: var(--primary-color-darker);
     border-radius: var(--border-radius-md);
     pointer-events: none; /* Ignore mouse events on overlay */
     opacity: 0;
     visibility: hidden;
     z-index: 100; /* Ensure it's above note content */
     transition: opacity 0.2s ease-in-out, visibility 0s linear 0.2s;
 }
body.dragging .drop-overlay.visible {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.2s ease-in-out, visibility 0s linear 0s;
}

/* Enhanced drag-and-drop styles */
.notebook-sidebar.drag-active {
    background-color: rgba(76, 175, 80, 0.05); /* Light green tint */
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(76, 175, 80, 0.3);
    transform: scale(1.02); /* Slightly larger during drag */
}

.notebook-sidebar.enhanced-visibility {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    z-index: 1000 !important;
    background-color: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border: 3px solid var(--primary-color);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: pulseGlow 2s infinite;
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(76, 175, 80, 0.4);
    }
    50% {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 30px rgba(76, 175, 80, 0.7);
    }
}

/* Enhanced drop zone indicators */
.notebook-button.drag-over {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-hover)) !important;
    color: var(--text-on-primary) !important;
    outline: 3px solid var(--primary-color-darker) !important;
    outline-offset: -3px !important;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.5);
    animation: dragOverPulse 1s infinite;
}

@keyframes dragOverPulse {
    0%, 100% {
        transform: scale(1.05);
    }
    50% {
        transform: scale(1.08);
    }
}

/* Visual feedback for draggable items */
#dashboard-results .note-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md), 0 4px 12px rgba(76, 175, 80, 0.2);
    border-left: 4px solid var(--primary-color);
}

#dashboard-results .note-item.dragging {
    opacity: 0.7;
    border: 2px dashed var(--primary-color);
    cursor: grabbing;
    transform: rotate(2deg) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Auto-scroll indicators */
body.dragging-note::before,
body.dragging-note::after {
    content: '';
    position: fixed;
    left: 0;
    right: 0;
    height: 50px;
    background: linear-gradient(to bottom, rgba(76, 175, 80, 0.3), transparent);
    pointer-events: none;
    z-index: 999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

body.dragging-note::before {
    top: 0;
    background: linear-gradient(to bottom, rgba(76, 175, 80, 0.3), transparent);
}

body.dragging-note::after {
    bottom: 0;
    background: linear-gradient(to top, rgba(76, 175, 80, 0.3), transparent);
}

/* Show scroll indicators when near edges */
body.dragging-note.near-top::before,
body.dragging-note.near-bottom::after {
    opacity: 1;
}

/* Mobile responsiveness for enhanced drag features */
@media (max-width: 768px) {
    .notebook-sidebar.enhanced-visibility {
        width: 180px;
        top: 10px !important;
        right: 10px !important;
    }

    .notebook-sidebar.drag-active {
        transform: scale(1.01); /* Smaller scale on mobile */
    }

    /* Adjust auto-scroll threshold for mobile */
    body.dragging-note::before,
    body.dragging-note::after {
        height: 40px; /* Smaller scroll zones on mobile */
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .notebook-sidebar,
    .notebook-button,
    #dashboard-results .note-item {
        transition: none !important;
        animation: none !important;
    }

    .notebook-sidebar.enhanced-visibility {
        animation: none !important;
    }

    @keyframes pulseGlow,
    @keyframes dragOverPulse {
        0%, 100% { transform: none; }
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .notebook-sidebar.drag-active {
        border: 3px solid var(--primary-color);
        background-color: var(--card-bg);
    }

    .notebook-button.drag-over {
        outline: 4px solid var(--primary-color-darker) !important;
        background-color: var(--primary-color) !important;
    }
}


/* --- Modal Styles --- */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1001; /* Sit on top */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgba(0,0,0,0.5); /* Black w/ opacity */
  align-items: center;
  justify-content: center;
  padding: 20px;
}
.modal-content {
  background-color: #fefefe;
  margin: auto;
  padding: 25px;
  border: 1px solid #888;
  width: 80%;
  max-width: 400px; /* Max width */
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.modal-content h4 {
    text-align: center; margin: 0; color: var(--primary-color-darker); font-size: 1.1em;
}
.modal-content p {
    margin: 5px 0;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-secondary);
}


.modal-content input[type="text"] {
     width: 100%;
     padding: 10px;
     border: 1px solid var(--border-color);
     border-radius: var(--border-radius-md);
     font-size: 1em;
}
 .modal-content input[type="text"]:focus {
     border-color: var(--primary-color);
     outline: none;
     box-shadow: var(--shadow-focus-ring);
}
 .modal-content button {
     width: 100%;
     background-color: var(--primary-color);
     color: var(--text-on-primary);
     font-weight: 600;
}
 .modal-content button:hover {
      background-color: var(--primary-color-hover);
}
 .close-button {
   color: var(--text-secondary);
   position: absolute;
   top: 10px;
   right: 15px;
   font-size: 28px;
   font-weight: bold;
   cursor: pointer;
   transition: color 0.2s ease;
 }
 .close-button:hover,
 .close-button:focus {
   color: var(--text-primary);
   text-decoration: none;
 }

/* --- Content View Modal Styles --- */
.content-view-modal-content {
  max-width: 900px; /* Reduced from 1000px for better proportions */
  width: 80%; /* Reduced from 85% for smaller overall size */
  max-height: 85vh; /* Reduced to ensure actions section fits */
  min-height: 60vh; /* Reduced to accommodate actions section */
  overflow: hidden; /* Prevent overflow issues */
  display: flex;
  flex-direction: column;
  animation: modalFadeIn 0.2s ease-out;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border-radius: var(--border-radius-lg, 8px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  position: relative;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modalFadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

.modal.closing {
  animation: modalFadeOut 0.2s ease-in forwards;
}

/* Header Section */
.content-view-header {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  background: linear-gradient(to right, var(--primary-color-light, #e8f5e9), transparent);
  border-radius: var(--border-radius-lg, 8px) var(--border-radius-lg, 8px) 0 0;
}

.content-view-type-indicator {
  margin-right: 15px;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color-light, #e8f5e9);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.content-view-modal-content h4 {
  font-size: 20px;
  margin: 0;
  flex-grow: 1;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-view-modal-content .close-button {
  position: relative;
  top: auto;
  right: auto;
  font-size: 28px;
  margin-left: 10px;
  transition: all 0.2s ease;
}

.content-view-modal-content .close-button:hover {
  transform: scale(1.1);
  color: var(--primary-color);
}

/* Metadata Section */
.content-view-metadata {
  margin: 0;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-light, #f9f9f9);
}

.content-view-url {
  font-size: 14px;
  margin-bottom: 12px;
  word-break: break-all;
  display: flex;
  align-items: center;
}

.content-view-url::before {
  content: "🔗";
  margin-right: 8px;
  font-size: 16px;
}

.content-view-url a {
  color: var(--primary-color);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  transition: color 0.2s ease;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.content-view-url a:hover {
  color: var(--primary-color-hover);
  text-decoration: underline;
}

.content-view-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 13px;
  color: var(--text-secondary);
}

.content-view-details span {
  background-color: var(--white);
  padding: 5px 10px;
  border-radius: var(--border-radius-sm, 4px);
  border: 1px solid var(--border-color-light, #eee);
  display: inline-flex;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Content Section */
.content-view-body-container {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto; /* Allow container to grow and shrink */
  min-height: 0; /* Important for flex child to respect parent's height */
  position: relative;
  overflow: hidden; /* Prevent content from overflowing */
}

.content-view-body-toolbar {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  border-bottom: 1px solid var(--border-color-light, #eee);
  background-color: var(--white);
  flex-wrap: nowrap;
  gap: 6px;
  overflow: hidden;
  justify-content: space-between;
  min-height: 44px;
}

/* Specific styles for copy and edit buttons */
#content-view-copy-btn,
#content-view-edit-btn {
  font-size: 14px;
  width: 32px;
  height: 32px;
  min-width: 32px;
  padding: 0;
  margin-left: 4px;
}

.icon-button {
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  padding: 4px;
  border-radius: var(--border-radius-sm, 4px);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  width: 32px;
  height: 32px;
  min-width: 32px;
  flex-shrink: 0;
}

.icon-button:hover {
  background-color: var(--primary-color-light, #e8f5e9);
  color: var(--primary-color);
}

.content-view-zoom-controls {
  display: flex;
  align-items: center;
  margin-left: 0;
  gap: 0;
  font-size: 13px;
  background-color: var(--background-light, #f9f9f9);
  border-radius: var(--border-radius-sm, 4px);
  padding: 0;
  border: 1px solid var(--border-color-light, #eee);
  width: 100px; /* Fixed width */
  justify-content: space-between;
  overflow: hidden;
}

.content-view-zoom-controls .icon-button {
  padding: 0;
  font-size: 14px;
  width: 28px;
  height: 28px;
  min-width: 28px;
  border-radius: 0;
  font-weight: bold;
}

#content-view-zoom-level {
  font-size: 13px;
  color: var(--text-secondary);
  width: 44px;
  text-align: center;
  margin: 0;
  padding: 0;
  flex-shrink: 0;
}

.content-view-body {
  padding: 30px; /* Reduced padding to fit better */
  background-color: #ffffff; /* Pure white for best readability */
  flex: 1 1 auto; /* Allow flexible sizing */
  overflow-y: auto;
  line-height: 1.8; /* Slightly reduced from 1.9 */
  font-size: 17px; /* Base font size - will be controlled by zoom */
  transition: font-size 0.2s ease, line-height 0.2s ease;
  position: relative;
  border: none;
  margin: 0;
  /* Remove fixed heights to allow flexible sizing */
  min-height: 200px; /* Minimum readable height */
  max-height: calc(85vh - 200px); /* Ensure space for header and actions */
  /* Enhanced reading experience */
  color: #2c3e50; /* Darker text for better contrast */
  word-spacing: 0.05em;
  letter-spacing: 0.01em;
  font-family: 'Georgia', 'Times New Roman', serif; /* Better reading font */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Style for highlight content */
.content-view-body .highlight-text {
  padding: 35px; /* Increased for better spacing */
  margin: 25px 0; /* Better vertical spacing */
  border-radius: 12px; /* More rounded for modern look */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  position: relative;
  /* Enhanced highlight styling for better reading */
  background: linear-gradient(135deg, #fff9e6, #fff3d3); /* Warmer, more readable highlight */
  border-left: 6px solid #f39c12; /* Warmer accent color */
  font-size: 19px; /* Larger for highlights */
  line-height: 2.0; /* More spacious line height */
  font-weight: 400;
  color: #2c3e50; /* Consistent text color */
}

/* Add quotation marks to highlight text */
.content-view-body .highlight-text::before {
  content: """;
  font-size: 40px;
  color: rgba(0, 0, 0, 0.1);
  position: absolute;
  top: -5px;
  left: 5px;
}

.content-view-body .highlight-text::after {
  content: """;
  font-size: 40px;
  color: rgba(0, 0, 0, 0.1);
  position: absolute;
  bottom: -25px;
  right: 5px;
}

/* Style for note content */
.content-view-body .note-content {
  white-space: pre-wrap;
  padding: 35px; /* Increased for better spacing */
  /* Enhanced note content styling */
  background: #fafbfc; /* Light, clean background */
  border-radius: 12px; /* Consistent with highlights */
  border: 1px solid #e1e8ed; /* Subtle border */
  margin: 25px 0;
  font-size: 18px; /* Consistent with main text */
  line-height: 1.9; /* Consistent line height */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  color: #2c3e50; /* Consistent text color */
}

/* Style for images in notes */
.content-view-body img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius-sm, 4px);
  margin: 10px 0;
}

/* Actions Section */
.content-view-actions {
  display: flex !important; /* Force visibility */
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px; /* Reduced padding to save space */
  border-top: 1px solid var(--border-color);
  background-color: var(--background-light, #f9f9f9);
  border-radius: 0 0 var(--border-radius-lg, 8px) var(--border-radius-lg, 8px);
  /* Proper positioning at bottom of modal */
  flex-shrink: 0; /* Prevent shrinking */
  margin-top: auto; /* Push to bottom */
  position: relative;
  z-index: 10; /* Reasonable z-index */
  height: 60px; /* Fixed height for consistent layout */
  box-sizing: border-box;
}

.content-view-navigation {
  display: flex !important; /* Ensure navigation is always visible */
  gap: 8px;
  align-items: center;
}

/* Optional: Add a subtle indicator between navigation buttons */
.content-view-navigation::after {
  content: '';
  width: 1px;
  height: 20px;
  background-color: var(--border-color);
  margin: 0 4px;
  opacity: 0.3;
}

.nav-button {
  background: #ffffff;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm, 4px);
  padding: 6px 12px; /* Reduced padding to fit better */
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px; /* Ensure minimum width for readability */
  height: 32px; /* Fixed height for consistency */
  box-sizing: border-box;
}

.nav-button:hover:not(:disabled) {
  background-color: var(--primary-color-light, #e8f5e9);
  color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f8f9fa;
  color: #adb5bd;
  border-color: #e9ecef;
}

.nav-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.content-view-main-actions {
  display: flex;
  gap: 10px;
}

.content-view-main-actions button {
  width: auto;
  padding: 10px 20px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.content-view-main-actions button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* --- Enhanced Reading Window Improvements --- */

/* Responsive design for content view modal (reading window) */
@media (max-width: 1200px) {
  .content-view-modal-content {
    max-width: 800px;
    width: 85%;
    max-height: 80vh; /* Reduced to ensure actions section fits */
  }

  .content-view-body {
    padding: 25px;
    font-size: 16px;
    line-height: 1.7;
    max-height: calc(80vh - 180px); /* Adjust for smaller modal */
  }
}

@media (max-width: 768px) {
  .content-view-modal-content {
    max-width: 95%;
    width: 95%;
    max-height: 85vh; /* Reduced to prevent overflow */
    min-height: 50vh;
  }

  .content-view-body {
    padding: 20px;
    font-size: 15px;
    line-height: 1.6;
    max-height: calc(85vh - 160px); /* Ensure actions section fits */
    min-height: 150px;
  }

  .content-view-body .highlight-text {
    padding: 20px;
    font-size: 16px;
    line-height: 1.7;
  }

  .content-view-body .note-content {
    padding: 20px;
    font-size: 15px;
    line-height: 1.6;
  }

  .content-view-actions {
    padding: 10px 15px; /* Reduced padding on mobile */
    height: 50px; /* Smaller height on mobile */
  }

  .nav-button {
    padding: 4px 8px;
    font-size: 12px;
    min-width: 70px;
    height: 28px;
  }
}

/* Enhanced focus states for better accessibility in reading window */
.content-view-body:focus-within {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Improved text selection styling in reading window */
.content-view-body ::selection {
  background-color: rgba(76, 175, 80, 0.25);
  color: var(--text-primary);
}

/* Better spacing for mixed content (notes with highlights) in reading window */
.content-view-body .highlight-text + .note-content,
.content-view-body .note-content + .highlight-text {
  margin-top: 25px;
}

/* Enhanced scrollbar styling for reading window */
.content-view-body::-webkit-scrollbar {
  width: 8px;
}

.content-view-body::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.content-view-body::-webkit-scrollbar-thumb {
  background: rgba(76, 175, 80, 0.4);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.content-view-body::-webkit-scrollbar-thumb:hover {
  background: rgba(76, 175, 80, 0.6);
}

/* Smooth scrolling for better reading experience */
.content-view-body {
  scroll-behavior: smooth;
}

/* Smooth transitions for zoom functionality in reading window */
.content-view-body * {
  transition: font-size 0.2s ease, line-height 0.2s ease;
}

/* Zoom level classes for content view body */
.content-view-body.zoom-75 { font-size: 13px; }
.content-view-body.zoom-80 { font-size: 14px; }
.content-view-body.zoom-90 { font-size: 15px; }
.content-view-body.zoom-100 { font-size: 17px; } /* Default */
.content-view-body.zoom-110 { font-size: 19px; }
.content-view-body.zoom-125 { font-size: 21px; }
.content-view-body.zoom-150 { font-size: 25px; }
.content-view-body.zoom-175 { font-size: 30px; }
.content-view-body.zoom-200 { font-size: 34px; }

/* Zoom adjustments for highlight text */
.content-view-body.zoom-75 .highlight-text { font-size: 14px; }
.content-view-body.zoom-80 .highlight-text { font-size: 15px; }
.content-view-body.zoom-90 .highlight-text { font-size: 16px; }
.content-view-body.zoom-100 .highlight-text { font-size: 18px; }
.content-view-body.zoom-110 .highlight-text { font-size: 20px; }
.content-view-body.zoom-125 .highlight-text { font-size: 22px; }
.content-view-body.zoom-150 .highlight-text { font-size: 26px; }
.content-view-body.zoom-175 .highlight-text { font-size: 31px; }
.content-view-body.zoom-200 .highlight-text { font-size: 35px; }

/* Zoom adjustments for note content */
.content-view-body.zoom-75 .note-content { font-size: 13px; }
.content-view-body.zoom-80 .note-content { font-size: 14px; }
.content-view-body.zoom-90 .note-content { font-size: 15px; }
.content-view-body.zoom-100 .note-content { font-size: 17px; }
.content-view-body.zoom-110 .note-content { font-size: 19px; }
.content-view-body.zoom-125 .note-content { font-size: 21px; }
.content-view-body.zoom-150 .note-content { font-size: 25px; }
.content-view-body.zoom-175 .note-content { font-size: 30px; }
.content-view-body.zoom-200 .note-content { font-size: 34px; }

/* Zoom adjustments for headings */
.content-view-body.zoom-75 h1 { font-size: 21px; }
.content-view-body.zoom-75 h2 { font-size: 18px; }
.content-view-body.zoom-75 h3 { font-size: 16px; }
.content-view-body.zoom-75 h4 { font-size: 14px; }

.content-view-body.zoom-80 h1 { font-size: 22px; }
.content-view-body.zoom-80 h2 { font-size: 19px; }
.content-view-body.zoom-80 h3 { font-size: 17px; }
.content-view-body.zoom-80 h4 { font-size: 15px; }

.content-view-body.zoom-90 h1 { font-size: 25px; }
.content-view-body.zoom-90 h2 { font-size: 22px; }
.content-view-body.zoom-90 h3 { font-size: 19px; }
.content-view-body.zoom-90 h4 { font-size: 17px; }

.content-view-body.zoom-110 h1 { font-size: 31px; }
.content-view-body.zoom-110 h2 { font-size: 26px; }
.content-view-body.zoom-110 h3 { font-size: 23px; }
.content-view-body.zoom-110 h4 { font-size: 21px; }

.content-view-body.zoom-125 h1 { font-size: 35px; }
.content-view-body.zoom-125 h2 { font-size: 30px; }
.content-view-body.zoom-125 h3 { font-size: 26px; }
.content-view-body.zoom-125 h4 { font-size: 24px; }

.content-view-body.zoom-150 h1 { font-size: 42px; }
.content-view-body.zoom-150 h2 { font-size: 36px; }
.content-view-body.zoom-150 h3 { font-size: 32px; }
.content-view-body.zoom-150 h4 { font-size: 29px; }

.content-view-body.zoom-175 h1 { font-size: 49px; }
.content-view-body.zoom-175 h2 { font-size: 42px; }
.content-view-body.zoom-175 h3 { font-size: 37px; }
.content-view-body.zoom-175 h4 { font-size: 33px; }

.content-view-body.zoom-200 h1 { font-size: 56px; }
.content-view-body.zoom-200 h2 { font-size: 48px; }
.content-view-body.zoom-200 h3 { font-size: 42px; }
.content-view-body.zoom-200 h4 { font-size: 38px; }

/* Enhanced typography for better reading experience */
.content-view-body h1, .content-view-body h2, .content-view-body h3,
.content-view-body h4, .content-view-body h5, .content-view-body h6 {
  line-height: 1.3;
  margin-top: 2em;
  margin-bottom: 1em;
  font-weight: 600;
  color: #2c3e50;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.content-view-body h1 { font-size: 28px; }
.content-view-body h2 { font-size: 24px; }
.content-view-body h3 { font-size: 21px; }
.content-view-body h4 { font-size: 19px; }

.content-view-body p {
  margin-bottom: 1.5em;
  text-align: justify;
  text-justify: inter-word;
}

.content-view-body ul, .content-view-body ol {
  margin-bottom: 1.5em;
  padding-left: 2.5em;
}

.content-view-body li {
  margin-bottom: 0.8em;
  line-height: 1.8;
}

.content-view-body a {
  color: #3498db;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.content-view-body a:hover {
  color: #2980b9;
  border-bottom-color: #2980b9;
}

/* Enhanced print styles for reading window */
@media print {
  .content-view-modal-content {
    max-width: none !important;
    width: 100% !important;
    max-height: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
  }

  .content-view-body {
    padding: 20px !important;
    font-size: 12pt !important;
    line-height: 1.6 !important;
    color: black !important;
  }

  .content-view-body .highlight-text {
    background: #f0f0f0 !important;
    border-left: 3px solid #333 !important;
    font-size: 12pt !important;
  }

  .content-view-body .note-content {
    background: #f9f9f9 !important;
    border: 1px solid #ccc !important;
    font-size: 12pt !important;
  }
}

/* Additional reading enhancements */
.content-view-body {
  /* Better text rendering */
  text-align: left;
  hyphens: auto;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Enhanced typography for different content types */
.content-view-body strong, .content-view-body b {
  font-weight: 600;
  color: #1a252f;
}

.content-view-body em, .content-view-body i {
  font-style: italic;
  color: #34495e;
}

.content-view-body code {
  background: #f1f2f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 16px;
  color: #e74c3c;
}

.content-view-body pre {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  overflow-x: auto;
  border-left: 4px solid #3498db;
  margin: 20px 0;
}

.content-view-body blockquote {
  border-left: 4px solid #bdc3c7;
  padding-left: 20px;
  margin: 20px 0;
  font-style: italic;
  color: #7f8c8d;
}

/* --- END OF FILE dashboard.css --- */