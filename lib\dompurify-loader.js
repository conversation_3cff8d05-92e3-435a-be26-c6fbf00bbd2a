/**
 * DOMPurify Loader
 * Initializes DOMPurify from the local file
 */

(function() {
    // Check if DOMPurify is already loaded and available globally
    if (window.DOMPurify) {
        console.log('Stickara: DOMPurify already loaded');
        // Make it available under our namespace as well
        window.StickaraDOMPurify = window.DOMPurify;
        return;
    }

    /**
     * Initializes DOMPurify with secure configuration
     */
    function initDOMPurify() {
        try {
            // DOMPurify should be loaded from the local file via manifest.json
            if (!window.DOMPurify) {
                console.error('Stickara: DOMPurify not available');
                return {
                    sanitize: function(html) {
                        // Simple fallback sanitization
                        const div = document.createElement('div');
                        div.textContent = html;
                        return div.textContent;
                    }
                };
            }

            const purify = window.DOMPurify;

            // Configure DOMPurify with secure defaults
            purify.setConfig({
                ALLOWED_TAGS: [
                    // Basic formatting
                    'p', 'div', 'span', 'br', 'hr',
                    // Headings
                    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                    // Text formatting
                    'b', 'strong', 'i', 'em', 'u', 'strike', 'sub', 'sup', 'mark',
                    // Lists
                    'ul', 'ol', 'li',
                    // Tables
                    'table', 'thead', 'tbody', 'tr', 'th', 'td',
                    // Other elements
                    'blockquote', 'pre', 'code', 'a', 'img'
                ],
                ALLOWED_ATTR: [
                    // Common attributes
                    'id', 'class', 'style', 'title',
                    // Links
                    'href', 'target', 'rel',
                    // Images
                    'src', 'alt', 'width', 'height',
                    // Tables
                    'colspan', 'rowspan',
                    // Custom data attributes for Stickara functionality
                    'data-stickara-*'
                ],
                ALLOW_DATA_ATTR: false, // Only allow our specific data attributes
                ADD_ATTR: ['target'], // Allow target="_blank" for links
                FORBID_TAGS: ['script', 'style', 'iframe', 'object', 'embed', 'form', 'input'],
                FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover', 'onmouseout', 'onmouseenter', 'onmouseleave'],
                SANITIZE_DOM: true,
                KEEP_CONTENT: true,
                RETURN_DOM_FRAGMENT: false,
                RETURN_DOM: false
            });

            // Make DOMPurify available globally
            window.StickaraDOMPurify = purify;

            console.log('Stickara: DOMPurify initialized with secure configuration');
            return purify;
        } catch (error) {
            console.error('Stickara: Failed to initialize DOMPurify:', error);
            // Return a fallback sanitizer function if DOMPurify fails to load
            return {
                sanitize: function(html) {
                    // Simple fallback sanitization
                    const div = document.createElement('div');
                    div.textContent = html;
                    return div.textContent;
                }
            };
        }
    }

    // Initialize DOMPurify
    window.StickaraDOMPurify = initDOMPurify();
})();
