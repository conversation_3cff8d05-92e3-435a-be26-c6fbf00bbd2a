// --- START OF FILE content-security-utils.js ---

/**
 * Utility functions for security-related operations
 */

/**
 * Safely sanitizes a string to prevent XSS attacks
 * @param {string} unsafeText - The potentially unsafe text
 * @returns {string} - Sanitized text safe for insertion
 */
function sanitizeText(unsafeText) {
    if (unsafeText === null || unsafeText === undefined) {
        return '';
    }
    
    // Use the DOM's built-in text sanitization
    const tempElement = document.createElement('div');
    tempElement.textContent = String(unsafeText);
    return tempElement.textContent;
}

/**
 * Creates a DOM element with sanitized text content
 * @param {string} tagName - The HTML tag name
 * @param {string} text - The text content to sanitize and add
 * @param {Object} [attributes] - Optional attributes to add to the element
 * @returns {HTMLElement} - The created element with safe content
 */
function createSafeElement(tagName, text, attributes = {}) {
    const element = document.createElement(tagName);
    
    // Set sanitized text content
    if (text !== undefined && text !== null) {
        element.textContent = sanitizeText(text);
    }
    
    // Add attributes
    Object.entries(attributes).forEach(([key, value]) => {
        // Sanitize attribute values too
        element.setAttribute(key, sanitizeText(value));
    });
    
    return element;
}

/**
 * Safely renders HTML by creating DOM elements instead of using innerHTML
 * @param {string} html - The HTML string to render safely
 * @returns {DocumentFragment} - A document fragment with the rendered content
 */
function createSafeHTML(html) {
    // Create a parser to parse the HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    
    // Create a document fragment to hold the sanitized content
    const fragment = document.createDocumentFragment();
    
    // Process each node from the parsed document
    Array.from(doc.body.childNodes).forEach(node => {
        // For text nodes, just clone them
        if (node.nodeType === Node.TEXT_NODE) {
            fragment.appendChild(node.cloneNode(true));
            return;
        }
        
        // For element nodes, recreate them with sanitized content
        if (node.nodeType === Node.ELEMENT_NODE) {
            // Create a new element of the same type
            const safeElement = document.createElement(node.tagName);
            
            // Copy sanitized attributes
            Array.from(node.attributes).forEach(attr => {
                // Skip event handlers and javascript: URLs
                if (attr.name.startsWith('on') || 
                    (attr.name === 'href' && attr.value.toLowerCase().startsWith('javascript:'))) {
                    return;
                }
                safeElement.setAttribute(attr.name, sanitizeText(attr.value));
            });
            
            // Recursively process child nodes
            Array.from(node.childNodes).forEach(childNode => {
                if (childNode.nodeType === Node.TEXT_NODE) {
                    safeElement.appendChild(document.createTextNode(childNode.textContent));
                } else if (childNode.nodeType === Node.ELEMENT_NODE) {
                    // Recursively sanitize child elements
                    const childFragment = createSafeHTML(childNode.outerHTML);
                    safeElement.appendChild(childFragment);
                }
            });
            
            fragment.appendChild(safeElement);
        }
    });
    
    return fragment;
}

/**
 * Validates a URL to ensure it's not a dangerous protocol
 * @param {string} url - The URL to validate
 * @returns {boolean} - Whether the URL is safe
 */
function isSafeURL(url) {
    try {
        const parsedURL = new URL(url);
        return ['http:', 'https:'].includes(parsedURL.protocol);
    } catch (e) {
        return false;
    }
}

console.log("Stickara: Security Utils Loaded");
// --- END OF FILE content-security-utils.js ---
