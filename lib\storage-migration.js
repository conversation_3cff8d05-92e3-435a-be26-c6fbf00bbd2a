/**
 * Stickara Storage Migration Utility
 * Provides tools to migrate data from chrome.storage.local to IndexedDB
 */

// Create a namespace to avoid global pollution
window.StickaraMigration = (function() {
    // Migration status constants
    const MIGRATION_STATUS = {
        NOT_STARTED: 'not_started',
        IN_PROGRESS: 'in_progress',
        COMPLETED: 'completed',
        FAILED: 'failed',
        PARTIAL: 'partial'
    };

    // Data types for migration
    const DATA_TYPES = {
        HIGHLIGHTS: 'highlights',
        NOTES: 'notes',
        COMMERCIAL_SITE: 'commercial_site',
        SETTINGS: 'settings',
        NOTEBOOKS: 'notebooks',
        CACHE: 'cache',
        ALL: 'all'
    };

    // Configuration
    const config = {
        batchSize: 50,                  // Number of items to migrate in a batch
        migrationDelay: 100,            // Delay between batches (ms)
        cleanupAfterMigration: false,   // Remove data from source after migration
        validateMigration: true,        // Validate migrated data
        retainBackup: true,             // Keep a backup of migrated data
        debug: false                    // Debug mode
    };

    // Private variables
    let migrationState = {
        status: MIGRATION_STATUS.NOT_STARTED,
        progress: {},
        startTime: null,
        endTime: null,
        error: null
    };

    /**
     * Log messages when debug mode is enabled
     * @param {...any} args - Arguments to log
     */
    function debugLog(...args) {
        if (config.debug) {
            console.log('[StickaraMigration]', ...args);
        }
    }

    /**
     * Checks if IndexedDB is available and initialized
     * @returns {Promise<boolean>} A promise that resolves with true if IndexedDB is ready
     */
    async function checkIndexedDBReady() {
        if (!window.StickaraIndexedDB) {
            throw new Error('IndexedDB module not available');
        }

        try {
            await window.StickaraIndexedDB.init();
            return true;
        } catch (error) {
            console.error('Stickara: Error initializing IndexedDB:', error);
            throw error;
        }
    }

    /**
     * Gets all keys from chrome.storage.local
     * @returns {Promise<string[]>} A promise that resolves with an array of keys
     */
    async function getAllStorageKeys() {
        return new Promise((resolve, reject) => {
            chrome.storage.local.get(null, (result) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(`Error accessing storage: ${chrome.runtime.lastError.message}`));
                } else {
                    resolve(Object.keys(result));
                }
            });
        });
    }

    /**
     * Gets data from chrome.storage.local for the specified keys
     * @param {string[]} keys - The keys to get data for
     * @returns {Promise<Object>} A promise that resolves with the data
     */
    async function getStorageData(keys) {
        return new Promise((resolve, reject) => {
            chrome.storage.local.get(keys, (result) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(`Error getting data: ${chrome.runtime.lastError.message}`));
                } else {
                    resolve(result);
                }
            });
        });
    }

    /**
     * Removes data from chrome.storage.local for the specified keys
     * @param {string[]} keys - The keys to remove
     * @returns {Promise<void>} A promise that resolves when the data is removed
     */
    async function removeStorageData(keys) {
        return new Promise((resolve, reject) => {
            chrome.storage.local.remove(keys, () => {
                if (chrome.runtime.lastError) {
                    reject(new Error(`Error removing data: ${chrome.runtime.lastError.message}`));
                } else {
                    resolve();
                }
            });
        });
    }

    /**
     * Categorizes a storage key by data type
     * @param {string} key - The storage key
     * @returns {string} The data type
     */
    function categorizeKey(key) {
        if (key.startsWith('highlight_')) {
            return DATA_TYPES.HIGHLIGHTS;
        } else if (key.startsWith('note_')) {
            return DATA_TYPES.NOTES;
        } else if (key.startsWith('sb_commercial_')) {
            return DATA_TYPES.COMMERCIAL_SITE;
        } else if (key.startsWith('Stickara_settings') || key.includes('settings')) {
            return DATA_TYPES.SETTINGS;
        } else if (key.includes('notebook')) {
            return DATA_TYPES.NOTEBOOKS;
        } else if (key.startsWith('cache_')) {
            return DATA_TYPES.CACHE;
        } else {
            return 'other';
        }
    }

    /**
     * Migrates highlights from chrome.storage.local to IndexedDB
     * @param {Object} data - The highlight data to migrate
     * @returns {Promise<Object>} A promise that resolves with the migration result
     */
    async function migrateHighlights(data) {
        const results = {
            total: 0,
            migrated: 0,
            failed: 0,
            details: []
        };

        for (const [key, highlights] of Object.entries(data)) {
            if (!key.startsWith('highlight_') || !Array.isArray(highlights)) {
                continue;
            }

            const url = key.replace('highlight_', '');
            results.total += highlights.length;

            for (const highlight of highlights) {
                if (!highlight || !highlight.id) continue;

                try {
                    // Prepare highlight for IndexedDB
                    const highlightData = {
                        ...highlight,
                        key: key,
                        url: url,
                        lastModified: highlight.timestamp || Date.now()
                    };

                    // Store in IndexedDB
                    await window.StickaraIndexedDB.setHighlight(highlightData);
                    results.migrated++;
                    results.details.push({
                        id: highlight.id,
                        success: true
                    });
                } catch (error) {
                    console.error(`Stickara: Error migrating highlight ${highlight.id}:`, error);
                    results.failed++;
                    results.details.push({
                        id: highlight.id,
                        success: false,
                        error: error.message
                    });
                }
            }
        }

        return results;
    }

    /**
     * Migrates notes from chrome.storage.local to IndexedDB
     * @param {Object} data - The note data to migrate
     * @returns {Promise<Object>} A promise that resolves with the migration result
     */
    async function migrateNotes(data) {
        const results = {
            total: 0,
            migrated: 0,
            failed: 0,
            details: []
        };

        for (const [key, note] of Object.entries(data)) {
            if (!key.startsWith('note_')) {
                continue;
            }

            results.total++;

            try {
                // Prepare note for IndexedDB
                const noteData = {
                    ...note,
                    key: key,
                    lastModified: note.timestamp || Date.now()
                };

                // Store in IndexedDB
                await window.StickaraIndexedDB.setNote(noteData);
                results.migrated++;
                results.details.push({
                    key: key,
                    success: true
                });
            } catch (error) {
                console.error(`Stickara: Error migrating note ${key}:`, error);
                results.failed++;
                results.details.push({
                    key: key,
                    success: false,
                    error: error.message
                });
            }
        }

        return results;
    }

    /**
     * Migrates commercial site data from chrome.storage.local to IndexedDB
     * @param {Object} data - The commercial site data to migrate
     * @returns {Promise<Object>} A promise that resolves with the migration result
     */
    async function migrateCommercialSiteData(data) {
        const results = {
            total: 0,
            migrated: 0,
            failed: 0,
            details: []
        };

        // Migrate user feedback
        if (data.sb_commercial_feedback) {
            results.total++;
            try {
                await window.StickaraIndexedDB.setMetadata({
                    key: 'commercial_feedback',
                    data: data.sb_commercial_feedback,
                    lastModified: Date.now()
                });
                results.migrated++;
                results.details.push({
                    key: 'commercial_feedback',
                    success: true
                });
            } catch (error) {
                console.error('Stickara: Error migrating commercial feedback:', error);
                results.failed++;
                results.details.push({
                    key: 'commercial_feedback',
                    success: false,
                    error: error.message
                });
            }
        }



        return results;
    }

    /**
     * Migrates settings from chrome.storage.local to IndexedDB
     * @param {Object} data - The settings data to migrate
     * @returns {Promise<Object>} A promise that resolves with the migration result
     */
    async function migrateSettings(data) {
        const results = {
            total: 0,
            migrated: 0,
            failed: 0,
            details: []
        };

        // Migrate Stickara settings
        if (data.Stickara_settings) {
            results.total++;
            try {
                await window.StickaraIndexedDB.setMetadata({
                    key: 'settings',
                    data: data.Stickara_settings,
                    lastModified: Date.now()
                });
                results.migrated++;
                results.details.push({
                    key: 'settings',
                    success: true
                });
            } catch (error) {
                console.error('Stickara: Error migrating settings:', error);
                results.failed++;
                results.details.push({
                    key: 'settings',
                    success: false,
                    error: error.message
                });
            }
        }

        return results;
    }

    /**
     * Starts the migration process
     * @param {string[]} [dataTypes=[DATA_TYPES.ALL]] - The data types to migrate
     * @returns {Promise<Object>} A promise that resolves with the migration result
     */
    async function startMigration(dataTypes = [DATA_TYPES.ALL]) {
        // Check if migration is already in progress
        if (migrationState.status === MIGRATION_STATUS.IN_PROGRESS) {
            return {
                success: false,
                error: 'Migration already in progress',
                state: { ...migrationState }
            };
        }

        // Reset migration state
        migrationState = {
            status: MIGRATION_STATUS.IN_PROGRESS,
            progress: {},
            startTime: Date.now(),
            endTime: null,
            error: null
        };

        try {
            // Check if IndexedDB is ready
            await checkIndexedDBReady();

            // Get all keys from storage
            const allKeys = await getAllStorageKeys();
            debugLog(`Found ${allKeys.length} keys in chrome.storage.local`);

            // Filter keys by data type
            const keysToMigrate = [];
            const migrateAll = dataTypes.includes(DATA_TYPES.ALL);

            for (const key of allKeys) {
                const category = categorizeKey(key);
                if (migrateAll || dataTypes.includes(category)) {
                    keysToMigrate.push(key);
                }
            }

            debugLog(`Selected ${keysToMigrate.length} keys for migration`);

            // Process in batches
            const results = {
                total: keysToMigrate.length,
                processed: 0,
                migrated: 0,
                failed: 0,
                byType: {}
            };

            // Initialize results for each data type
            Object.values(DATA_TYPES).forEach(type => {
                results.byType[type] = {
                    total: 0,
                    migrated: 0,
                    failed: 0
                };
            });

            // Process in batches
            for (let i = 0; i < keysToMigrate.length; i += config.batchSize) {
                const batchKeys = keysToMigrate.slice(i, i + config.batchSize);
                const batchData = await getStorageData(batchKeys);

                // Group data by type
                const groupedData = {
                    highlights: {},
                    notes: {},
                    commercial_site: {},
                    settings: {},
                    notebooks: {},
                    cache: {},
                    other: {}
                };

                for (const key of batchKeys) {
                    const category = categorizeKey(key);
                    groupedData[category][key] = batchData[key];
                }

                // Migrate each type
                const batchResults = {
                    highlights: await migrateHighlights(groupedData.highlights),
                    notes: await migrateNotes(groupedData.notes),
                    commercial_site: await migrateCommercialSiteData(groupedData.commercial_site),
                    settings: await migrateSettings(groupedData.settings)
                    // TODO: Add other migration functions as needed
                };

                // Update results
                results.processed += batchKeys.length;
                results.migrated += batchResults.highlights.migrated +
                                   batchResults.notes.migrated +
                                   batchResults.commercial_site.migrated +
                                   batchResults.settings.migrated;
                results.failed += batchResults.highlights.failed +
                                 batchResults.notes.failed +
                                 batchResults.commercial_site.failed +
                                 batchResults.settings.failed;

                // Update progress
                migrationState.progress = {
                    ...results,
                    percentComplete: Math.round((results.processed / results.total) * 100)
                };

                // Clean up if configured
                if (config.cleanupAfterMigration) {
                    await removeStorageData(batchKeys);
                }

                // Add delay between batches
                if (i + config.batchSize < keysToMigrate.length) {
                    await new Promise(resolve => setTimeout(resolve, config.migrationDelay));
                }
            }

            // Update migration state
            migrationState.status = MIGRATION_STATUS.COMPLETED;
            migrationState.endTime = Date.now();
            migrationState.progress = {
                ...results,
                percentComplete: 100,
                duration: migrationState.endTime - migrationState.startTime
            };

            debugLog('Migration completed successfully', migrationState.progress);
            return {
                success: true,
                state: { ...migrationState }
            };
        } catch (error) {
            console.error('Stickara: Error during migration:', error);

            // Update migration state
            migrationState.status = MIGRATION_STATUS.FAILED;
            migrationState.endTime = Date.now();
            migrationState.error = error.message;

            return {
                success: false,
                error: error.message,
                state: { ...migrationState }
            };
        }
    }

    /**
     * Gets the current migration state
     * @returns {Object} The current migration state
     */
    function getMigrationState() {
        return { ...migrationState };
    }

    // Return the public API
    return {
        // Core migration operations
        startMigration,
        getMigrationState,

        // Constants
        MIGRATION_STATUS,
        DATA_TYPES,

        // Configuration
        updateConfig: (newConfig) => {
            Object.assign(config, newConfig);
            return { ...config };
        },
        getConfig: () => ({ ...config })
    };
})();

console.log("Stickara: Storage Migration Utility Loaded");
