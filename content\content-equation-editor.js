// --- START OF FILE content-equation-editor.js ---

/**
 * Advanced Equation Editor for Stickara
 * Provides a comprehensive interface for creating and editing mathematical equations
 */

// Global variables for the equation editor
let equationEditorModal = null;
let currentEquationSpan = null;
let equationPreview = null;
let latexInput = null;
let equationLibrary = null;
let userEquations = [];
let recentEquations = [];
let favoriteEquations = [];

// Equation categories and their equations (expanded)
const EQUATION_CATEGORIES = {
    'Basic Math': [
        { name: 'Addition', latex: 'a + b = c', description: 'Basic addition', tags: ['arithmetic', 'basic'] },
        { name: 'Subtraction', latex: 'a - b = c', description: 'Basic subtraction', tags: ['arithmetic', 'basic'] },
        { name: 'Multiplication', latex: 'a \\times b = c', description: 'Basic multiplication', tags: ['arithmetic', 'basic'] },
        { name: 'Division', latex: '\\frac{a}{b} = c', description: 'Basic division', tags: ['arithmetic', 'fraction'] },
        { name: 'Square Root', latex: '\\sqrt{x}', description: 'Square root', tags: ['root', 'radical'] },
        { name: 'Cube Root', latex: '\\sqrt[3]{x}', description: 'Cube root', tags: ['root', 'radical'] },
        { name: 'Nth Root', latex: '\\sqrt[n]{x}', description: 'Nth root', tags: ['root', 'radical'] },
        { name: 'Power', latex: 'x^n', description: 'Exponentiation', tags: ['power', 'exponent'] },
        { name: 'Absolute Value', latex: '|x|', description: 'Absolute value', tags: ['absolute', 'modulus'] },
        { name: 'Percentage', latex: '\\frac{x}{100} \\times y', description: 'Percentage calculation', tags: ['percent', 'ratio'] }
    ],
    'Algebra': [
        { name: 'Quadratic Formula', latex: 'x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}', description: 'Quadratic equation solution', tags: ['quadratic', 'formula'] },
        { name: 'Linear Equation', latex: 'y = mx + b', description: 'Linear function', tags: ['linear', 'slope'] },
        { name: 'Point-Slope Form', latex: 'y - y_1 = m(x - x_1)', description: 'Point-slope form of line', tags: ['linear', 'point-slope'] },
        { name: 'Factoring', latex: 'x^2 - y^2 = (x+y)(x-y)', description: 'Difference of squares', tags: ['factoring', 'polynomial'] },
        { name: 'Perfect Square', latex: '(a \\pm b)^2 = a^2 \\pm 2ab + b^2', description: 'Perfect square trinomial', tags: ['factoring', 'square'] },
        { name: 'Logarithm', latex: '\\log_b(x) = y', description: 'Logarithmic function', tags: ['logarithm', 'log'] },
        { name: 'Natural Log', latex: '\\ln(x) = \\log_e(x)', description: 'Natural logarithm', tags: ['logarithm', 'natural'] },
        { name: 'Exponential', latex: 'e^x', description: 'Exponential function', tags: ['exponential', 'euler'] },
        { name: 'Binomial Theorem', latex: '(a+b)^n = \\sum_{k=0}^{n} \\binom{n}{k} a^{n-k} b^k', description: 'Binomial expansion', tags: ['binomial', 'expansion'] },
        { name: 'Distance Formula', latex: 'd = \\sqrt{(x_2-x_1)^2 + (y_2-y_1)^2}', description: 'Distance between two points', tags: ['distance', 'coordinate'] }
    ],
    'Geometry': [
        { name: 'Pythagorean Theorem', latex: 'a^2 + b^2 = c^2', description: 'Right triangle relationship' },
        { name: 'Circle Area', latex: 'A = \\pi r^2', description: 'Area of a circle' },
        { name: 'Circle Circumference', latex: 'C = 2\\pi r', description: 'Circumference of a circle' },
        { name: 'Triangle Area', latex: 'A = \\frac{1}{2}bh', description: 'Area of a triangle' },
        { name: 'Sphere Volume', latex: 'V = \\frac{4}{3}\\pi r^3', description: 'Volume of a sphere' }
    ],
    'Calculus': [
        { name: 'Derivative', latex: '\\frac{d}{dx}f(x) = f\'(x)', description: 'Derivative notation' },
        { name: 'Integral', latex: '\\int f(x) dx', description: 'Indefinite integral' },
        { name: 'Definite Integral', latex: '\\int_a^b f(x) dx', description: 'Definite integral' },
        { name: 'Limit', latex: '\\lim_{x \\to a} f(x) = L', description: 'Limit definition' },
        { name: 'Chain Rule', latex: '\\frac{d}{dx}[f(g(x))] = f\'(g(x)) \\cdot g\'(x)', description: 'Chain rule for derivatives' }
    ],
    'Physics': [
        { name: 'Energy-Mass', latex: 'E = mc^2', description: 'Einstein\'s mass-energy equivalence' },
        { name: 'Force', latex: 'F = ma', description: 'Newton\'s second law' },
        { name: 'Kinetic Energy', latex: 'KE = \\frac{1}{2}mv^2', description: 'Kinetic energy formula' },
        { name: 'Potential Energy', latex: 'PE = mgh', description: 'Gravitational potential energy' },
        { name: 'Wave Equation', latex: 'v = f\\lambda', description: 'Wave speed equation' }
    ],
    'Chemistry': [
        { name: 'Water', latex: 'H_2O', description: 'Water molecule' },
        { name: 'Carbon Dioxide', latex: 'CO_2', description: 'Carbon dioxide molecule' },
        { name: 'Methane', latex: 'CH_4', description: 'Methane molecule' },
        { name: 'Ideal Gas Law', latex: 'PV = nRT', description: 'Ideal gas law equation' },
        { name: 'pH Formula', latex: 'pH = -\\log[H^+]', description: 'pH calculation' }
    ],
    'Statistics': [
        { name: 'Mean', latex: '\\bar{x} = \\frac{1}{n}\\sum_{i=1}^{n} x_i', description: 'Arithmetic mean', tags: ['mean', 'average'] },
        { name: 'Standard Deviation', latex: '\\sigma = \\sqrt{\\frac{1}{n}\\sum_{i=1}^{n}(x_i - \\mu)^2}', description: 'Population standard deviation', tags: ['std', 'deviation'] },
        { name: 'Normal Distribution', latex: 'f(x) = \\frac{1}{\\sigma\\sqrt{2\\pi}}e^{-\\frac{(x-\\mu)^2}{2\\sigma^2}}', description: 'Normal distribution PDF', tags: ['normal', 'gaussian'] },
        { name: 'Correlation', latex: 'r = \\frac{\\sum(x_i - \\bar{x})(y_i - \\bar{y})}{\\sqrt{\\sum(x_i - \\bar{x})^2\\sum(y_i - \\bar{y})^2}}', description: 'Pearson correlation coefficient', tags: ['correlation', 'pearson'] },
        { name: 'Variance', latex: '\\sigma^2 = \\frac{1}{n}\\sum_{i=1}^{n}(x_i - \\mu)^2', description: 'Population variance', tags: ['variance', 'var'] },
        { name: 'Chi-Square', latex: '\\chi^2 = \\sum_{i=1}^{n} \\frac{(O_i - E_i)^2}{E_i}', description: 'Chi-square test statistic', tags: ['chi-square', 'test'] },
        { name: 'T-Test', latex: 't = \\frac{\\bar{x} - \\mu}{s/\\sqrt{n}}', description: 'One-sample t-test', tags: ['t-test', 'hypothesis'] }
    ],
    'Advanced Templates': [
        { name: 'Matrix 2x2', latex: '\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}', description: '2x2 matrix template', tags: ['matrix', 'linear'] },
        { name: 'Matrix 3x3', latex: '\\begin{pmatrix} a & b & c \\\\ d & e & f \\\\ g & h & i \\end{pmatrix}', description: '3x3 matrix template', tags: ['matrix', 'linear'] },
        { name: 'Determinant', latex: '\\det(A) = \\begin{vmatrix} a & b \\\\ c & d \\end{vmatrix} = ad - bc', description: '2x2 determinant', tags: ['determinant', 'matrix'] },
        { name: 'System of Equations', latex: '\\begin{cases} ax + by = c \\\\ dx + ey = f \\end{cases}', description: 'System of linear equations', tags: ['system', 'equations'] },
        { name: 'Piecewise Function', latex: 'f(x) = \\begin{cases} x^2 & \\text{if } x \\geq 0 \\\\ -x & \\text{if } x < 0 \\end{cases}', description: 'Piecewise function template', tags: ['piecewise', 'function'] },
        { name: 'Summation', latex: '\\sum_{i=1}^{n} a_i = a_1 + a_2 + \\cdots + a_n', description: 'Summation notation', tags: ['sum', 'series'] },
        { name: 'Product', latex: '\\prod_{i=1}^{n} a_i = a_1 \\cdot a_2 \\cdots a_n', description: 'Product notation', tags: ['product', 'series'] },
        { name: 'Binomial Coefficient', latex: '\\binom{n}{k} = \\frac{n!}{k!(n-k)!}', description: 'Binomial coefficient', tags: ['binomial', 'combinatorics'] },
        { name: 'Fourier Transform', latex: 'F(\\omega) = \\int_{-\\infty}^{\\infty} f(t) e^{-i\\omega t} dt', description: 'Fourier transform', tags: ['fourier', 'transform'] },
        { name: 'Laplace Transform', latex: 'F(s) = \\int_{0}^{\\infty} f(t) e^{-st} dt', description: 'Laplace transform', tags: ['laplace', 'transform'] },
        { name: 'Taylor Series', latex: 'f(x) = \\sum_{n=0}^{\\infty} \\frac{f^{(n)}(a)}{n!}(x-a)^n', description: 'Taylor series expansion', tags: ['taylor', 'series'] },
        { name: 'Complex Number', latex: 'z = a + bi = r e^{i\\theta}', description: 'Complex number representation', tags: ['complex', 'number'] },
        { name: 'Vector', latex: '\\vec{v} = \\langle v_1, v_2, v_3 \\rangle', description: 'Vector notation', tags: ['vector', 'linear'] },
        { name: 'Dot Product', latex: '\\vec{a} \\cdot \\vec{b} = |\\vec{a}||\\vec{b}|\\cos\\theta', description: 'Vector dot product', tags: ['dot', 'product', 'vector'] },
        { name: 'Cross Product', latex: '\\vec{a} \\times \\vec{b} = |\\vec{a}||\\vec{b}|\\sin\\theta \\hat{n}', description: 'Vector cross product', tags: ['cross', 'product', 'vector'] }
    ]
};

// Common mathematical symbols organized by category (expanded)
const SYMBOL_CATEGORIES = {
    'Basic Operations': [
        { symbol: '+', latex: '+', name: 'Plus', category: 'basic' },
        { symbol: '−', latex: '-', name: 'Minus', category: 'basic' },
        { symbol: '×', latex: '\\times', name: 'Times', category: 'basic' },
        { symbol: '÷', latex: '\\div', name: 'Division', category: 'basic' },
        { symbol: '=', latex: '=', name: 'Equals', category: 'basic' },
        { symbol: '≠', latex: '\\neq', name: 'Not equal', category: 'basic' },
        { symbol: '±', latex: '\\pm', name: 'Plus minus', category: 'basic' },
        { symbol: '∓', latex: '\\mp', name: 'Minus plus', category: 'basic' },
        { symbol: '⋅', latex: '\\cdot', name: 'Dot product', category: 'basic' },
        { symbol: '∗', latex: '\\ast', name: 'Asterisk', category: 'basic' }
    ],
    'Fractions & Roots': [
        { symbol: '½', latex: '\\frac{1}{2}', name: 'One half' },
        { symbol: '√', latex: '\\sqrt{x}', name: 'Square root' },
        { symbol: '∛', latex: '\\sqrt[3]{x}', name: 'Cube root' },
        { symbol: 'ⁿ√', latex: '\\sqrt[n]{x}', name: 'Nth root' }
    ],
    'Powers & Indices': [
        { symbol: 'x²', latex: 'x^2', name: 'Squared' },
        { symbol: 'x³', latex: 'x^3', name: 'Cubed' },
        { symbol: 'xⁿ', latex: 'x^n', name: 'Power' },
        { symbol: 'x₁', latex: 'x_1', name: 'Subscript' }
    ],
    'Comparison': [
        { symbol: '<', latex: '<', name: 'Less than' },
        { symbol: '>', latex: '>', name: 'Greater than' },
        { symbol: '≤', latex: '\\leq', name: 'Less than or equal' },
        { symbol: '≥', latex: '\\geq', name: 'Greater than or equal' },
        { symbol: '≈', latex: '\\approx', name: 'Approximately' },
        { symbol: '∝', latex: '\\propto', name: 'Proportional to' }
    ],
    'Greek Letters': [
        { symbol: 'α', latex: '\\alpha', name: 'Alpha' },
        { symbol: 'β', latex: '\\beta', name: 'Beta' },
        { symbol: 'γ', latex: '\\gamma', name: 'Gamma' },
        { symbol: 'δ', latex: '\\delta', name: 'Delta' },
        { symbol: 'ε', latex: '\\epsilon', name: 'Epsilon' },
        { symbol: 'ζ', latex: '\\zeta', name: 'Zeta' },
        { symbol: 'η', latex: '\\eta', name: 'Eta' },
        { symbol: 'θ', latex: '\\theta', name: 'Theta' },
        { symbol: 'ι', latex: '\\iota', name: 'Iota' },
        { symbol: 'κ', latex: '\\kappa', name: 'Kappa' },
        { symbol: 'λ', latex: '\\lambda', name: 'Lambda' },
        { symbol: 'μ', latex: '\\mu', name: 'Mu' },
        { symbol: 'ν', latex: '\\nu', name: 'Nu' },
        { symbol: 'ξ', latex: '\\xi', name: 'Xi' },
        { symbol: 'π', latex: '\\pi', name: 'Pi' },
        { symbol: 'ρ', latex: '\\rho', name: 'Rho' },
        { symbol: 'σ', latex: '\\sigma', name: 'Sigma' },
        { symbol: 'τ', latex: '\\tau', name: 'Tau' },
        { symbol: 'υ', latex: '\\upsilon', name: 'Upsilon' },
        { symbol: 'φ', latex: '\\phi', name: 'Phi' },
        { symbol: 'χ', latex: '\\chi', name: 'Chi' },
        { symbol: 'ψ', latex: '\\psi', name: 'Psi' },
        { symbol: 'ω', latex: '\\omega', name: 'Omega' },
        { symbol: 'Γ', latex: '\\Gamma', name: 'Capital Gamma' },
        { symbol: 'Δ', latex: '\\Delta', name: 'Capital Delta' },
        { symbol: 'Θ', latex: '\\Theta', name: 'Capital Theta' },
        { symbol: 'Λ', latex: '\\Lambda', name: 'Capital Lambda' },
        { symbol: 'Ξ', latex: '\\Xi', name: 'Capital Xi' },
        { symbol: 'Π', latex: '\\Pi', name: 'Capital Pi' },
        { symbol: 'Σ', latex: '\\Sigma', name: 'Capital Sigma' },
        { symbol: 'Φ', latex: '\\Phi', name: 'Capital Phi' },
        { symbol: 'Ψ', latex: '\\Psi', name: 'Capital Psi' },
        { symbol: 'Ω', latex: '\\Omega', name: 'Capital Omega' }
    ],
    'Calculus': [
        { symbol: '∫', latex: '\\int', name: 'Integral' },
        { symbol: '∬', latex: '\\iint', name: 'Double integral' },
        { symbol: '∭', latex: '\\iiint', name: 'Triple integral' },
        { symbol: '∮', latex: '\\oint', name: 'Contour integral' },
        { symbol: '∂', latex: '\\partial', name: 'Partial derivative' },
        { symbol: '∇', latex: '\\nabla', name: 'Nabla' },
        { symbol: '∞', latex: '\\infty', name: 'Infinity' },
        { symbol: '∑', latex: '\\sum', name: 'Sum' },
        { symbol: '∏', latex: '\\prod', name: 'Product' },
        { symbol: '∐', latex: '\\coprod', name: 'Coproduct' },
        { symbol: 'lim', latex: '\\lim', name: 'Limit' },
        { symbol: 'sup', latex: '\\sup', name: 'Supremum' },
        { symbol: 'inf', latex: '\\inf', name: 'Infimum' },
        { symbol: 'max', latex: '\\max', name: 'Maximum' },
        { symbol: 'min', latex: '\\min', name: 'Minimum' },
        { symbol: 'arg', latex: '\\arg', name: 'Argument' },
        { symbol: '∇²', latex: '\\nabla^2', name: 'Laplacian' },
        { symbol: '∇·', latex: '\\nabla \\cdot', name: 'Divergence' },
        { symbol: '∇×', latex: '\\nabla \\times', name: 'Curl' }
    ],
    'Sets & Logic': [
        { symbol: '∈', latex: '\\in', name: 'Element of' },
        { symbol: '∉', latex: '\\notin', name: 'Not element of' },
        { symbol: '⊂', latex: '\\subset', name: 'Subset' },
        { symbol: '⊆', latex: '\\subseteq', name: 'Subset or equal' },
        { symbol: '⊃', latex: '\\supset', name: 'Superset' },
        { symbol: '⊇', latex: '\\supseteq', name: 'Superset or equal' },
        { symbol: '∪', latex: '\\cup', name: 'Union' },
        { symbol: '∩', latex: '\\cap', name: 'Intersection' },
        { symbol: '∅', latex: '\\emptyset', name: 'Empty set' },
        { symbol: '∀', latex: '\\forall', name: 'For all' },
        { symbol: '∃', latex: '\\exists', name: 'There exists' },
        { symbol: '∄', latex: '\\nexists', name: 'Does not exist' },
        { symbol: '∧', latex: '\\land', name: 'Logical and' },
        { symbol: '∨', latex: '\\lor', name: 'Logical or' },
        { symbol: '¬', latex: '\\neg', name: 'Logical not' },
        { symbol: '⊕', latex: '\\oplus', name: 'Exclusive or' },
        { symbol: '⊗', latex: '\\otimes', name: 'Tensor product' },
        { symbol: '⊥', latex: '\\perp', name: 'Perpendicular' },
        { symbol: '∥', latex: '\\parallel', name: 'Parallel' }
    ],
    'Advanced Math': [
        { symbol: '∂', latex: '\\partial', name: 'Partial derivative' },
        { symbol: '∇', latex: '\\nabla', name: 'Del operator' },
        { symbol: '∆', latex: '\\triangle', name: 'Triangle' },
        { symbol: '□', latex: '\\square', name: 'Square' },
        { symbol: '◊', latex: '\\diamond', name: 'Diamond' },
        { symbol: '∘', latex: '\\circ', name: 'Composition' },
        { symbol: '∗', latex: '\\ast', name: 'Asterisk operator' },
        { symbol: '⋆', latex: '\\star', name: 'Star operator' },
        { symbol: '†', latex: '\\dagger', name: 'Dagger' },
        { symbol: '‡', latex: '\\ddagger', name: 'Double dagger' },
        { symbol: '∝', latex: '\\propto', name: 'Proportional to' },
        { symbol: '∼', latex: '\\sim', name: 'Similar to' },
        { symbol: '≃', latex: '\\simeq', name: 'Asymptotically equal' },
        { symbol: '≅', latex: '\\cong', name: 'Congruent' },
        { symbol: '≡', latex: '\\equiv', name: 'Equivalent' },
        { symbol: '≢', latex: '\\not\\equiv', name: 'Not equivalent' },
        { symbol: '≪', latex: '\\ll', name: 'Much less than' },
        { symbol: '≫', latex: '\\gg', name: 'Much greater than' }
    ],
    'Number Sets': [
        { symbol: 'ℕ', latex: '\\mathbb{N}', name: 'Natural numbers' },
        { symbol: 'ℤ', latex: '\\mathbb{Z}', name: 'Integers' },
        { symbol: 'ℚ', latex: '\\mathbb{Q}', name: 'Rational numbers' },
        { symbol: 'ℝ', latex: '\\mathbb{R}', name: 'Real numbers' },
        { symbol: 'ℂ', latex: '\\mathbb{C}', name: 'Complex numbers' },
        { symbol: 'ℍ', latex: '\\mathbb{H}', name: 'Quaternions' },
        { symbol: 'ℙ', latex: '\\mathbb{P}', name: 'Prime numbers' },
        { symbol: '𝔽', latex: '\\mathbb{F}', name: 'Field' },
        { symbol: '𝕀', latex: '\\mathbb{I}', name: 'Irrational numbers' }
    ],
    'Arrows & Relations': [
        { symbol: '→', latex: '\\to', name: 'Right arrow' },
        { symbol: '←', latex: '\\leftarrow', name: 'Left arrow' },
        { symbol: '↔', latex: '\\leftrightarrow', name: 'Left-right arrow' },
        { symbol: '⇒', latex: '\\Rightarrow', name: 'Implies' },
        { symbol: '⇐', latex: '\\Leftarrow', name: 'Implied by' },
        { symbol: '⇔', latex: '\\Leftrightarrow', name: 'If and only if' },
        { symbol: '↑', latex: '\\uparrow', name: 'Up arrow' },
        { symbol: '↓', latex: '\\downarrow', name: 'Down arrow' },
        { symbol: '↗', latex: '\\nearrow', name: 'Northeast arrow' },
        { symbol: '↘', latex: '\\searrow', name: 'Southeast arrow' },
        { symbol: '↙', latex: '\\swarrow', name: 'Southwest arrow' },
        { symbol: '↖', latex: '\\nwarrow', name: 'Northwest arrow' },
        { symbol: '⟶', latex: '\\longrightarrow', name: 'Long right arrow' },
        { symbol: '⟵', latex: '\\longleftarrow', name: 'Long left arrow' },
        { symbol: '⟷', latex: '\\longleftrightarrow', name: 'Long left-right arrow' },
        { symbol: '↦', latex: '\\mapsto', name: 'Maps to' },
        { symbol: '⊢', latex: '\\vdash', name: 'Proves' },
        { symbol: '⊨', latex: '\\models', name: 'Models' }
    ],
    'Brackets & Delimiters': [
        { symbol: '⟨', latex: '\\langle', name: 'Left angle bracket' },
        { symbol: '⟩', latex: '\\rangle', name: 'Right angle bracket' },
        { symbol: '⌊', latex: '\\lfloor', name: 'Left floor' },
        { symbol: '⌋', latex: '\\rfloor', name: 'Right floor' },
        { symbol: '⌈', latex: '\\lceil', name: 'Left ceiling' },
        { symbol: '⌉', latex: '\\rceil', name: 'Right ceiling' },
        { symbol: '‖', latex: '\\|', name: 'Double vertical bar' },
        { symbol: '∥', latex: '\\parallel', name: 'Parallel' },
        { symbol: '⟦', latex: '\\llbracket', name: 'Left double bracket' },
        { symbol: '⟧', latex: '\\rrbracket', name: 'Right double bracket' }
    ],
    'Trigonometry': [
        { symbol: 'sin', latex: '\\sin', name: 'Sine' },
        { symbol: 'cos', latex: '\\cos', name: 'Cosine' },
        { symbol: 'tan', latex: '\\tan', name: 'Tangent' },
        { symbol: 'cot', latex: '\\cot', name: 'Cotangent' },
        { symbol: 'sec', latex: '\\sec', name: 'Secant' },
        { symbol: 'csc', latex: '\\csc', name: 'Cosecant' },
        { symbol: 'arcsin', latex: '\\arcsin', name: 'Arcsine' },
        { symbol: 'arccos', latex: '\\arccos', name: 'Arccosine' },
        { symbol: 'arctan', latex: '\\arctan', name: 'Arctangent' },
        { symbol: 'sinh', latex: '\\sinh', name: 'Hyperbolic sine' },
        { symbol: 'cosh', latex: '\\cosh', name: 'Hyperbolic cosine' },
        { symbol: 'tanh', latex: '\\tanh', name: 'Hyperbolic tangent' }
    ]
};

/**
 * Opens the advanced equation editor modal
 * @param {HTMLElement} equationSpan - The equation span element to edit
 */
function openAdvancedEquationEditor(equationSpan) {
    console.log("Stickara: openAdvancedEquationEditor called with span:", equationSpan);
    currentEquationSpan = equationSpan;

    // Create modal if it doesn't exist
    if (!equationEditorModal) {
        console.log("Stickara: Creating equation editor modal");
        createEquationEditorModal();
    }

    if (!equationEditorModal) {
        console.error("Stickara: Failed to create equation editor modal");
        alert("Error: Could not create equation editor. Please try again.");
        return;
    }

    // Load current equation
    const currentLatex = equationSpan.getAttribute('data-latex') || '';
    console.log("Stickara: Current LaTeX:", currentLatex);

    if (latexInput) {
        latexInput.value = currentLatex;
        updateEquationPreview();
    } else {
        console.error("Stickara: LaTeX input not found");
    }

    // Load user equations
    loadUserEquations();

    // Show modal
    console.log("Stickara: Showing equation editor modal");
    equationEditorModal.style.display = 'flex';
    equationEditorModal.setAttribute('aria-hidden', 'false');

    // Focus on LaTeX input with additional safety check
    setTimeout(() => {
        if (latexInput && typeof latexInput.focus === 'function') {
            try {
                latexInput.focus();
                latexInput.select();
            } catch (e) {
                console.warn('Stickara: Could not focus LaTeX input:', e);
            }
        }
    }, 100);
}

/**
 * Creates the equation editor modal interface with enhanced UX
 */
function createEquationEditorModal() {
    // Create modal overlay
    equationEditorModal = document.createElement('div');
    equationEditorModal.id = 'Stickara-equation-editor-modal';
    equationEditorModal.className = 'Stickara-modal-overlay';
    equationEditorModal.style.display = 'none';
    equationEditorModal.setAttribute('role', 'dialog');
    equationEditorModal.setAttribute('aria-modal', 'true');
    equationEditorModal.setAttribute('aria-labelledby', 'equation-editor-title');

    // Create modal content with enhanced styling
    const modalContent = document.createElement('div');
    modalContent.className = 'Stickara-equation-editor-content enhanced-modal';

    // Create simplified header
    const header = document.createElement('div');
    header.className = 'Stickara-equation-editor-header';

    const titleContainer = document.createElement('div');
    titleContainer.className = 'Stickara-title-container';

    const title = document.createElement('h3');
    title.id = 'equation-editor-title';
    title.innerHTML = '🧮 <span class="title-text">Advanced Equation Editor</span>';

    const subtitle = document.createElement('div');
    subtitle.className = 'Stickara-editor-subtitle';
    subtitle.textContent = 'Create beautiful mathematical expressions';

    titleContainer.appendChild(title);
    titleContainer.appendChild(subtitle);

    // Header controls
    const headerControls = document.createElement('div');
    headerControls.className = 'Stickara-header-controls';

    const closeBtn = document.createElement('button');
    closeBtn.className = 'Stickara-header-btn close-btn';
    closeBtn.innerHTML = '×';
    closeBtn.title = 'Close equation editor (Esc)';
    closeBtn.addEventListener('click', closeEquationEditor);

    headerControls.appendChild(closeBtn);

    header.appendChild(titleContainer);
    header.appendChild(headerControls);

    // Create main content area
    const mainContent = document.createElement('div');
    mainContent.className = 'Stickara-equation-editor-main';

    // Create left panel (input and preview)
    const leftPanel = createLeftPanel();

    // Create right panel (library and symbols)
    const rightPanel = createRightPanel();

    mainContent.appendChild(leftPanel);
    mainContent.appendChild(rightPanel);

    // Create footer with action buttons
    const footer = createFooter();

    modalContent.appendChild(header);
    modalContent.appendChild(mainContent);
    modalContent.appendChild(footer);

    equationEditorModal.appendChild(modalContent);
    document.body.appendChild(equationEditorModal);

    // Add event listeners
    equationEditorModal.addEventListener('click', (e) => {
        if (e.target === equationEditorModal) {
            closeEquationEditor();
        }
    });

    document.addEventListener('keydown', handleEquationEditorKeydown);
}

/**
 * Creates the left panel with LaTeX input and preview
 */
function createLeftPanel() {
    const leftPanel = document.createElement('div');
    leftPanel.className = 'Stickara-equation-editor-left';

    // LaTeX input section
    const inputSection = document.createElement('div');
    inputSection.className = 'Stickara-equation-input-section';

    const inputLabel = document.createElement('label');
    inputLabel.textContent = 'LaTeX Code:';
    inputLabel.htmlFor = 'equation-latex-input';

    latexInput = document.createElement('textarea');
    latexInput.id = 'equation-latex-input';
    latexInput.className = 'Stickara-equation-latex-input';
    latexInput.placeholder = 'Enter LaTeX code here (e.g., E = mc^2)';
    latexInput.addEventListener('input', updateEquationPreview);
    latexInput.addEventListener('keydown', handleLatexInputKeydown);

    inputSection.appendChild(inputLabel);
    inputSection.appendChild(latexInput);

    // Preview section
    const previewSection = document.createElement('div');
    previewSection.className = 'Stickara-equation-preview-section';

    const previewLabel = document.createElement('label');
    previewLabel.textContent = 'Preview:';

    equationPreview = document.createElement('div');
    equationPreview.className = 'Stickara-equation-preview';
    equationPreview.innerHTML = '<span style="color: #999; font-style: italic;">Enter LaTeX code to see preview</span>';

    previewSection.appendChild(previewLabel);
    previewSection.appendChild(equationPreview);

    leftPanel.appendChild(inputSection);
    leftPanel.appendChild(previewSection);

    return leftPanel;
}

/**
 * Creates the right panel with equation library and symbol palette
 */
function createRightPanel() {
    const rightPanel = document.createElement('div');
    rightPanel.className = 'Stickara-equation-editor-right';

    // Create enhanced tabs with icons and indicators
    const tabContainer = document.createElement('div');
    tabContainer.className = 'Stickara-equation-tabs enhanced-tabs';

    const libraryTab = createEnhancedTab('📚', 'Library', 'library', 'Browse equation templates', true);
    const symbolsTab = createEnhancedTab('∑', 'Symbols', 'symbols', 'Insert mathematical symbols');
    const recentTab = createEnhancedTab('🕒', 'Recent', 'recent', 'Recently used equations');
    const favoritesTab = createEnhancedTab('⭐', 'Favorites', 'favorites', 'Your favorite equations');
    const userTab = createEnhancedTab('💾', 'Saved', 'user', 'Your saved equations');

    tabContainer.appendChild(libraryTab);
    tabContainer.appendChild(symbolsTab);
    tabContainer.appendChild(recentTab);
    tabContainer.appendChild(favoritesTab);
    tabContainer.appendChild(userTab);

    // Create content areas
    const contentContainer = document.createElement('div');
    contentContainer.className = 'Stickara-equation-content';

    // Library content
    const libraryContent = createLibraryContent();
    libraryContent.id = 'library-content';
    libraryContent.className = 'Stickara-equation-tab-content active';

    // Symbols content
    const symbolsContent = createSymbolsContent();
    symbolsContent.id = 'symbols-content';
    symbolsContent.className = 'Stickara-equation-tab-content';

    // Recent equations content
    const recentContent = createRecentContent();
    recentContent.id = 'recent-content';
    recentContent.className = 'Stickara-equation-tab-content';

    // Favorites content
    const favoritesContent = createFavoritesContent();
    favoritesContent.id = 'favorites-content';
    favoritesContent.className = 'Stickara-equation-tab-content';

    // User equations content
    const userContent = createUserContent();
    userContent.id = 'user-content';
    userContent.className = 'Stickara-equation-tab-content';

    contentContainer.appendChild(libraryContent);
    contentContainer.appendChild(symbolsContent);
    contentContainer.appendChild(recentContent);
    contentContainer.appendChild(favoritesContent);
    contentContainer.appendChild(userContent);

    rightPanel.appendChild(tabContainer);
    rightPanel.appendChild(contentContainer);

    return rightPanel;
}

/**
 * Creates the equation library content
 */
function createLibraryContent() {
    const container = document.createElement('div');
    container.className = 'Stickara-equation-library';

    // Search box
    const searchContainer = document.createElement('div');
    searchContainer.className = 'Stickara-equation-search';

    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'Search equations...';
    searchInput.className = 'Stickara-equation-search-input';
    searchInput.addEventListener('input', (e) => filterEquations(e.target.value));

    searchContainer.appendChild(searchInput);

    // Categories
    const categoriesContainer = document.createElement('div');
    categoriesContainer.className = 'Stickara-equation-categories';

    Object.keys(EQUATION_CATEGORIES).forEach(categoryName => {
        const categoryDiv = document.createElement('div');
        categoryDiv.className = 'Stickara-equation-category';

        const categoryHeader = document.createElement('h4');
        categoryHeader.className = 'Stickara-equation-category-header';
        categoryHeader.textContent = categoryName;
        categoryHeader.addEventListener('click', () => toggleCategory(categoryDiv));

        const categoryContent = document.createElement('div');
        categoryContent.className = 'Stickara-equation-category-content';

        EQUATION_CATEGORIES[categoryName].forEach(equation => {
            const equationItem = document.createElement('div');
            equationItem.className = 'Stickara-equation-item';
            equationItem.setAttribute('data-latex', equation.latex);
            equationItem.setAttribute('data-name', equation.name.toLowerCase());
            equationItem.setAttribute('data-description', equation.description.toLowerCase());

            const equationName = document.createElement('div');
            equationName.className = 'Stickara-equation-name';
            equationName.textContent = equation.name;

            const equationPreview = document.createElement('div');
            equationPreview.className = 'Stickara-equation-item-preview';

            // Render equation preview
            try {
                if (typeof katex !== 'undefined') {
                    katex.render(equation.latex, equationPreview, {
                        throwOnError: false,
                        displayMode: false
                    });
                } else {
                    equationPreview.textContent = equation.latex;
                }
            } catch (e) {
                equationPreview.textContent = equation.latex;
            }

            const equationDescription = document.createElement('div');
            equationDescription.className = 'Stickara-equation-description';
            equationDescription.textContent = equation.description;

            equationItem.appendChild(equationName);
            equationItem.appendChild(equationPreview);
            equationItem.appendChild(equationDescription);

            equationItem.addEventListener('click', () => insertEquationFromLibrary(equation.latex));

            categoryContent.appendChild(equationItem);
        });

        categoryDiv.appendChild(categoryHeader);
        categoryDiv.appendChild(categoryContent);
        categoriesContainer.appendChild(categoryDiv);
    });

    container.appendChild(searchContainer);
    container.appendChild(categoriesContainer);

    return container;
}

/**
 * Creates the symbols palette content
 */
function createSymbolsContent() {
    const container = document.createElement('div');
    container.className = 'Stickara-equation-symbols';

    Object.keys(SYMBOL_CATEGORIES).forEach(categoryName => {
        const categoryDiv = document.createElement('div');
        categoryDiv.className = 'Stickara-symbol-category';

        const categoryHeader = document.createElement('h4');
        categoryHeader.className = 'Stickara-symbol-category-header';
        categoryHeader.textContent = categoryName;

        const symbolsGrid = document.createElement('div');
        symbolsGrid.className = 'Stickara-symbols-grid';

        SYMBOL_CATEGORIES[categoryName].forEach(symbol => {
            const symbolButton = document.createElement('button');
            symbolButton.className = 'Stickara-symbol-button';
            symbolButton.textContent = symbol.symbol;
            symbolButton.title = `${symbol.name} (${symbol.latex})`;
            symbolButton.addEventListener('click', () => insertSymbol(symbol.latex));

            symbolsGrid.appendChild(symbolButton);
        });

        categoryDiv.appendChild(categoryHeader);
        categoryDiv.appendChild(symbolsGrid);
        container.appendChild(categoryDiv);
    });

    return container;
}

/**
 * Creates the recent equations content
 */
function createRecentContent() {
    const container = document.createElement('div');
    container.className = 'Stickara-recent-equations';

    const header = document.createElement('div');
    header.className = 'Stickara-recent-header';

    const title = document.createElement('h4');
    title.innerHTML = '🕒 Recently Used';

    const clearBtn = document.createElement('button');
    clearBtn.className = 'Stickara-clear-recent-btn';
    clearBtn.textContent = 'Clear All';
    clearBtn.title = 'Clear recent equations';
    clearBtn.addEventListener('click', clearRecentEquations);

    header.appendChild(title);
    header.appendChild(clearBtn);

    const recentList = document.createElement('div');
    recentList.className = 'Stickara-recent-list';
    recentList.id = 'recent-equations-list';

    container.appendChild(header);
    container.appendChild(recentList);

    return container;
}

/**
 * Creates the favorites content
 */
function createFavoritesContent() {
    const container = document.createElement('div');
    container.className = 'Stickara-favorites-equations';

    const header = document.createElement('div');
    header.className = 'Stickara-favorites-header';

    const title = document.createElement('h4');
    title.innerHTML = '⭐ Favorite Equations';

    header.appendChild(title);

    const favoritesList = document.createElement('div');
    favoritesList.className = 'Stickara-favorites-list';
    favoritesList.id = 'favorites-equations-list';

    container.appendChild(header);
    container.appendChild(favoritesList);

    return container;
}

/**
 * Creates the user equations content
 */
function createUserContent() {
    const container = document.createElement('div');
    container.className = 'Stickara-user-equations';

    // Header with add button
    const header = document.createElement('div');
    header.className = 'Stickara-user-equations-header';

    const title = document.createElement('h4');
    title.textContent = 'My Saved Equations';

    const addButton = document.createElement('button');
    addButton.className = 'Stickara-add-equation-btn';
    addButton.textContent = '+ Save Current';
    addButton.title = 'Save current equation to library';
    addButton.addEventListener('click', saveCurrentEquation);

    header.appendChild(title);
    header.appendChild(addButton);

    // User equations list
    const equationsList = document.createElement('div');
    equationsList.className = 'Stickara-user-equations-list';
    equationsList.id = 'user-equations-list';

    container.appendChild(header);
    container.appendChild(equationsList);

    return container;
}

/**
 * Creates the footer with action buttons
 */
function createFooter() {
    const footer = document.createElement('div');
    footer.className = 'Stickara-equation-editor-footer';

    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'Stickara-equation-editor-buttons';

    const cancelBtn = document.createElement('button');
    cancelBtn.className = 'Stickara-equation-btn Stickara-equation-btn-secondary';
    cancelBtn.textContent = 'Cancel';
    cancelBtn.addEventListener('click', closeEquationEditor);

    const clearBtn = document.createElement('button');
    clearBtn.className = 'Stickara-equation-btn Stickara-equation-btn-secondary';
    clearBtn.textContent = 'Clear';
    clearBtn.addEventListener('click', clearEquation);

    const insertBtn = document.createElement('button');
    insertBtn.className = 'Stickara-equation-btn Stickara-equation-btn-primary';
    insertBtn.textContent = 'Insert Equation';
    insertBtn.addEventListener('click', insertEquationFromEditor);

    buttonContainer.appendChild(cancelBtn);
    buttonContainer.appendChild(clearBtn);
    buttonContainer.appendChild(insertBtn);

    footer.appendChild(buttonContainer);

    return footer;
}

// ===== EVENT HANDLERS AND UTILITY FUNCTIONS =====

/**
 * Updates the equation preview when LaTeX input changes
 */
function updateEquationPreview() {
    if (!latexInput || !equationPreview) return;

    const latex = latexInput.value.trim();

    if (!latex) {
        equationPreview.innerHTML = '<span style="color: #999; font-style: italic;">Enter LaTeX code to see preview</span>';
        return;
    }

    try {
        if (typeof katex !== 'undefined') {
            // Clean the LaTeX input to avoid warnings
            const cleanLatex = latex.replace(/\\\\/g, '\\\\\\\\').replace(/\\newline/g, '');

            katex.render(cleanLatex, equationPreview, {
                throwOnError: false,
                displayMode: true,
                output: 'html',
                strict: false, // Allow more flexible LaTeX
                trust: false,
                macros: {
                    "\\RR": "\\mathbb{R}",
                    "\\NN": "\\mathbb{N}",
                    "\\ZZ": "\\mathbb{Z}",
                    "\\QQ": "\\mathbb{Q}",
                    "\\CC": "\\mathbb{C}"
                }
            });
        } else {
            equationPreview.innerHTML = `<span style="color: red;">KaTeX not loaded: ${latex}</span>`;
        }
    } catch (e) {
        equationPreview.innerHTML = `<span style="color: red;">Invalid LaTeX: ${latex}</span>`;
        console.warn('Stickara: LaTeX rendering error:', e);
    }
}

/**
 * Switches between tabs in the right panel with enhanced animations
 */
function switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.Stickara-equation-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // Find and activate the correct tab
    const targetTab = document.querySelector(`[data-tab="${tabName}"]`);
    if (targetTab) {
        targetTab.classList.add('active');
    }

    // Update content areas with fade animation
    document.querySelectorAll('.Stickara-equation-tab-content').forEach(content => {
        content.style.opacity = '0';
        content.style.transform = 'translateY(10px)';
        setTimeout(() => {
            content.classList.remove('active');
        }, 150);
    });

    // Show new content
    setTimeout(() => {
        const newContent = document.getElementById(`${tabName}-content`);
        if (newContent) {
            newContent.classList.add('active');
            newContent.style.opacity = '1';
            newContent.style.transform = 'translateY(0)';

            // Refresh content based on tab
            refreshTabContent(tabName);
        }
    }, 150);
}

/**
 * Refreshes content for specific tabs
 */
function refreshTabContent(tabName) {
    switch (tabName) {
        case 'recent':
            refreshRecentEquations();
            break;
        case 'favorites':
            refreshFavoriteEquations();
            break;
        case 'user':
            refreshUserEquationsList();
            break;
    }
}

/**
 * Toggles category expansion in the library
 */
function toggleCategory(categoryDiv) {
    const content = categoryDiv.querySelector('.Stickara-equation-category-content');
    const header = categoryDiv.querySelector('.Stickara-equation-category-header');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        header.classList.add('expanded');
    } else {
        content.style.display = 'none';
        header.classList.remove('expanded');
    }
}

/**
 * Filters equations in the library based on search term
 */
function filterEquations(searchTerm) {
    const term = searchTerm.toLowerCase();
    const items = document.querySelectorAll('.Stickara-equation-item');

    items.forEach(item => {
        const name = item.getAttribute('data-name') || '';
        const description = item.getAttribute('data-description') || '';
        const latex = item.getAttribute('data-latex') || '';

        if (name.includes(term) || description.includes(term) || latex.toLowerCase().includes(term)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });

    // Show/hide categories based on visible items
    document.querySelectorAll('.Stickara-equation-category').forEach(category => {
        const visibleItems = category.querySelectorAll('.Stickara-equation-item[style*="block"], .Stickara-equation-item:not([style*="none"])');
        category.style.display = visibleItems.length > 0 ? 'block' : 'none';
    });
}

/**
 * Inserts an equation from the library into the LaTeX input
 */
function insertEquationFromLibrary(latex, name = '') {
    if (latexInput) {
        latexInput.value = latex;
        latexInput.focus();
        updateEquationPreview();

        // Add to recent equations
        addToRecent(latex, name);

        // Add visual feedback
        showInsertFeedback();
    }
}

/**
 * Shows visual feedback when equation is inserted
 */
function showInsertFeedback() {
    if (!equationPreview) {
        console.log('Stickara: Preview element not found for feedback');
        return;
    }

    equationPreview.style.transform = 'scale(1.05)';
    equationPreview.style.boxShadow = '0 0 20px rgba(127, 243, 98, 0.3)';

    setTimeout(() => {
        if (equationPreview) {
            equationPreview.style.transform = 'scale(1)';
            equationPreview.style.boxShadow = '';
        }
    }, 300);
}

/**
 * Inserts a symbol at the cursor position in the LaTeX input
 */
function insertSymbol(latex) {
    if (!latexInput) return;

    const start = latexInput.selectionStart;
    const end = latexInput.selectionEnd;
    const currentValue = latexInput.value;

    // Insert the symbol at cursor position
    const newValue = currentValue.substring(0, start) + latex + currentValue.substring(end);
    latexInput.value = newValue;

    // Move cursor to end of inserted text
    const newCursorPos = start + latex.length;
    latexInput.setSelectionRange(newCursorPos, newCursorPos);
    latexInput.focus();

    updateEquationPreview();
}

/**
 * Saves the current equation to user library
 */
function saveCurrentEquation() {
    if (!latexInput) return;

    const latex = latexInput.value.trim();
    if (!latex) {
        alert('Please enter a LaTeX equation before saving.');
        return;
    }

    const name = prompt('Enter a name for this equation:');
    if (!name) return;

    const description = prompt('Enter a description (optional):') || '';

    const userEquation = {
        id: Date.now().toString(),
        name: name,
        latex: latex,
        description: description,
        created: new Date().toISOString()
    };

    userEquations.push(userEquation);
    saveUserEquations();
    refreshUserEquationsList();

    // Show success message
    alert('Equation saved successfully!');
}

/**
 * Loads user equations from storage
 */
function loadUserEquations() {
    try {
        const stored = localStorage.getItem('Stickara-user-equations');
        userEquations = stored ? JSON.parse(stored) : [];
        refreshUserEquationsList();
    } catch (e) {
        console.error('Error loading user equations:', e);
        userEquations = [];
    }
}

/**
 * Saves user equations to storage
 */
function saveUserEquations() {
    try {
        localStorage.setItem('Stickara-user-equations', JSON.stringify(userEquations));
    } catch (e) {
        console.error('Error saving user equations:', e);
    }
}

/**
 * Refreshes the user equations list display
 */
function refreshUserEquationsList() {
    const list = document.getElementById('user-equations-list');
    if (!list) return;

    list.innerHTML = '';

    if (userEquations.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'Stickara-empty-message';
        emptyMessage.textContent = 'No saved equations yet. Save your current equation to get started!';
        list.appendChild(emptyMessage);
        return;
    }

    userEquations.forEach(equation => {
        const item = document.createElement('div');
        item.className = 'Stickara-user-equation-item';

        const header = document.createElement('div');
        header.className = 'Stickara-user-equation-header';

        const name = document.createElement('div');
        name.className = 'Stickara-user-equation-name';
        name.textContent = equation.name;

        const actions = document.createElement('div');
        actions.className = 'Stickara-user-equation-actions';

        const useBtn = document.createElement('button');
        useBtn.className = 'Stickara-user-equation-btn';
        useBtn.textContent = 'Use';
        useBtn.title = 'Use this equation';
        useBtn.addEventListener('click', () => insertEquationFromLibrary(equation.latex));

        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'Stickara-user-equation-btn Stickara-user-equation-delete';
        deleteBtn.textContent = '×';
        deleteBtn.title = 'Delete this equation';
        deleteBtn.addEventListener('click', () => deleteUserEquation(equation.id));

        actions.appendChild(useBtn);
        actions.appendChild(deleteBtn);

        header.appendChild(name);
        header.appendChild(actions);

        const preview = document.createElement('div');
        preview.className = 'Stickara-user-equation-preview';

        try {
            if (typeof katex !== 'undefined') {
                katex.render(equation.latex, preview, {
                    throwOnError: false,
                    displayMode: false
                });
            } else {
                preview.textContent = equation.latex;
            }
        } catch (e) {
            preview.textContent = equation.latex;
        }

        if (equation.description) {
            const description = document.createElement('div');
            description.className = 'Stickara-user-equation-description';
            description.textContent = equation.description;
            item.appendChild(header);
            item.appendChild(preview);
            item.appendChild(description);
        } else {
            item.appendChild(header);
            item.appendChild(preview);
        }

        list.appendChild(item);
    });
}

/**
 * Deletes a user equation
 */
function deleteUserEquation(id) {
    if (!confirm('Are you sure you want to delete this equation?')) return;

    userEquations = userEquations.filter(eq => eq.id !== id);
    saveUserEquations();
    refreshUserEquationsList();
}

/**
 * Clears the current equation
 */
function clearEquation() {
    if (latexInput) {
        latexInput.value = '';
        latexInput.focus();
        updateEquationPreview();
    }
}

/**
 * Inserts the equation into the note and closes the editor with normal styling
 */
function insertEquationFromEditor() {
    if (!latexInput || !currentEquationSpan) return;

    const latex = latexInput.value.trim();
    if (!latex) {
        alert('Please enter a LaTeX equation');
        return;
    }

    try {
        // Add to recent equations
        addToRecent(latex, 'Custom Equation');

        // Update the equation span with new LaTeX
        if (typeof updateEquationSpan === 'function') {
            updateEquationSpan(currentEquationSpan, latex);
            // The updateEquationSpan function now handles all styling
        }

        closeEquationEditor();
        showInsertFeedback();
    } catch (e) {
        console.error('Error inserting equation:', e);
        alert('Error inserting equation. Please check your LaTeX syntax.');
    }
}

/**
 * Closes the equation editor modal with smooth animation
 */
function closeEquationEditor() {
    if (equationEditorModal) {
        // Add closing animation
        const modalContent = equationEditorModal.querySelector('.Stickara-equation-editor-content');
        if (modalContent) {
            modalContent.style.animation = 'modalSlideOut 0.2s ease-in forwards';
        }

        // Hide modal after animation
        setTimeout(() => {
            equationEditorModal.style.display = 'none';
            equationEditorModal.setAttribute('aria-hidden', 'true');

            // Reset animation for next time
            if (modalContent) {
                modalContent.style.animation = '';
            }
        }, 200);

        document.removeEventListener('keydown', handleEquationEditorKeydown);
    }

    currentEquationSpan = null;

    // Return focus to note text area
    if (typeof noteText !== 'undefined' && noteText) {
        noteText.focus();
    }
}

/**
 * Handles keyboard shortcuts in the equation editor
 */
function handleEquationEditorKeydown(e) {
    if (!equationEditorModal || equationEditorModal.style.display === 'none') return;

    // Escape key - close editor
    if (e.key === 'Escape') {
        e.preventDefault();
        closeEquationEditor();
        return;
    }

    // Ctrl+Enter - insert equation
    if (e.key === 'Enter' && e.ctrlKey) {
        e.preventDefault();
        insertEquationFromEditor();
        return;
    }

    // Tab navigation within modal
    if (e.key === 'Tab') {
        const focusableElements = equationEditorModal.querySelectorAll(
            'button, input, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }
}

/**
 * Handles keyboard shortcuts in the LaTeX input
 */
function handleLatexInputKeydown(e) {
    // Ctrl+Enter - insert equation
    if (e.key === 'Enter' && e.ctrlKey) {
        e.preventDefault();
        insertEquationFromEditor();
        return;
    }

    // Tab - switch to symbols tab for quick access
    if (e.key === 'Tab' && !e.shiftKey && !e.ctrlKey) {
        e.preventDefault();
        switchTab('symbols');
        // Focus first symbol button
        const firstSymbol = document.querySelector('.Stickara-symbol-button');
        if (firstSymbol) firstSymbol.focus();
    }
}

// ===== ENHANCED UTILITY FUNCTIONS =====

/**
 * Creates an enhanced tab with icon and tooltip
 */
function createEnhancedTab(icon, text, tabId, tooltip, isActive = false) {
    const tab = document.createElement('button');
    tab.className = `Stickara-equation-tab enhanced-tab ${isActive ? 'active' : ''}`;
    tab.setAttribute('data-tab', tabId);
    tab.title = tooltip;

    tab.innerHTML = `
        <span class="tab-icon">${icon}</span>
        <span class="tab-text">${text}</span>
        <span class="tab-indicator"></span>
    `;

    tab.addEventListener('click', () => switchTab(tabId));
    return tab;
}

// Drag functionality removed - modal now appears centered

/**
 * Adds equation to recent list
 */
function addToRecent(latex, name = '') {
    const recentItem = {
        latex: latex,
        name: name || `Equation ${recentEquations.length + 1}`,
        timestamp: Date.now()
    };

    // Remove if already exists
    recentEquations = recentEquations.filter(item => item.latex !== latex);

    // Add to beginning
    recentEquations.unshift(recentItem);

    // Keep only last 20
    recentEquations = recentEquations.slice(0, 20);

    // Save to storage
    try {
        localStorage.setItem('Stickara-recent-equations', JSON.stringify(recentEquations));
    } catch (e) {
        console.error('Error saving recent equations:', e);
    }
}

/**
 * Loads recent equations from storage
 */
function loadRecentEquations() {
    try {
        const stored = localStorage.getItem('Stickara-recent-equations');
        recentEquations = stored ? JSON.parse(stored) : [];
    } catch (e) {
        console.error('Error loading recent equations:', e);
        recentEquations = [];
    }
}

/**
 * Loads favorite equations from storage
 */
function loadFavoriteEquations() {
    try {
        const stored = localStorage.getItem('Stickara-favorite-equations');
        favoriteEquations = stored ? JSON.parse(stored) : [];
    } catch (e) {
        console.error('Error loading favorite equations:', e);
        favoriteEquations = [];
    }
}

/**
 * Refreshes the recent equations list
 */
function refreshRecentEquations() {
    const list = document.getElementById('recent-equations-list');
    if (!list) return;

    list.innerHTML = '';

    if (recentEquations.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'Stickara-empty-message';
        emptyMessage.innerHTML = '🕒 No recent equations yet.<br>Start using equations to see them here!';
        list.appendChild(emptyMessage);
        return;
    }

    recentEquations.forEach((equation, index) => {
        const item = createEquationListItem(equation, 'recent', index);
        list.appendChild(item);
    });
}

/**
 * Refreshes the favorites list
 */
function refreshFavoriteEquations() {
    const list = document.getElementById('favorites-equations-list');
    if (!list) return;

    list.innerHTML = '';

    if (favoriteEquations.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'Stickara-empty-message';
        emptyMessage.innerHTML = '⭐ No favorite equations yet.<br>Click the star icon to add favorites!';
        list.appendChild(emptyMessage);
        return;
    }

    favoriteEquations.forEach((equation, index) => {
        const item = createEquationListItem(equation, 'favorite', index);
        list.appendChild(item);
    });
}

/**
 * Creates a list item for equations (recent, favorites, user)
 */
function createEquationListItem(equation, type, index) {
    const item = document.createElement('div');
    item.className = `Stickara-equation-list-item ${type}-item`;

    const header = document.createElement('div');
    header.className = 'Stickara-equation-item-header';

    const name = document.createElement('div');
    name.className = 'Stickara-equation-item-name';
    name.textContent = equation.name;

    const actions = document.createElement('div');
    actions.className = 'Stickara-equation-item-actions';

    // Use button
    const useBtn = document.createElement('button');
    useBtn.className = 'Stickara-equation-action-btn use-btn';
    useBtn.innerHTML = '📝';
    useBtn.title = 'Use this equation';
    useBtn.addEventListener('click', () => insertEquationFromLibrary(equation.latex, equation.name));

    // Favorite/unfavorite button
    if (type !== 'favorite') {
        const favoriteBtn = document.createElement('button');
        favoriteBtn.className = 'Stickara-equation-action-btn favorite-btn';
        favoriteBtn.innerHTML = '⭐';
        favoriteBtn.title = 'Add to favorites';
        favoriteBtn.addEventListener('click', () => toggleFavorite(equation));
        actions.appendChild(favoriteBtn);
    }

    // Delete button for recent and favorites
    if (type === 'recent') {
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'Stickara-equation-action-btn delete-btn';
        deleteBtn.innerHTML = '🗑️';
        deleteBtn.title = 'Remove from recent';
        deleteBtn.addEventListener('click', () => removeFromRecent(index));
        actions.appendChild(deleteBtn);
    } else if (type === 'favorite') {
        const unfavoriteBtn = document.createElement('button');
        unfavoriteBtn.className = 'Stickara-equation-action-btn unfavorite-btn';
        unfavoriteBtn.innerHTML = '💔';
        unfavoriteBtn.title = 'Remove from favorites';
        unfavoriteBtn.addEventListener('click', () => removeFromFavorites(index));
        actions.appendChild(unfavoriteBtn);
    }

    actions.appendChild(useBtn);

    header.appendChild(name);
    header.appendChild(actions);

    // Preview
    const preview = document.createElement('div');
    preview.className = 'Stickara-equation-item-preview';

    try {
        if (typeof katex !== 'undefined') {
            katex.render(equation.latex, preview, {
                throwOnError: false,
                displayMode: false
            });
        } else {
            preview.textContent = equation.latex;
        }
    } catch (e) {
        preview.textContent = equation.latex;
    }

    // Timestamp for recent items
    if (type === 'recent' && equation.timestamp) {
        const timestamp = document.createElement('div');
        timestamp.className = 'Stickara-equation-timestamp';
        timestamp.textContent = formatTimestamp(equation.timestamp);
        item.appendChild(header);
        item.appendChild(preview);
        item.appendChild(timestamp);
    } else {
        item.appendChild(header);
        item.appendChild(preview);
    }

    return item;
}

/**
 * Formats timestamp for display
 */
function formatTimestamp(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return new Date(timestamp).toLocaleDateString();
}

/**
 * Clears all recent equations
 */
function clearRecentEquations() {
    if (confirm('Clear all recent equations?')) {
        recentEquations = [];
        localStorage.removeItem('Stickara-recent-equations');
        refreshRecentEquations();
    }
}

/**
 * Toggles favorite status of an equation
 */
function toggleFavorite(equation) {
    const exists = favoriteEquations.find(fav => fav.latex === equation.latex);
    if (exists) {
        removeFromFavorites(favoriteEquations.indexOf(exists));
    } else {
        favoriteEquations.push({
            ...equation,
            favorited: Date.now()
        });
        saveFavoriteEquations();
        refreshFavoriteEquations();
    }
}

/**
 * Removes equation from recent list
 */
function removeFromRecent(index) {
    recentEquations.splice(index, 1);
    try {
        localStorage.setItem('Stickara-recent-equations', JSON.stringify(recentEquations));
    } catch (e) {
        console.error('Error saving recent equations:', e);
    }
    refreshRecentEquations();
}

/**
 * Removes equation from favorites
 */
function removeFromFavorites(index) {
    favoriteEquations.splice(index, 1);
    saveFavoriteEquations();
    refreshFavoriteEquations();
}

/**
 * Saves favorite equations to storage
 */
function saveFavoriteEquations() {
    try {
        localStorage.setItem('Stickara-favorite-equations', JSON.stringify(favoriteEquations));
    } catch (e) {
        console.error('Error saving favorite equations:', e);
    }
}

// ===== INITIALIZATION =====

/**
 * Initialize equation editor when DOM is ready
 */
function initializeEquationEditor() {
    // Load stored data
    loadRecentEquations();
    loadFavoriteEquations();

    // The equation editor will be created when first needed
    console.log('Stickara: Enhanced equation editor script loaded and initialized');
    console.log('Stickara: openAdvancedEquationEditor function available:', typeof openAdvancedEquationEditor === 'function');
    console.log('Stickara: KaTeX available:', typeof katex !== 'undefined');
    console.log('Stickara: Recent equations loaded:', recentEquations.length);
    console.log('Stickara: Favorite equations loaded:', favoriteEquations.length);
}

// Initialize when the script loads
if (document.readyState === 'loading') {
    console.log('Stickara: Document still loading, waiting for DOMContentLoaded');
    document.addEventListener('DOMContentLoaded', initializeEquationEditor);
} else {
    console.log('Stickara: Document already loaded, initializing immediately');
    initializeEquationEditor();
}

// --- END OF FILE content-equation-editor.js ---
