// --- START OF FILE content-storage.js ---

// Assume necessary variables (like currentUrl, stateKey, STORAGE_KEY_PREFIX, currentNoteIndex, notes, saveTimeout,
// DEFAULT_COLOR, DEFAULT_OPACITY, DEFAULT_FONT_SIZE, note<PERSON>ontainer, noteText, noteHeader, tagsInput, reminderInput,
// timestampSpan, noteSwitcher, isFullyInitialized, noteTitleInput, NOTE_TITLE_INPUT_ID)
// and functions (like applyNoteDataToUI, updateActiveSwatch, updateActiveOpacity, updatePinButtonState,
// updateGlobalPinButtonState, updateMinimizeButtonState, calculateTextAreaHeight, handleFormattingState,
// resetFlashcardState, updateNoteSwitcher, showNote, hideNote, showSaveFeedback?, showStatus?, formatRelativeTime?,
// getPlainText) // <<< ADDED getPlainText dependency
// are defined elsewhere (in state.js, utils.js, ui.js) or imported/available in the global scope.

/**
 * Checks if web workers are supported
 * @returns {boolean} True if workers can be used safely
 */
function checkWorkerSupport() {
    // Check if Worker constructor is available
    if (typeof Worker === 'undefined') {
        console.log('Stickara: Worker constructor not available');
        return false;
    }

    // Check if we're on a site with strict CSP (like ChatGPT)
    const url = window.location.href;
    if (url.includes('chatgpt.com') || url.includes('chat.openai.com') || url.includes('aistudio.google.com')) {
        console.log('Stickara: Detected site with strict CSP, disabling workers');
        return false;
    }

    // Try to create a simple test worker to check CSP
    try {
        const testScript = 'self.postMessage("test");';
        const blob = new Blob([testScript], { type: 'application/javascript' });
        const testWorker = new Worker(URL.createObjectURL(blob));
        testWorker.terminate();
        return true;
    } catch (error) {
        console.log('Stickara: Worker creation blocked by CSP:', error.message);
        return false;
    }
}

/**
 * Creates a default data structure for a new note.
 * @returns {object} The default note data object.
 */
function createDefaultNoteData() {
    // Get default size and position from settings if available
    let defaultSize = { width: '340px', height: '380px' };
    let defaultPosition = { top: '100px', left: '', right: '24px', bottom: 'auto' };

    // Use settings if available
    if (window.StickaraSettings && typeof window.StickaraSettings.getDefaultNoteSize === 'function') {
        const settingsSize = window.StickaraSettings.getDefaultNoteSize();
        defaultSize = {
            width: settingsSize.width || defaultSize.width,
            height: settingsSize.height || defaultSize.height
        };
    }

    if (window.StickaraSettings && typeof window.StickaraSettings.getDefaultNotePosition === 'function') {
        const settingsPosition = window.StickaraSettings.getDefaultNotePosition();
        defaultPosition = {
            top: settingsPosition.top || defaultPosition.top,
            left: defaultPosition.left, // Keep left empty by default
            right: settingsPosition.right || defaultPosition.right,
            bottom: defaultPosition.bottom // Keep bottom auto by default
        };
    }

    // Get preferred font size from existing notes
    let preferredFontSize = DEFAULT_FONT_SIZE;
    if (typeof notes !== 'undefined' && notes && Array.isArray(notes)) {
        // Find the most recent note with a preferred font size
        for (let i = notes.length - 1; i >= 0; i--) {
            if (notes[i] && typeof notes[i].preferredFontSize === 'number') {
                preferredFontSize = notes[i].preferredFontSize;
                break;
            }
        }
    }

    return {
        text: '',
        position: defaultPosition,
        size: defaultSize,
        color: DEFAULT_COLOR,
        opacity: DEFAULT_OPACITY,
        pinned: false,
        textSize: preferredFontSize + 'px',
        preferredFontSize: preferredFontSize, // Store user's last used font size preference
        globalFontSize: preferredFontSize, // Store global font size for the entire text area
        textShadow: false, // Text shadow effect toggle
        // fontFamily removed - now applied at text level
        // minimized property removed
        lastSaved: null,
        tags: [],
        reminder: null,
        globallyPinned: false,
        associatedVideoUrl: null,
        associatedVideoTitle: null,
        title: null,
        notebookId: null, // Default to no notebook (Ungrouped)
        calendarEventId: null, // For storing Google Calendar event ID

        isGlobal: false // Whether this note is global (works across all URLs)
    };
}

/**
 * Debounces the save operation. Schedules saveCurrentNote after a delay.
 * Checks if fully initialized before scheduling.
 * Now uses web workers for background processing when available.
 */
window.scheduleSave = function() {
    if (!isFullyInitialized) {
        return;
    }
    clearTimeout(saveTimeout);
    saveTimeout = setTimeout(() => {
        saveCurrentNote();
        saveState(); // Save state (currentNoteIndex, visibility) as well
    }, 1500); // Save after 1.5 seconds of inactivity
}

/**
 * Validates note content to prevent saving empty notes
 * @param {string} text - The note text content
 * @param {Array} tags - The note tags array
 * @param {boolean} globallyPinned - Whether the note is globally pinned
 * @returns {boolean} - True if note has valid content, false otherwise
 */
function validateNoteContent(text, tags, globallyPinned) {
    // Check if text has meaningful content (not just whitespace)
    const hasText = text && text.trim().length > 0;

    // Check if there are any tags
    const hasTags = tags && Array.isArray(tags) && tags.length > 0;

    // Check if note is globally pinned (this makes it worth saving even without content)
    const isPinned = globallyPinned === true;

    // Note is valid if it has text, tags, or is globally pinned
    return hasText || hasTags || isPinned;
}

/**
 * Saves the data of the currently active note (notes[currentNoteIndex]) to chrome.storage.local.
 * Sends necessary messages to background script (alarms, Drive upload).
 * Uses web workers for background processing when available.
 */
function saveCurrentNote() {
    // --- Prerequisite Checks ---
    if (!isFullyInitialized || !noteText || !noteContainer || !notes || typeof notes[currentNoteIndex] === 'undefined' || !noteTitleInput || !tagsInput || !reminderInput || !timestampSpan) {
        if (noteContainer?.classList?.contains('visible') && isFullyInitialized) {
             console.error(`Stickara CRITICAL FAIL: Cannot save note ${currentNoteIndex}: Prerequisites missing.`);
             if (typeof showStatus === 'function') showStatus('Save Failed: Critical Error!', 'error', 10000);
        }
        return;
    }

    // Get the existing note data object from cache to update it
    let noteData = notes[currentNoteIndex];
    if (typeof noteData !== 'object' || noteData === null) {
        console.warn(`Stickara: No note data found in cache for index ${currentNoteIndex} during save. Creating default.`);
        notes[currentNoteIndex] = createDefaultNoteData();
        noteData = notes[currentNoteIndex];
    }
    let errorDuringRead = false;

    // --- Update Note Data Fields (with error handling) ---

    // Custom Title
    try { noteData.title = noteTitleInput.value.trim() || null; } catch (e) { errorDuringRead = true; console.error(`ERROR reading noteTitleInput.value:`, e); noteData.title = noteData.title || null; }

    // Text Content - Note: We're storing HTML content, but it will be sanitized BEFORE storage
    try {
        // We need to store the HTML content for formatting, but we must sanitize it first
        const rawHTML = noteText.innerHTML;

        // Use our sanitization manager if available
        if (window.StickaraSanitizationManager && typeof window.StickaraSanitizationManager.sanitize === 'function') {
            // Sanitize the HTML content before storage
            const sanitizedHTML = window.StickaraSanitizationManager.sanitize(rawHTML);
            noteData.text = sanitizedHTML;
        }
        // Fallback to DOMPurify if available
        else if (window.DOMPurify) {
            // Sanitize the HTML content before storage
            const sanitizedHTML = window.DOMPurify.sanitize(rawHTML);
            noteData.text = sanitizedHTML;
        }
        // Fallback to StickaraDOMPurify if available
        else if (window.StickaraDOMPurify) {
            // Sanitize the HTML content before storage
            const sanitizedHTML = window.StickaraDOMPurify.sanitize(rawHTML);
            noteData.text = sanitizedHTML;
        }
        // Fallback to createSafeHTML if available
        else if (typeof createSafeHTML === 'function') {
            // This validates the content but doesn't modify it
            // We call it to check for malicious content, but we don't use the result directly
            createSafeHTML(rawHTML);
            // Store the original HTML - it will be sanitized by validateData
            noteData.text = rawHTML;
        } else {
            // If no sanitization is available, store as-is but log a warning
            console.warn("Stickara: No HTML sanitization available - security risk!");
            noteData.text = rawHTML;
        }
    } catch (e) {
        errorDuringRead = true;
        console.error(`CRITICAL ERROR reading noteText.innerHTML:`, e);
        if (typeof showStatus === 'function') showStatus('Save Failed: Cannot read note content!', 'error', 10000);
        return;
    }

    // --- Read other properties with fallbacks ---
    let currentStyle = {}; try { currentStyle = window.getComputedStyle(noteContainer); } catch(e) { errorDuringRead = true; }
    let rect = {}; try { rect = noteContainer.getBoundingClientRect(); } catch(e) { errorDuringRead = true; }

    const isPinned = noteContainer.classList.contains('pinned'); noteData.pinned = isPinned;
    const isGloballyPinned = noteContainer.classList.contains('globally-pinned'); noteData.globallyPinned = isGloballyPinned;
    const isMinimized = noteContainer.classList.contains('minimized'); noteData.minimized = isMinimized;
    const isGlobal = noteContainer.classList.contains('global-note'); noteData.isGlobal = isGlobal;

    // Position (use rect if available)
    if (rect.top !== undefined && rect.left !== undefined) {
         noteData.position = {
            top: (isPinned ? rect.top : (rect.top + window.scrollY)) + 'px',
            left: (isPinned ? rect.left : (rect.left + window.scrollX)) + 'px',
            right: 'auto', bottom: 'auto'
        };
    } else if (!noteData.position) { noteData.position = createDefaultNoteData().position; console.warn("Using default position."); }

    // Size (use style if available, preserve old size if minimized)
    if (currentStyle.width && currentStyle.height && !isMinimized) {
        noteData.size = { width: currentStyle.width, height: currentStyle.height };
    } else if (!isMinimized && !noteData.size) { noteData.size = createDefaultNoteData().size; console.warn("Using default size."); }
    // noteData.size remains unchanged if minimized

    // Color Theme
    const currentThemeMatch = noteContainer.className.match(/theme-(\w+)/);
    noteData.color = currentThemeMatch ? currentThemeMatch[1] : (noteData.color || DEFAULT_COLOR);

    // Opacity
    try { noteData.opacity = parseFloat(noteContainer.style.opacity) || (noteData.opacity || DEFAULT_OPACITY); }
    catch(e) { errorDuringRead = true; noteData.opacity = noteData.opacity || DEFAULT_OPACITY; }

    // Text Size
    try { noteData.textSize = noteText.style.fontSize || (noteData.textSize || (DEFAULT_FONT_SIZE + 'px')); }
    catch(e) { errorDuringRead = true; noteData.textSize = noteData.textSize || (DEFAULT_FONT_SIZE + 'px'); }

    // Text Shadow
    try { noteData.textShadow = noteText.classList.contains('Stickara-text-shadow-enabled'); }
    catch(e) { errorDuringRead = true; noteData.textShadow = noteData.textShadow || false; }

    // We no longer store global font family since it's applied at the text level

    // Tags
    try { noteData.tags = tagsInput.value.split(',').map(tag => tag.trim()).filter(Boolean); }
    catch(e) { errorDuringRead = true; noteData.tags = noteData.tags || []; }

    // Reminder
    let reminderTime = null;
    let addToCalendar = false; // <<< Get Calendar Checkbox state
    try {
        const calendarCheckbox = document.getElementById('Stickara-calendar-checkbox');
        addToCalendar = calendarCheckbox ? calendarCheckbox.checked : false;
        console.log("Calendar checkbox state during save:", addToCalendar);

        if (reminderInput.value) {
            const reminderDate = new Date(reminderInput.value);
            // Save if valid and not significantly in the past (allow buffer for editing)
            if (!isNaN(reminderDate.getTime()) && reminderDate.getTime() > (Date.now() - 300000)) { // 5 min buffer
                reminderTime = reminderDate.getTime();
            } else if (!isNaN(reminderDate.getTime())) {
                console.warn("Stickara: Reminder time is past.");
            } else {
                 console.warn("Stickara: Invalid reminder date entered.");
            }
        }
    } catch (e) { errorDuringRead = true; console.error("Error reading reminder input:", e); }
    noteData.reminder = reminderTime; // Store timestamp or null

    // Calendar Event ID (read from cache, updated by background)
    noteData.calendarEventId = notes[currentNoteIndex]?.calendarEventId || null;

    // Associated Video Info (Read from cache)
    noteData.associatedVideoUrl = notes[currentNoteIndex]?.associatedVideoUrl || null;
    noteData.associatedVideoTitle = notes[currentNoteIndex]?.associatedVideoTitle || null;

    // --- Notebook ID (Read from cache) ---
    noteData.notebookId = notes[currentNoteIndex]?.notebookId || null;
    // -------------------------------------



    // Always update lastSaved timestamp
    noteData.lastSaved = Date.now();

    if (errorDuringRead) console.warn(`[saveCurrentNote] Some read errors occurred. Saving with best effort.`);

    // Ensure isGlobal property matches the UI state
    noteData.isGlobal = noteContainer.classList.contains('global-note');

    // --- Validate note content before saving ---
    if (!validateNoteContent(noteData.text, noteData.tags, noteData.globallyPinned)) {
        console.log(`Stickara: Skipping save for note ${currentNoteIndex} - empty content`);
        if (typeof showStatus === 'function') {
            showStatus('Cannot save empty note. Add some text, tags, or pin the note globally.', 'info', 3000);
        }
        return;
    }

    // Use different key format for global notes
    let noteKey;
    if (noteData.isGlobal) {
        // For global notes, use the standard global note key format
        noteKey = `${STORAGE_KEY_PREFIX}global_note${currentNoteIndex}`;
    } else {
        // For URL-specific notes, use the raw URL directly like highlights do
        // This ensures URLs are preserved exactly as they appear in the browser
        const rawUrl = window.stickaraRawCurrentUrl || window.location.href;
        noteKey = `${STORAGE_KEY_PREFIX}${rawUrl}_note${currentNoteIndex}`;
        console.log(`Stickara: Saving note ${currentNoteIndex} with key: ${noteKey}`);
        console.log(`Stickara: Raw URL used for saving: ${rawUrl}`);
    }

    // Log which type of note we're saving
    console.log(`Stickara: Saving ${noteData.isGlobal ? 'global' : 'URL-specific'} note ${currentNoteIndex} with key ${noteKey}`);

    // Create a deep copy to prevent modification before async save completes
    const dataToSave = JSON.parse(JSON.stringify(noteData));

    // --- Prepare plain text for reminder message ---
    let notePlainTextForReminder = null;
    if (reminderTime && addToCalendar && typeof getPlainText === 'function') {
        notePlainTextForReminder = getPlainText(dataToSave.text); // Get plain text IF needed for calendar
    }
    // --------------------------------------------

    // --- Handle Alarm Setting/Clearing ---
    const alarmName = `reminder::${currentUrl}::${currentNoteIndex}`;
    chrome.runtime.sendMessage({ action: 'clearReminder', alarmName: alarmName })
        .then(() => {
            if (reminderTime) {
                 // If reminderTime is valid, set the new alarm
                 // <<< SEND PLAIN TEXT FOR CALENDAR EVENT >>>
                 console.log("Sending setReminder message with addToCalendar:", addToCalendar);
                 return chrome.runtime.sendMessage({
                    action: 'setReminder',
                    url: currentUrl,
                    noteIndex: currentNoteIndex,
                    reminderTime: reminderTime,
                    addToCalendar: addToCalendar,
                    noteTitle: dataToSave.title, // Use title from dataToSave
                    noteText: notePlainTextForReminder // Pass plain text
                });
            } // No return needed if reminderTime is null (clearing was sufficient)
        })
        .catch(e => console.error(`SB Reminder message chain error:`, e));
    // ------------------------------------

    // --- Check if we should use web workers ---
    const shouldUseWorkers = checkWorkerSupport();

    // --- Process note data in a web worker if available and supported ---
    if (shouldUseWorkers && window.StickaraWorkerManager) {
        console.log(`Stickara: Using web worker to process note ${currentNoteIndex} data`);

        // Use web worker for processing
        window.StickaraWorkerManager.runTask(
            'workers/storage-worker.js',
            'processNoteData',
            { noteData: dataToSave, options: { generateMetadata: true } }
        )
        .then(result => {
            // Use the processed data
            if (result && result.processedData && result.success) {
                console.log(`Stickara: Note ${currentNoteIndex} processed successfully by worker`);
                saveNoteToStorage(noteKey, result.processedData);
            } else {
                console.warn(`Stickara: Worker returned invalid result, using original data`);
                saveNoteToStorage(noteKey, dataToSave);
            }
        })
        .catch(error => {
            console.error(`Stickara: Error processing note ${currentNoteIndex} data in worker:`, error);
            // Fall back to saving the original data
            saveNoteToStorage(noteKey, dataToSave);
        });
    } else {
        // No worker available or workers not supported, save directly
        if (!shouldUseWorkers) {
            console.log(`Stickara: Workers not supported, saving note ${currentNoteIndex} data directly`);
        } else {
            console.log(`Stickara: No worker manager available, saving note ${currentNoteIndex} data directly`);
        }
        saveNoteToStorage(noteKey, dataToSave);
    }
}

/**
 * Saves note data to storage and triggers related actions
 * @param {string} noteKey - The note key
 * @param {Object} dataToSave - The note data to save
 */
function saveNoteToStorage(noteKey, dataToSave) {
    // --- Save to Storage using Standard Chrome Storage ---
    // NOTE: Bypassing Secure Storage utility due to persistence issues
    try {
        console.log(`Stickara: Saving note ${currentNoteIndex} directly to Chrome storage with key: ${noteKey}`);
        chrome.storage.local.set({ [noteKey]: dataToSave }, () => {
            const error = chrome.runtime.lastError;
            if (error) {
                console.error(`Stickara Error saving note locally:`, error.message);
                if (typeof showStatus === 'function') showStatus('Error saving note!', 'error');
            } else {
                console.log(`Stickara: Note ${currentNoteIndex} saved successfully with Chrome storage to key: ${noteKey}`);
                // Verify the save by immediately reading it back
                chrome.storage.local.get([noteKey], (verifyResult) => {
                    if (verifyResult[noteKey]) {
                        console.log(`Stickara: VERIFICATION SUCCESS - Note ${currentNoteIndex} found in storage after save`);
                    } else {
                        console.error(`Stickara: VERIFICATION FAILED - Note ${currentNoteIndex} NOT found in storage after save!`);
                    }
                });
                if (typeof showSaveFeedback === 'function') showSaveFeedback(currentNoteIndex);
                triggerDriveUpload(noteKey, dataToSave);
            }
        });
    } catch (e) {
        console.error(`Stickara: Exception in saveNoteToStorage:`, e);
        // Last resort fallback
        try {
            chrome.storage.local.set({ [noteKey]: dataToSave }, () => {
                const error = chrome.runtime.lastError;
                if (error) {
                    console.error(`Stickara Error saving note locally:`, error.message);
                    if (typeof showStatus === 'function') showStatus('Error saving note!', 'error');
                } else {
                    console.log(`Stickara: Note ${currentNoteIndex} saved successfully with last resort storage to key: ${noteKey}`);
                    // Verify the save by immediately reading it back
                    chrome.storage.local.get([noteKey], (verifyResult) => {
                        if (verifyResult[noteKey]) {
                            console.log(`Stickara: VERIFICATION SUCCESS - Note ${currentNoteIndex} found in storage after save`);
                        } else {
                            console.error(`Stickara: VERIFICATION FAILED - Note ${currentNoteIndex} NOT found in storage after save!`);
                        }
                    });
                    if (typeof showSaveFeedback === 'function') showSaveFeedback(currentNoteIndex);
                    triggerDriveUpload(noteKey, dataToSave);
                }
            });
        } catch (finalError) {
            console.error(`Stickara: Final exception saving note:`, finalError);
            if (typeof showStatus === 'function') showStatus('Error saving note!', 'error');
        }
    }
}

/**
 * Helper function to trigger Google Drive upload
 * @param {string} noteKey - The note key
 * @param {Object} noteData - The note data
 */
function triggerDriveUpload(noteKey, noteData) {
    try {
        // --- Trigger Google Drive upload ---
        if (window.StickaraSecureMessaging && typeof window.StickaraSecureMessaging.sendToBackground === 'function') {
            // Use secure messaging if available
            window.StickaraSecureMessaging.sendToBackground({
                action: 'uploadNoteToDrive',
                noteKey: noteKey,
                noteData: noteData
            }).catch(err => console.log("SB Secure Msg Send Error (Drive Upload):", err?.message));
        } else {
            // Fall back to standard messaging
            chrome.runtime.sendMessage({
                action: 'uploadNoteToDrive',
                noteKey: noteKey,
                noteData: noteData
            }).catch(err => console.log("SB Msg Send Error (Drive Upload):", err?.message));
        }
    } catch (e) {
        console.log("Stickara: Error triggering Drive upload:", e);
    }
}


/**
 * Saves both global and URL-specific versions of a note.
 * This ensures that both versions are preserved when switching between them.
 * @param {number} noteIndex - The index of the note to save
 * @param {Object} globalNoteData - The global note data to save
 * @param {Object} urlNoteData - The URL-specific note data to save
 * @param {Function} callback - Optional callback to run after saving
 */
function saveBothNoteVersions(noteIndex, globalNoteData, urlNoteData, callback) {
    console.log(`Stickara: Saving both versions of note ${noteIndex}`);

    // Ensure the data is properly marked
    if (globalNoteData) {
        globalNoteData.isGlobal = true;
    }

    if (urlNoteData) {
        urlNoteData.isGlobal = false;
    }

    // Create the storage keys
    const globalNoteKey = `${STORAGE_KEY_PREFIX}global_note${noteIndex}`;

    // Generate the URL-specific key using raw URL like highlights do
    const rawUrl = window.stickaraRawCurrentUrl || window.location.href;
    const urlSpecificKey = `${STORAGE_KEY_PREFIX}${rawUrl}_note${noteIndex}`;

    // Prepare the data to save
    const dataToSave = {};

    if (globalNoteData) {
        dataToSave[globalNoteKey] = globalNoteData;
    }

    if (urlNoteData) {
        dataToSave[urlSpecificKey] = urlNoteData;
    }

    // Save both notes at once
    chrome.storage.local.set(dataToSave, () => {
        if (chrome.runtime.lastError) {
            console.error(`Stickara: Error saving both versions of note ${noteIndex}:`, chrome.runtime.lastError.message);
            if (callback) callback(false);
        } else {
            console.log(`Stickara: Successfully saved both versions of note ${noteIndex}`);
            if (callback) callback(true);
        }
    });
}

/**
 * Reloads a specific URL-specific note from storage.
 * Used when toggling from global to URL-specific mode.
 * @param {number} noteIndex - The index of the note to reload
 * @param {Function} callback - Optional callback to run after reloading
 */
function reloadUrlSpecificNote(noteIndex, callback) {
    console.log(`Stickara: Reloading URL-specific note ${noteIndex}`);

    // Generate the key for the URL-specific note using raw URL like highlights do
    const rawUrl = window.stickaraRawCurrentUrl || window.location.href;
    const urlSpecificKey = `${STORAGE_KEY_PREFIX}${rawUrl}_note${noteIndex}`;

    // Load the URL-specific note from storage
    chrome.storage.local.get(urlSpecificKey, (result) => {
        if (chrome.runtime.lastError) {
            console.error(`Stickara: Error loading URL-specific note ${noteIndex}:`, chrome.runtime.lastError.message);
            if (callback) callback(false);
            return;
        }

        const urlSpecificNote = result[urlSpecificKey];

        if (urlSpecificNote) {
            // Update the cache with the URL-specific note
            notes[noteIndex] = { ...createDefaultNoteData(), ...urlSpecificNote };
            notes[noteIndex].isGlobal = false; // Ensure it's marked as non-global

            // If this is the current note, apply it to the UI
            if (noteIndex === currentNoteIndex) {
                applyNoteDataToUI(notes[noteIndex]);
            }

            console.log(`Stickara: Successfully reloaded URL-specific note ${noteIndex}`);
            if (callback) callback(true);
        } else {
            console.log(`Stickara: No URL-specific note ${noteIndex} found`);

            // If no URL-specific note exists, create a default one
            notes[noteIndex] = createDefaultNoteData();
            notes[noteIndex].isGlobal = false;

            // If this is the current note, apply it to the UI
            if (noteIndex === currentNoteIndex) {
                applyNoteDataToUI(notes[noteIndex]);
            }

            if (callback) callback(false);
        }
    });
}

/**
 * Loads the data for the currently selected note (currentNoteIndex) into the UI.
 * Handles missing data, sanitizes loaded data, and applies it to the UI.
 */
function loadCurrentNote(retryCount = 0) {
    const maxRetries = 10; // Prevent infinite retry loops

    // Check prerequisites - only check for essential elements
    if (!noteContainer || !noteText || !notes || !noteHeader || !tagsInput || !reminderInput || !timestampSpan || !noteTitleInput) {
        if (retryCount < maxRetries) {
            console.warn(`Stickara: Cannot load note, UI elements/cache not ready. Retrying... (${retryCount + 1}/${maxRetries})`);
            setTimeout(() => loadCurrentNote(retryCount + 1), 250); // Increased delay
            return;
        } else {
            console.error("Stickara: Maximum retries reached for loadCurrentNote. Some UI elements may be missing.");
            // Continue anyway to prevent complete failure
        }
    }

    // noteSwitcher is optional - it might not exist if createDropdown function is missing
    if (!noteSwitcher) {
        console.warn("Stickara: noteSwitcher not available, but continuing with note load");
    }

    const noteDataFromCache = notes[currentNoteIndex];

    if (!noteDataFromCache) {
        console.log(`Stickara: No data found in cache for note ${currentNoteIndex}, applying defaults.`);
        notes[currentNoteIndex] = createDefaultNoteData(); // Ensure default exists in cache
        applyNoteDataToUI(notes[currentNoteIndex]);
    } else {
        // --- Sanitize and Apply Data from Cache ---
        const sanitizedData = { ...createDefaultNoteData(), ...noteDataFromCache }; // Ensure all fields exist

        // Type checking / validation
        sanitizedData.title = typeof sanitizedData.title === 'string' ? sanitizedData.title : null;
        sanitizedData.notebookId = typeof sanitizedData.notebookId === 'string' ? sanitizedData.notebookId : null;
        sanitizedData.calendarEventId = typeof sanitizedData.calendarEventId === 'string' ? sanitizedData.calendarEventId : null;
        sanitizedData.globallyPinned = typeof sanitizedData.globallyPinned === 'boolean' ? sanitizedData.globallyPinned : false;
        sanitizedData.pinned = typeof sanitizedData.pinned === 'boolean' ? sanitizedData.pinned : false;
        sanitizedData.minimized = typeof sanitizedData.minimized === 'boolean' ? sanitizedData.minimized : false;
        sanitizedData.isGlobal = typeof sanitizedData.isGlobal === 'boolean' ? sanitizedData.isGlobal : false;
        sanitizedData.tags = Array.isArray(sanitizedData.tags) ? sanitizedData.tags.map(String) : []; // Ensure tags are strings
        sanitizedData.color = typeof sanitizedData.color === 'string' ? sanitizedData.color : DEFAULT_COLOR;
        sanitizedData.opacity = typeof sanitizedData.opacity === 'number' ? sanitizedData.opacity : DEFAULT_OPACITY;
        sanitizedData.textSize = typeof sanitizedData.textSize === 'string' ? sanitizedData.textSize : (DEFAULT_FONT_SIZE + 'px');
        sanitizedData.textShadow = typeof sanitizedData.textShadow === 'boolean' ? sanitizedData.textShadow : false;
        // We no longer use global fontFamily
        sanitizedData.position = typeof sanitizedData.position === 'object' ? sanitizedData.position : createDefaultNoteData().position;
        sanitizedData.size = typeof sanitizedData.size === 'object' ? sanitizedData.size : createDefaultNoteData().size;
        sanitizedData.associatedVideoUrl = sanitizedData.associatedVideoUrl || null;
        sanitizedData.associatedVideoTitle = sanitizedData.associatedVideoTitle || null;
        sanitizedData.reminder = Number.isFinite(sanitizedData.reminder) ? sanitizedData.reminder : null;
        sanitizedData.lastSaved = Number.isFinite(sanitizedData.lastSaved) ? sanitizedData.lastSaved : null;
        sanitizedData.calendarEventId = sanitizedData.calendarEventId || null; // <<< Load calendar ID


        // Ensure the cache holds the sanitized version
        notes[currentNoteIndex] = sanitizedData;
        applyNoteDataToUI(sanitizedData); // Apply loaded/sanitized data
    }

    // Update UI elements related to the *current note context*
    const switcherButton = noteSwitcher?.querySelector('.Stickara-dropdown-button');
    if (switcherButton) {
        const iconHtml = switcherButton.querySelector('.Stickara-icon')?.outerHTML || '';
        const displayTitle = notes[currentNoteIndex]?.title || `Note ${currentNoteIndex}`;
        switcherButton.innerHTML = iconHtml + displayTitle;
    }

    if(typeof calculateTextAreaHeight === 'function') calculateTextAreaHeight();
    if(typeof handleFormattingState === 'function') handleFormattingState();
    if(typeof resetFlashcardState === 'function') resetFlashcardState(); // Reset flashcards on note switch
    lastTimestampedVideoSrc = notes[currentNoteIndex]?.associatedVideoUrl || null; // Restore last video URL

    // Check for Pro/Con snippets when the note is loaded
    if (typeof checkForProConSnippets === 'function') {
        setTimeout(checkForProConSnippets, 300); // Small delay to ensure the DOM is updated
    }
}

/**
 * Applies a given note data object to the corresponding UI elements.
 */
function applyNoteDataToUI(data) {
    if (!noteContainer || !noteText || !noteHeader || !tagsInput || !reminderInput || !timestampSpan || !noteTitleInput) {
        console.error("Stickara applyNoteDataToUI: Critical UI elements missing.");
        return;
    }
    if (!data) { // Guard against null/undefined data
         console.error("Stickara applyNoteDataToUI: Invalid or null data provided.");
         data = createDefaultNoteData(); // Apply defaults if bad data is passed
    }

    // Apply Title - Sanitize the title
    noteTitleInput.value = typeof sanitizeText === 'function' ?
        sanitizeText(data.title || '') :
        (data.title || '');
    noteTitleInput.placeholder = `Note ${currentNoteIndex}`; // Dynamic placeholder

    // Apply note text content with proper sanitization
    if (data.text) {
        // Use our sanitization manager if available
        if (window.StickaraSanitizationManager && typeof window.StickaraSanitizationManager.sanitize === 'function') {
            // Sanitize the HTML content before rendering
            const sanitizedHTML = window.StickaraSanitizationManager.sanitize(data.text);
            noteText.innerHTML = sanitizedHTML;
        }
        // Fallback to DOMPurify if available
        else if (window.DOMPurify) {
            noteText.innerHTML = window.DOMPurify.sanitize(data.text);
        }
        // Fallback to StickaraDOMPurify if available
        else if (window.StickaraDOMPurify) {
            noteText.innerHTML = window.StickaraDOMPurify.sanitize(data.text);
        }
        // Fallback to createSafeHTML if available
        else if (typeof createSafeHTML === 'function') {
            // Use our security utility to safely render HTML
            const safeFragment = createSafeHTML(data.text);
            // Clear existing content
            noteText.innerHTML = '';
            // Append the sanitized content
            noteText.appendChild(safeFragment);
        } else {
            // Fallback if security utils aren't available
            console.warn("Stickara: No HTML sanitization available - security risk!");
            noteText.innerHTML = data.text;
        }
    } else {
        noteText.innerHTML = '';
    }

    // State classes and basic styles
    const isPinned = data.pinned || false; noteContainer.classList.toggle('pinned', isPinned);
    const isGloballyPinned = data.globallyPinned || false; noteContainer.classList.toggle('globally-pinned', isGloballyPinned);
    // Minimize functionality removed

    // Explicitly handle global note status
    const isGlobal = data.isGlobal || false;
    noteContainer.classList.toggle('global-note', isGlobal);
    console.log(`Stickara: Applied note data with isGlobal=${isGlobal} to UI`);

    // Update notebook sidebar selection
    if (typeof updateCurrentNotebookSelection === 'function') {
        updateCurrentNotebookSelection();
    }

    // Dispatch custom event for notebook sidebar
    if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('stickaraNoteChanged', {
            detail: { noteIndex: currentNoteIndex, noteData: data }
        }));
    }

    // Update URL note and global note button states
    const urlNoteBtn = document.getElementById('Stickara-url-note-btn');
    const globalNoteBtn = document.getElementById('Stickara-global-note-btn');

    if (urlNoteBtn) {
        urlNoteBtn.classList.toggle('active', !isGlobal);
        urlNoteBtn.title = !isGlobal ? 'URL-specific note active' : 'Show URL-specific note (only for this page)';
        urlNoteBtn.setAttribute('aria-label', !isGlobal ? 'URL-specific note active' : 'Show URL-specific note (only for this page)');
    }

    if (globalNoteBtn) {
        globalNoteBtn.classList.toggle('active', isGlobal);
        globalNoteBtn.title = isGlobal ? 'Global note active' : 'Show global note (works across all URLs)';
        globalNoteBtn.setAttribute('aria-label', isGlobal ? 'Global note active' : 'Show global note (works across all URLs)');
    }

    noteContainer.style.position = isPinned ? 'fixed' : 'absolute';
    const color = data.color || DEFAULT_COLOR;
    noteContainer.className = noteContainer.className.replace(/theme-\w+/g, '').trim();
    noteContainer.classList.add(`theme-${color}`);
    const opacity = data.opacity || DEFAULT_OPACITY; noteContainer.style.opacity = String(opacity);

    // Apply global font size if available, otherwise use textSize or default
    const globalFontSize = data.globalFontSize || data.preferredFontSize || DEFAULT_FONT_SIZE;
    noteText.style.fontSize = globalFontSize + 'px';
    noteText.style.setProperty('--Stickara-default-font-size', globalFontSize + 'px');

    // Update the global font size slider if it exists
    updateGlobalFontSizeSlider(globalFontSize);

    // We no longer set global fontFamily

    // Positioning (Carefully apply top/left based on pinned state)
    const defaultPosition = createDefaultNoteData().position;
    let top = data.position?.top || defaultPosition.top;
    let left = data.position?.left || defaultPosition.left;
    let right = data.position?.right || defaultPosition.right;
    let topPx = parseFloat(top);
    let leftPx = parseFloat(left);

    if (!isNaN(topPx)) { // Apply top position relative to document/viewport
         noteContainer.style.top = (isPinned ? topPx : (topPx + window.scrollY)) + 'px';
         noteContainer.style.bottom = 'auto';
    } else { // Fallback if top is invalid
         const fallbackTop = 100;
         noteContainer.style.top = (isPinned ? fallbackTop : (fallbackTop + window.scrollY)) + 'px';
         noteContainer.style.bottom = 'auto';
    }
     if (!isNaN(leftPx)) { // Apply left position relative to document/viewport
         noteContainer.style.left = (isPinned ? leftPx : (leftPx + window.scrollX)) + 'px';
         noteContainer.style.right = 'auto';
     } else if (right && right !== 'auto') { // Use right if specified
         noteContainer.style.left = 'auto';
         noteContainer.style.right = right;
     } else { // Default to right offset if left is unavailable/invalid
         noteContainer.style.left = 'auto';
         noteContainer.style.right = defaultPosition.right || '24px';
     }


    // Size and Minimized State Handling
     const defaultSize = createDefaultNoteData().size;
     const storedWidth = data.size?.width || defaultSize.width;
     const storedHeight = data.size?.height || defaultSize.height;

    // Minimize functionality removed
    noteContainer.style.width = storedWidth;
    noteContainer.style.height = storedHeight;
    noteContainer.style.minHeight = ''; // Restore CSS min-height

    // Apply text shadow state
    const textShadowEnabled = data.textShadow || false;
    if (textShadowEnabled) {
        noteText.classList.add('Stickara-text-shadow-enabled');
    } else {
        noteText.classList.remove('Stickara-text-shadow-enabled');
    }

    // Update control states (safe checks for function existence)
    if (typeof updateActiveSwatch === 'function') updateActiveSwatch(color); else console.warn("updateActiveSwatch missing");
    if (typeof updateActiveOpacity === 'function') updateActiveOpacity(opacity); else console.warn("updateActiveOpacity missing");
    if (typeof updateActiveTextShadow === 'function') updateActiveTextShadow(textShadowEnabled); else console.warn("updateActiveTextShadow missing");
    if (typeof updatePinButtonState === 'function') updatePinButtonState(isPinned); else console.warn("updatePinButtonState missing");
    if (typeof updateGlobalPinButtonState === 'function') updateGlobalPinButtonState(isGloballyPinned); else console.warn("updateGlobalPinButtonState missing");
    // Minimize functionality removed
    if (typeof updateGlobalNoteButtonState === 'function') updateGlobalNoteButtonState(isGlobal); else console.warn("updateGlobalNoteButtonState missing");

    // Last Saved Timestamp
    if (timestampSpan) timestampSpan.innerText = data.lastSaved ? `Saved: ${formatRelativeTime(data.lastSaved)}` : '';

    // Tags and Reminder Input Fields
    if (tagsInput) tagsInput.value = data.tags ? data.tags.join(', ') : '';
    if (reminderInput) {
        try {
             if (data.reminder) {
                 const reminderDate = new Date(data.reminder);
                 if (!isNaN(reminderDate.getTime())) {
                     const pad = (num) => String(num).padStart(2, '0');
                     const year = reminderDate.getFullYear();
                     const month = pad(reminderDate.getMonth() + 1);
                     const day = pad(reminderDate.getDate());
                     const hours = pad(reminderDate.getHours());
                     const minutes = pad(reminderDate.getMinutes());
                     reminderInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
                 } else { reminderInput.value = ''; }
             } else { reminderInput.value = ''; }
         } catch (e) { reminderInput.value = ''; console.warn("Error formatting reminder date:", data.reminder); }
     }

    // Calendar Checkbox - Check if a calendar event ID exists
    const calendarCheckbox = document.getElementById('Stickara-calendar-checkbox');
    if (calendarCheckbox) {
        // Check the box if an event ID exists *and* there's a reminder set
        // (The ID might linger even if the reminder time is cleared)
        calendarCheckbox.checked = !!(data.calendarEventId && data.reminder);
        // Disable checkbox if no reminder time is set
        calendarCheckbox.disabled = !data.reminder;
    }

    // Update lastTimestampedVideoSrc state variable (for timestamp feature)
     lastTimestampedVideoSrc = data.associatedVideoUrl || null;
     // NOTE: Notebook ID (data.notebookId) is loaded into cache but not directly applied to content UI


}


/**
 * Creates a default note with the user's preferred scope
 * @param {number} noteIndex - The note index to create default for
 * @returns {Object} Default note data with correct scope
 */
function createDefaultNoteWithPreference(noteIndex) {
    const preferGlobal = localStorage.getItem(`Stickara_prefer_global_${noteIndex}`) === 'true';
    const defaultNote = createDefaultNoteData();
    defaultNote.isGlobal = preferGlobal;
    return defaultNote;
}

/**
 * Loads the overall state and note data, setting isFullyInitialized on success.
 */
window.loadState = function() {
    // console.log("[loadState] Starting..."); // Less noisy
    if (!noteContainer) {
        // console.warn("Stickara: loadState - noteContainer missing. Delaying..."); // Less noisy
        setTimeout(loadState, 150); // Shortened delay
        return;
    }

    isFullyInitialized = false; // Reset flag

    // Use standard Chrome storage directly (bypassing secure storage due to persistence issues)
    chrome.storage.local.get([stateKey], result => {
        let stateError = chrome.runtime.lastError;
        if (stateError) {
            console.error("SB Error loading state key:", stateKey, stateError.message);
            currentNoteIndex = 1; // Reset state
            notes = {}; // Clear notes cache
            notes[1] = createDefaultNoteWithPreference(1); // Ensure note 1 exists with user preference
            if (typeof loadCurrentNote === 'function') loadCurrentNote(); else console.error("loadCurrentNote missing");
            if (typeof updateNoteSwitcher === 'function') updateNoteSwitcher(); else console.error("updateNoteSwitcher missing");
            if (typeof hideNote === 'function') hideNote(); else console.error("hideNote missing");
            isFullyInitialized = true;
            console.warn("[loadState] Finished after state load error, forced init=true");
            return;
        }
        handleStateLoaded(result);
    });

    /**
     * Handles the loaded state data
     * @param {Object} result - The result from storage
     */
    function handleStateLoaded(result) {
        const state = result[stateKey] || { currentNoteIndex: 1, isVisible: false };
        currentNoteIndex = (state.currentNoteIndex >= 1 && state.currentNoteIndex <= 10) ? state.currentNoteIndex : 1;

        // Generate keys for both URL-specific notes and global notes
        let urlSpecificKeys = [];

        // Generate URL-specific keys using raw URL like highlights do
        const rawUrl = window.stickaraRawCurrentUrl || window.location.href;
        urlSpecificKeys = Array.from({ length: 10 }, (_, i) => `${STORAGE_KEY_PREFIX}${rawUrl}_note${i + 1}`);

        // Generate global note keys
        const globalNoteKeys = Array.from({ length: 10 }, (_, i) => `${STORAGE_KEY_PREFIX}global_note${i + 1}`);

        // Combine both sets of keys
        const noteKeysToLoad = [...urlSpecificKeys, ...globalNoteKeys];

        // Log the keys we're loading for debugging
        console.log("Stickara: Current URL:", rawUrl);
        console.log("Stickara: Loading notes with URL-specific keys:", urlSpecificKeys);
        console.log("Stickara: Loading notes with global keys:", globalNoteKeys);

        // Use standard Chrome storage directly (bypassing secure storage due to persistence issues)
        chrome.storage.local.get(noteKeysToLoad, notesResult => {
            let notesError = chrome.runtime.lastError;
            if (notesError) {
                console.error("SB Error loading note keys locally:", notesError.message);
                currentNoteIndex = 1; // Reset index on error
                notes = {}; // Clear cache
                notes[1] = createDefaultNoteWithPreference(1); // Ensure default for note 1 with user preference
                if (typeof loadCurrentNote === 'function') loadCurrentNote();
                if (typeof updateNoteSwitcher === 'function') updateNoteSwitcher();
                if (typeof hideNote === 'function') hideNote();
                isFullyInitialized = true;
                console.warn("[loadState] Finished after note load error, forced init=true");
                return;
            }
            processLoadedNotes(notesResult);
        });

        /**
         * Processes loaded notes data
         * @param {Object} notesResult - The loaded notes data
         */
        function processLoadedNotes(notesResult) {
            notes = {}; // Clear cache before population

            // First, collect all notes (both URL-specific and global)
            const urlNotes = {};
            const globalNotes = {};

            // Process URL-specific notes
            urlSpecificKeys.forEach((key, i) => {
                const noteIndex = i + 1;
                if (notesResult[key]) {
                    console.log(`Stickara: Found URL-specific note ${noteIndex} with key: ${key}`);
                    urlNotes[noteIndex] = { ...createDefaultNoteData(), ...notesResult[key] };
                } else {
                    console.log(`Stickara: No URL-specific note found for key: ${key}`);
                }
            });

            // Process global notes
            globalNoteKeys.forEach((key, i) => {
                const noteIndex = i + 1;
                if (notesResult[key]) {
                    console.log(`Stickara: Found global note ${noteIndex} with key: ${key}`);
                    globalNotes[noteIndex] = { ...createDefaultNoteData(), ...notesResult[key] };
                } else {
                    console.log(`Stickara: No global note found for key: ${key}`);
                }
            });

            // Now decide which notes to use based on isGlobal property
            for (let noteIndex = 1; noteIndex <= 10; noteIndex++) {
                const urlNote = urlNotes[noteIndex];
                const globalNote = globalNotes[noteIndex];

                // Check if we have a preference for global or URL-specific notes
                const storedPref = localStorage.getItem(`Stickara_prefer_global_${noteIndex}`);
                const preferGlobal = storedPref === 'true';

                if (urlNote && globalNote) {
                    // Both exist - use user preference to decide which one to load
                    if (preferGlobal) {
                        // User prefers global notes
                        notes[noteIndex] = globalNote;
                    } else {
                        // User prefers URL-specific notes (or no preference set, default to URL-specific)
                        notes[noteIndex] = urlNote;
                    }
                } else if (urlNote) {
                    // Only URL note exists - check if user prefers global
                    if (preferGlobal) {
                        // User prefers global but only URL note exists - create a default global note
                        notes[noteIndex] = createDefaultNoteWithPreference(noteIndex);
                    } else {
                        // User prefers URL-specific or no preference - use existing URL note
                        notes[noteIndex] = urlNote;
                    }
                } else if (globalNote) {
                    // Only global note exists - check if user prefers URL-specific
                    if (preferGlobal) {
                        // User prefers global - use existing global note
                        notes[noteIndex] = globalNote;
                    } else {
                        // User prefers URL-specific but only global note exists - create a default URL-specific note
                        notes[noteIndex] = createDefaultNoteWithPreference(noteIndex);
                    }
                }

                // Ensure the isGlobal property is correctly set based on the source
                if (notes[noteIndex]) {
                    // If we loaded from globalNotes, ensure isGlobal is true
                    // If we loaded from urlNotes, ensure isGlobal is false
                    if (notes[noteIndex] === globalNote && notes[noteIndex].isGlobal !== true) {
                        notes[noteIndex].isGlobal = true;
                    } else if (notes[noteIndex] === urlNote && notes[noteIndex].isGlobal !== false) {
                        notes[noteIndex].isGlobal = false;
                    }

                    // Don't automatically save preference here - only save when user explicitly chooses a scope
                    // The preference should only be updated by saveNoteScopePreference() when user manually switches
                }
            }
            // console.log("[loadState] Populated notes cache:", notes); // Less noisy

            // Ensure data exists in cache for the currentNoteIndex *before* loading
            if (!notes[currentNoteIndex]) {
                notes[currentNoteIndex] = createDefaultNoteWithPreference(currentNoteIndex);
            }

            // Update UI components (after notes cache is populated)
            if (typeof updateNoteSwitcher === 'function') updateNoteSwitcher(); else console.error("updateNoteSwitcher missing");
            if (typeof loadCurrentNote === 'function') loadCurrentNote(); else console.error("loadCurrentNote missing");

            // Set visibility LAST
            if (state.isVisible) {
                if (typeof showNote === 'function') showNote(); else console.error("showNote missing");
            } else {
                if (typeof hideNote === 'function') hideNote(); else console.error("hideNote missing");
            }

            isFullyInitialized = true; // --- SET INITIALIZATION FLAG ---
            // console.log("[loadState] Finished successfully, isFullyInitialized = true"); // Less noisy


        }
    }
}

/**
 * Saves the overall UI state (current note index, visibility) to chrome.storage.local.
 */
function saveState() {
    if (!noteContainer) return; // Guard
    if (!isFullyInitialized) return; // Don't save before loading is complete
    const state = {
        currentNoteIndex: currentNoteIndex,
        isVisible: noteContainer.classList?.contains('visible') || false
    };

    // Use secure storage utility if available
    if (window.StickaraSecureStorage && typeof window.StickaraSecureStorage.set === 'function') {
        // Use our secure storage utility
        window.StickaraSecureStorage.set(stateKey, state, (error) => {
            if (error) {
                console.error("SB Error saving state locally:", stateKey, error.message);
            }
            // else { console.log("[saveState] State saved."); } // Less noisy
        });
    } else {
        // Fall back to standard storage
        chrome.storage.local.set({ [stateKey]: state }, () => {
            if (chrome.runtime.lastError) {
                console.error("SB Error saving state locally:", stateKey, chrome.runtime.lastError.message);
            }
            // else { console.log("[saveState] State saved."); } // Less noisy
        });
    }
}

/**
 * Reloads notes for a new URL context (used when URL changes in SPA navigation)
 * This function loads the appropriate notes for the new URL and updates the UI
 */
function reloadNotesForNewURL() {
    console.log(`Stickara: Reloading notes for new URL context: ${window.location.href}`);

    // Only proceed if we're dealing with URL-specific notes
    if (!notes || !notes[currentNoteIndex] || notes[currentNoteIndex].isGlobal) {
        console.log("Stickara: Current note is global, no URL context reload needed");
        return;
    }

    // Check if this is a SPA site where URL context switching makes sense
    // Use the same comprehensive list as in content-state.js
    const spaSites = [
        // AI & Chat Platforms
        'chatgpt.com', 'chat.openai.com', 'claude.ai', 'bard.google.com', 'bing.com', 'copilot.microsoft.com',
        'character.ai', 'poe.com', 'perplexity.ai', 'you.com', 'phind.com', 'writesonic.com', 'jasper.ai',
        'copy.ai', 'rytr.me', 'anyword.com', 'shortly.ai', 'peppertype.ai', 'contentbot.ai', 'wordtune.com',

        // Social Media & Communication
        'twitter.com', 'x.com', 'facebook.com', 'instagram.com', 'linkedin.com', 'tiktok.com', 'snapchat.com',
        'pinterest.com', 'tumblr.com', 'reddit.com', 'discord.com', 'slack.com', 'telegram.org', 'whatsapp.com',
        'messenger.com', 'skype.com', 'zoom.us', 'teams.microsoft.com', 'meet.google.com', 'webex.com',
        'gotomeeting.com', 'bluejeans.com', 'whereby.com', 'jitsi.meet', 'bigbluebutton.org', 'gather.town',

        // Video & Streaming
        'youtube.com', 'vimeo.com', 'dailymotion.com', 'twitch.tv', 'netflix.com', 'hulu.com', 'disneyplus.com',
        'primevideo.com', 'hbomax.com', 'paramount.com', 'peacocktv.com', 'crunchyroll.com', 'funimation.com',
        'spotify.com', 'soundcloud.com', 'pandora.com', 'deezer.com', 'tidal.com', 'apple.com', 'music.amazon.com',

        // Development & Code
        'github.com', 'gitlab.com', 'bitbucket.org', 'codepen.io', 'jsfiddle.net', 'codesandbox.io', 'replit.com',
        'stackblitz.com', 'glitch.com', 'vercel.com', 'netlify.com', 'heroku.com', 'railway.app', 'render.com',
        'digitalocean.com', 'aws.amazon.com', 'console.cloud.google.com', 'portal.azure.com', 'firebase.google.com',
        'supabase.com', 'planetscale.com', 'neon.tech', 'upstash.com', 'mongodb.com', 'redis.com',

        // Design & Creative
        'figma.com', 'canva.com', 'sketch.com', 'adobe.com', 'invisionapp.com', 'miro.com', 'mural.co',
        'whimsical.com', 'lucidchart.com', 'draw.io', 'creately.com', 'conceptboard.com', 'milanote.com',
        'behance.net', 'dribbble.com', '99designs.com', 'awwwards.com', 'designspiration.com',

        // Productivity & Collaboration
        'notion.so', 'obsidian.md', 'roamresearch.com', 'logseq.com', 'craft.do', 'bear.app', 'ulysses.app',
        'typora.io', 'marktext.app', 'zettlr.com', 'joplin.app', 'standardnotes.org', 'simplenote.com',
        'evernote.com', 'onenote.com', 'googledocs.com', 'docs.google.com', 'office.com', 'onedrive.com',
        'dropbox.com', 'box.com', 'icloud.com', 'mega.nz', 'pcloud.com', 'sync.com',

        // Project Management
        'trello.com', 'asana.com', 'monday.com', 'clickup.com', 'airtable.com', 'basecamp.com', 'wrike.com',
        'smartsheet.com', 'teamwork.com', 'workfront.com', 'clarizen.com', 'zoho.com', 'freshworks.com',
        'hubspot.com', 'salesforce.com', 'pipedrive.com', 'crm.dynamics.com', 'zendesk.com', 'intercom.com',

        // E-commerce & Shopping
        'amazon.com', 'ebay.com', 'etsy.com', 'shopify.com', 'woocommerce.com', 'bigcommerce.com',
        'squarespace.com', 'wix.com', 'weebly.com', 'wordpress.com', 'webflow.com', 'framer.com',
        'bubble.io', 'zapier.com', 'ifttt.com', 'integromat.com', 'automate.io', 'microsoft.com',

        // Learning & Education
        'coursera.org', 'udemy.com', 'edx.org', 'khanacademy.org', 'codecademy.com', 'freecodecamp.org',
        'pluralsight.com', 'lynda.com', 'skillshare.com', 'masterclass.com', 'brilliant.org', 'duolingo.com',
        'babbel.com', 'rosettastone.com', 'memrise.com', 'anki.com', 'quizlet.com', 'kahoot.com',

        // News & Media
        'medium.com', 'substack.com', 'ghost.org', 'hashnode.com', 'dev.to', 'hackernews.com', 'producthunt.com',
        'indiehackers.com', 'betalist.com', 'angellist.com', 'crunchbase.com', 'techcrunch.com', 'theverge.com',
        'wired.com', 'arstechnica.com', 'engadget.com', 'gizmodo.com', 'mashable.com', 'buzzfeed.com',

        // Finance & Trading
        'robinhood.com', 'webull.com', 'etrade.com', 'schwab.com', 'fidelity.com', 'vanguard.com',
        'coinbase.com', 'binance.com', 'kraken.com', 'gemini.com', 'crypto.com', 'blockchain.com',
        'tradingview.com', 'investing.com', 'yahoo.com', 'marketwatch.com', 'bloomberg.com', 'reuters.com',

        // Travel & Maps
        'google.com', 'maps.google.com', 'earth.google.com', 'openstreetmap.org', 'mapbox.com', 'here.com',
        'booking.com', 'expedia.com', 'airbnb.com', 'vrbo.com', 'tripadvisor.com', 'kayak.com', 'skyscanner.com',
        'uber.com', 'lyft.com', 'doordash.com', 'grubhub.com', 'ubereats.com', 'postmates.com',

        // Gaming
        'steam.com', 'epicgames.com', 'origin.com', 'uplay.com', 'battle.net', 'roblox.com', 'minecraft.net',
        'fortnite.com', 'leagueoflegends.com', 'valorant.com', 'overwatch.com', 'hearthstone.com',
        'chess.com', 'lichess.org', 'poker.com', 'zynga.com', 'king.com', 'supercell.com',

        // Health & Fitness
        'myfitnesspal.com', 'fitbit.com', 'strava.com', 'garmin.com', 'nike.com', 'adidas.com',
        'peloton.com', 'headspace.com', 'calm.com', 'meditation.com', 'insight.com', 'ten.com',

        // Real Estate
        'zillow.com', 'realtor.com', 'redfin.com', 'trulia.com', 'apartments.com', 'rent.com',
        'airbnb.com', 'vrbo.com', 'homeaway.com', 'booking.com', 'hotels.com', 'expedia.com',

        // Job Search
        'linkedin.com', 'indeed.com', 'glassdoor.com', 'monster.com', 'careerbuilder.com', 'ziprecruiter.com',
        'dice.com', 'stackoverflow.com', 'angel.co', 'wellfound.com', 'hired.com', 'toptal.com',

        // Cloud Storage & File Sharing
        'drive.google.com', 'onedrive.com', 'dropbox.com', 'box.com', 'icloud.com', 'mega.nz',
        'pcloud.com', 'sync.com', 'tresorit.com', 'spideroak.com', 'backblaze.com', 'carbonite.com',

        // Analytics & Marketing
        'analytics.google.com', 'facebook.com', 'ads.google.com', 'mailchimp.com', 'constantcontact.com',
        'sendinblue.com', 'convertkit.com', 'aweber.com', 'getresponse.com', 'activecampaign.com',

        // CMS & Website Builders
        'wordpress.com', 'wix.com', 'squarespace.com', 'weebly.com', 'webflow.com', 'framer.com',
        'bubble.io', 'carrd.co', 'linktree.com', 'bio.link', 'linktr.ee', 'beacons.ai',

        // API & Development Tools
        'postman.com', 'insomnia.rest', 'swagger.io', 'rapidapi.com', 'apiary.io', 'mockapi.io',
        'jsonplaceholder.typicode.com', 'httpbin.org', 'reqres.in', 'jsonapi.org', 'graphql.org',

        // Security & VPN
        'nordvpn.com', 'expressvpn.com', 'surfshark.com', 'cyberghost.com', 'pia.com', 'protonvpn.com',
        'lastpass.com', '1password.com', 'bitwarden.com', 'dashlane.com', 'keeper.com', 'roboform.com',

        // Monitoring & DevOps
        'datadog.com', 'newrelic.com', 'splunk.com', 'elastic.co', 'grafana.com', 'prometheus.io',
        'sentry.io', 'rollbar.com', 'bugsnag.com', 'honeybadger.io', 'airbrake.io', 'raygun.com',

        // Communication & Email
        'gmail.com', 'outlook.com', 'yahoo.com', 'protonmail.com', 'tutanota.com', 'fastmail.com',
        'zoho.com', 'yandex.com', 'aol.com', 'icloud.com', 'mail.com', 'gmx.com',

        // Forums & Communities
        'stackoverflow.com', 'stackexchange.com', 'quora.com', 'reddit.com', 'discourse.org', 'flarum.org',
        'phpbb.com', 'vbulletin.com', 'invisioncommunity.com', 'xenforo.com', 'vanilla.com', 'nodebb.org',

        // Documentation & Wikis
        'wikipedia.org', 'fandom.com', 'wikia.com', 'confluence.atlassian.com', 'gitbook.com', 'bookstack.org',
        'dokuwiki.org', 'tiddlywiki.com', 'zim-wiki.org', 'trac.edgewall.org', 'redmine.org', 'mantisbt.org',

        // Testing & QA
        'browserstack.com', 'saucelabs.com', 'crossbrowsertesting.com', 'lambdatest.com', 'testingbot.com',
        'selenium.dev', 'cypress.io', 'playwright.dev', 'puppeteer.dev', 'webdriver.io', 'testcafe.io',

        // Database & Backend
        'mongodb.com', 'postgresql.org', 'mysql.com', 'redis.com', 'elasticsearch.co', 'neo4j.com',
        'cassandra.apache.org', 'couchdb.apache.org', 'rethinkdb.com', 'orientdb.org', 'arangodb.com',

        // Hosting & Infrastructure
        'aws.amazon.com', 'console.cloud.google.com', 'portal.azure.com', 'digitalocean.com', 'linode.com',
        'vultr.com', 'hetzner.com', 'ovh.com', 'scaleway.com', 'upcloud.com', 'cloudflare.com',

        // Content Management
        'contentful.com', 'strapi.io', 'sanity.io', 'forestry.io', 'netlify.com', 'gatsby.com',
        'nextjs.org', 'nuxtjs.org', 'svelte.dev', 'vue.js.org', 'react.dev', 'angular.io',

        // E-learning Platforms
        'canvas.instructure.com', 'blackboard.com', 'moodle.org', 'schoology.com', 'edmodo.com', 'classlink.com',
        'google.com', 'classroom.google.com', 'teams.microsoft.com', 'zoom.us', 'webex.com', 'gotomeeting.com',

        // Cryptocurrency & Blockchain
        'etherscan.io', 'bscscan.com', 'polygonscan.com', 'ftmscan.com', 'snowtrace.io', 'arbiscan.io',
        'optimistic.etherscan.io', 'explorer.solana.com', 'cardanoscan.io', 'tzstats.com', 'mintscan.io',

        // IoT & Smart Home
        'home.nest.com', 'alexa.amazon.com', 'smartthings.samsung.com', 'ifttt.com', 'home-assistant.io',
        'hubitat.com', 'wink.com', 'vera.com', 'homeseer.com', 'insteon.com', 'lutron.com',

        // Weather & Environment
        'weather.com', 'accuweather.com', 'weather.gov', 'windy.com', 'weatherunderground.com', 'darksky.net',
        'openweathermap.org', 'climacell.co', 'tomorrow.io', 'weatherapi.com', 'visualcrossing.com',

        // Sports & Entertainment
        'espn.com', 'nfl.com', 'nba.com', 'mlb.com', 'nhl.com', 'fifa.com', 'uefa.com', 'premierleague.com',
        'imdb.com', 'rottentomatoes.com', 'metacritic.com', 'letterboxd.com', 'goodreads.com', 'audible.com',

        // Government & Legal
        'irs.gov', 'ssa.gov', 'usps.com', 'dmv.org', 'courts.gov', 'justice.gov', 'fbi.gov', 'cia.gov',
        'whitehouse.gov', 'congress.gov', 'senate.gov', 'house.gov', 'supremecourt.gov', 'treasury.gov',

        // Scientific & Research
        'arxiv.org', 'pubmed.ncbi.nlm.nih.gov', 'scholar.google.com', 'researchgate.net', 'academia.edu',
        'mendeley.com', 'zotero.org', 'endnote.com', 'refworks.com', 'citeulike.org', 'bibsonomy.org',

        // Art & Culture
        'artstation.com', 'deviantart.com', 'flickr.com', '500px.com', 'unsplash.com', 'pexels.com',
        'shutterstock.com', 'gettyimages.com', 'istockphoto.com', 'adobe.com', 'canva.com', 'crello.com',

        // Additional E-commerce & Marketplaces
        'alibaba.com', 'aliexpress.com', 'wish.com', 'overstock.com', 'wayfair.com', 'target.com',
        'walmart.com', 'bestbuy.com', 'homedepot.com', 'lowes.com', 'costco.com', 'samsclub.com',
        'macys.com', 'nordstrom.com', 'zappos.com', 'chewy.com', 'petco.com', 'petsmart.com',
        'newegg.com', 'tigerdirect.com', 'bhphotovideo.com', 'adorama.com', 'sweetwater.com',
        'guitarcenter.com', 'musiciansfriend.com', 'reverb.com', 'ebay.co.uk', 'amazon.co.uk',
        'amazon.de', 'amazon.fr', 'amazon.it', 'amazon.es', 'amazon.ca', 'amazon.au', 'amazon.jp',

        // International Social Media & Communication
        'weibo.com', 'wechat.com', 'qq.com', 'baidu.com', 'yandex.ru', 'vk.com', 'ok.ru',
        'mail.ru', 'rambler.ru', 'naver.com', 'kakao.com', 'line.me', 'viber.com', 'kik.com',
        'snapchat.com', 'tinder.com', 'bumble.com', 'hinge.co', 'okcupid.com', 'match.com',
        'eharmony.com', 'pof.com', 'zoosk.com', 'badoo.com', 'meetup.com', 'eventbrite.com',

        // Streaming & Entertainment Platforms
        'twitch.tv', 'mixer.com', 'dlive.tv', 'trovo.live', 'facebook.com', 'kick.com',
        'caffeine.tv', 'streamlabs.com', 'obs.live', 'restream.io', 'streamyard.com',
        'riverside.fm', 'anchor.fm', 'buzzsprout.com', 'libsyn.com', 'spreaker.com',
        'podbean.com', 'castbox.fm', 'overcast.fm', 'pocketcasts.com', 'stitcher.com',

        // Music & Audio Platforms
        'bandcamp.com', 'mixcloud.com', 'audiomack.com', 'reverbnation.com', 'last.fm',
        'genius.com', 'musixmatch.com', 'shazam.com', 'songkick.com', 'setlist.fm',
        'discogs.com', 'allmusic.com', 'pitchfork.com', 'rollingstone.com', 'billboard.com',
        'stereogum.com', 'consequence.net', 'spin.com', 'nme.com', 'musicradar.com',

        // Video & Live Streaming
        'ustream.tv', 'livestream.com', 'periscope.tv', 'younow.com', 'bigo.tv',
        'liveme.com', 'streamlabs.com', 'nightbot.com', 'moobot.tv', 'fossabot.com',
        'chatbot.com', 'botisimo.com', 'wizebot.tv', 'deepbot.tv', 'ankhbot.com',

        // Gaming Platforms & Communities
        'itch.io', 'gamejolt.com', 'kongregate.com', 'newgrounds.com', 'armor.com',
        'miniclip.com', 'pogo.com', 'bigfishgames.com', 'wildtangent.com', 'shockwave.com',
        'addictinggames.com', 'coolmathgames.com', 'friv.com', 'kizi.com', 'y8.com',
        'gameforge.com', 'nexon.com', 'ncsoft.com', 'perfectworld.com', 'webzen.com',
        'gamigo.com', 'aeriagames.com', 'ijji.com', 'gpotato.com', 'outspark.com',

        // Developer Tools & Platforms
        'stackoverflow.com', 'stackexchange.com', 'serverfault.com', 'superuser.com',
        'askubuntu.com', 'mathoverflow.net', 'codereview.stackexchange.com', 'programmers.stackexchange.com',
        'softwareengineering.stackexchange.com', 'dba.stackexchange.com', 'unix.stackexchange.com',
        'apple.stackexchange.com', 'android.stackexchange.com', 'gaming.stackexchange.com',
        'scifi.stackexchange.com', 'cooking.stackexchange.com', 'diy.stackexchange.com',

        // Code Repositories & Version Control
        'sourceforge.net', 'launchpad.net', 'codeplex.com', 'googlecode.com', 'assembla.com',
        'unfuddle.com', 'beanstalkapp.com', 'codebasehq.com', 'projectlocker.com', 'repositoryhosting.com',
        'xp-dev.com', 'deveo.com', 'kiln.com', 'planio.com', 'rhodecode.com',

        // Cloud Computing & Infrastructure
        'rackspace.com', 'godaddy.com', 'hostgator.com', 'bluehost.com', 'dreamhost.com',
        'siteground.com', 'a2hosting.com', 'inmotion.com', 'hostmonster.com', 'justhost.com',
        'fatcow.com', 'ipage.com', 'greengeeks.com', 'hostpapa.com', 'namecheap.com',
        'hover.com', 'gandi.net', 'enom.com', 'networksolutions.com', 'register.com',

        // Content Delivery & CDN
        'maxcdn.com', 'keycdn.com', 'bunnycdn.com', 'stackpath.com', 'fastly.com',
        'jsdelivr.com', 'unpkg.com', 'cdnjs.com', 'bootstrapcdn.com', 'fontawesome.com',
        'googlefonts.com', 'typekit.com', 'fonts.com', 'myfonts.com', 'fontspring.com',

        // Database & Backend Services
        'fauna.com', 'cockroachdb.com', 'yugabyte.com', 'timescale.com', 'influxdata.com',
        'questdb.io', 'clickhouse.tech', 'apache.org', 'mariadb.org', 'percona.com',
        'enterprisedb.com', 'citusdata.com', 'greenplum.org', 'vertica.com', 'snowflake.com',

        // API & Integration Platforms
        'mulesoft.com', 'apigee.com', 'kong.com', 'tyk.io', 'ambassador.com',
        'getambassador.io', 'istio.io', 'linkerd.io', 'consul.io', 'vault.io',
        'terraform.io', 'packer.io', 'vagrant.com', 'docker.com', 'kubernetes.io',

        // Monitoring & Analytics
        'mixpanel.com', 'amplitude.com', 'segment.com', 'fullstory.com', 'hotjar.com',
        'crazyegg.com', 'optimizely.com', 'vwo.com', 'unbounce.com', 'leadpages.com',
        'clickfunnels.com', 'kartra.com', 'builderall.com', 'systeme.io', 'getresponse.com',

        // Email Marketing & Automation
        'drip.com', 'klaviyo.com', 'omnisend.com', 'campaignmonitor.com', 'emma.com',
        'constantcontact.com', 'icontact.com', 'verticalresponse.com', 'benchmark.com', 'getresponse.com',
        'aweber.com', 'mailerlite.com', 'moosend.com', 'sendy.co', 'phplist.org',

        // Customer Support & Help Desk
        'freshdesk.com', 'helpscout.com', 'kayako.com', 'desk.com', 'uservoice.com',
        'groove.com', 'helpshift.com', 'livechat.com', 'olark.com', 'tawk.to',
        'crisp.chat', 'drift.com', 'intercom.com', 'zendesk.com', 'freshworks.com',

        // Project Management & Collaboration
        'notion.so', 'coda.io', 'airtable.com', 'smartsheet.com', 'monday.com',
        'clickup.com', 'asana.com', 'trello.com', 'basecamp.com', 'wrike.com',
        'teamwork.com', 'workfront.com', 'clarizen.com', 'liquidplanner.com', 'projectplace.com',

        // Design & Prototyping Tools
        'sketch.com', 'figma.com', 'adobe.com', 'canva.com', 'invisionapp.com',
        'marvelapp.com', 'proto.io', 'justinmind.com', 'axure.com', 'balsamiq.com',
        'mockplus.com', 'flinto.com', 'principle.design', 'framer.com', 'origami.design',

        // Website Builders & CMS
        'wordpress.org', 'wordpress.com', 'wix.com', 'squarespace.com', 'weebly.com',
        'webflow.com', 'bubble.io', 'carrd.co', 'strikingly.com', 'jimdo.com',
        'site123.com', 'zyro.com', 'godaddy.com', 'ionos.com', 'hostinger.com',

        // E-learning & Online Education
        'udacity.com', 'coursera.org', 'edx.org', 'futurelearn.com', 'swayam.gov.in',
        'nptel.ac.in', 'mitopencourseware.org', 'ocw.mit.edu', 'stanford.edu', 'harvard.edu',
        'yale.edu', 'princeton.edu', 'columbia.edu', 'upenn.edu', 'dartmouth.edu',

        // Language Learning
        'duolingo.com', 'babbel.com', 'rosettastone.com', 'busuu.com', 'lingoda.com',
        'italki.com', 'preply.com', 'cambly.com', 'verbling.com', 'rype.com',
        'fluentu.com', 'lingvist.com', 'mondly.com', 'memrise.com', 'anki.com',

        // Skill Development & Training
        'skillshare.com', 'masterclass.com', 'linkedin.com', 'pluralsight.com', 'treehouse.com',
        'codecademy.com', 'freecodecamp.org', 'khanacademy.org', 'brilliant.org', 'datacamp.com',
        'coursera.org', 'udemy.com', 'edx.org', 'futurelearn.com', 'mindvalley.com',

        // News & Media Platforms
        'cnn.com', 'bbc.com', 'reuters.com', 'ap.org', 'npr.org',
        'pbs.org', 'cbsnews.com', 'abcnews.go.com', 'nbcnews.com', 'foxnews.com',
        'msnbc.com', 'usatoday.com', 'wsj.com', 'nytimes.com', 'washingtonpost.com',

        // International News
        'aljazeera.com', 'rt.com', 'dw.com', 'france24.com', 'euronews.com',
        'sputniknews.com', 'xinhuanet.com', 'globaltimes.cn', 'japantimes.co.jp', 'koreatimes.co.kr',
        'timesofindia.com', 'hindustantimes.com', 'thehindu.com', 'dawn.com', 'arabnews.com',

        // Technology News & Blogs
        'techcrunch.com', 'theverge.com', 'wired.com', 'arstechnica.com', 'engadget.com',
        'gizmodo.com', 'mashable.com', 'recode.net', 'venturebeat.com', 'readwrite.com',
        'gigaom.com', 'pandodaily.com', 'allthingsd.com', 'techmeme.com', 'slashdot.org',

        // Business & Finance News
        'bloomberg.com', 'cnbc.com', 'marketwatch.com', 'fool.com', 'seekingalpha.com',
        'morningstar.com', 'barrons.com', 'forbes.com', 'fortune.com', 'businessinsider.com',
        'fastcompany.com', 'inc.com', 'entrepreneur.com', 'harvard.edu', 'wharton.upenn.edu',

        // Scientific & Academic Journals
        'nature.com', 'science.org', 'cell.com', 'nejm.org', 'thelancet.com',
        'bmj.com', 'jama.jamanetwork.com', 'plos.org', 'frontiersin.org', 'mdpi.com',
        'springer.com', 'elsevier.com', 'wiley.com', 'taylor.com', 'sage.com',

        // Research & Academic Platforms
        'researchgate.net', 'academia.edu', 'mendeley.com', 'zotero.org', 'orcid.org',
        'publons.com', 'scopus.com', 'webofscience.com', 'googlescholar.com', 'semanticscholar.org',
        'arxiv.org', 'biorxiv.org', 'medrxiv.org', 'psyarxiv.com', 'socarxiv.org',

        // Government & Public Services
        'usa.gov', 'whitehouse.gov', 'congress.gov', 'senate.gov', 'house.gov',
        'supremecourt.gov', 'irs.gov', 'ssa.gov', 'medicare.gov', 'medicaid.gov',
        'va.gov', 'dol.gov', 'ed.gov', 'hhs.gov', 'dhs.gov',

        // International Government Sites
        'gov.uk', 'canada.ca', 'australia.gov.au', 'germany.travel', 'france.fr',
        'italy.it', 'spain.info', 'japan.go.jp', 'korea.kr', 'india.gov.in',
        'china.org.cn', 'russia.ru', 'brazil.gov.br', 'mexico.gob.mx', 'argentina.gob.ar',

        // Legal & Law Resources
        'justia.com', 'findlaw.com', 'martindale.com', 'avvo.com', 'lawyers.com',
        'nolo.com', 'legalzoom.com', 'rocketlawyer.com', 'lawdepot.com', 'uslegal.com',
        'law.com', 'americanbar.org', 'abajournal.com', 'lawreview.org', 'scotusblog.com',

        // Real Estate & Property
        'zillow.com', 'realtor.com', 'redfin.com', 'trulia.com', 'homes.com',
        'homefinder.com', 'move.com', 'rent.com', 'apartments.com', 'apartmentlist.com',
        'padmapper.com', 'hotpads.com', 'rentals.com', 'forrent.com', 'apartmentguide.com',

        // Travel & Tourism
        'expedia.com', 'booking.com', 'priceline.com', 'kayak.com', 'orbitz.com',
        'travelocity.com', 'cheaptickets.com', 'onetravel.com', 'momondo.com', 'skyscanner.com',
        'tripadvisor.com', 'yelp.com', 'foursquare.com', 'zomato.com', 'opentable.com',

        // Food & Restaurant Platforms
        'grubhub.com', 'doordash.com', 'ubereats.com', 'postmates.com', 'seamless.com',
        'caviar.com', 'eat24.com', 'delivery.com', 'foodpanda.com', 'justeat.com',
        'deliveroo.com', 'swiggy.com', 'zomato.com', 'yelp.com', 'opentable.com',

        // Health & Medical Platforms
        'webmd.com', 'mayoclinic.org', 'healthline.com', 'medlineplus.gov', 'nih.gov',
        'cdc.gov', 'who.int', 'fda.gov', 'drugs.com', 'rxlist.com',
        'medscape.com', 'uptodate.com', 'pubmed.ncbi.nlm.nih.gov', 'cochrane.org', 'bmj.com',

        // Fitness & Wellness
        'myfitnesspal.com', 'loseit.com', 'cronometer.com', 'fitbit.com', 'garmin.com',
        'strava.com', 'runkeeper.com', 'mapmyrun.com', 'endomondo.com', 'runtastic.com',
        'nike.com', 'adidas.com', 'underarmour.com', 'lululemon.com', 'peloton.com',

        // Mental Health & Meditation
        'headspace.com', 'calm.com', 'insight.com', 'ten.com', 'waking.com',
        'meditation.com', 'mindfulness.com', 'buddhify.com', 'stopbreathethink.com', 'smilingmind.com.au',
        'betterhelp.com', 'talkspace.com', 'cerebral.com', 'mdlive.com', 'amwell.com',

        // Dating & Relationships
        'match.com', 'eharmony.com', 'okcupid.com', 'pof.com', 'zoosk.com',
        'tinder.com', 'bumble.com', 'hinge.co', 'coffeemeetsbagel.com', 'happn.com',
        'badoo.com', 'meetme.com', 'skout.com', 'tagged.com', 'mingle2.com',

        // Photography & Visual Content
        'instagram.com', 'pinterest.com', 'flickr.com', '500px.com', 'smugmug.com',
        'photobucket.com', 'imgur.com', 'giphy.com', 'tenor.com', 'gfycat.com',
        'unsplash.com', 'pexels.com', 'pixabay.com', 'freepik.com', 'shutterstock.com',

        // Video Creation & Editing
        'youtube.com', 'vimeo.com', 'wistia.com', 'brightcove.com', 'jwplayer.com',
        'vidyard.com', 'loom.com', 'screencast.com', 'camtasia.com', 'snagit.com',
        'obs.live', 'streamlabs.com', 'xsplit.com', 'nvidia.com', 'amd.com',

        // Productivity & Time Management
        'todoist.com', 'any.do', 'wunderlist.com', 'remember.com', 'evernote.com',
        'onenote.com', 'simplenote.com', 'bear.app', 'ulysses.app', 'ia.net',
        'typora.io', 'marktext.app', 'zettlr.com', 'obsidian.md', 'roamresearch.com',

        // File Storage & Sync
        'dropbox.com', 'box.com', 'onedrive.com', 'googledrive.com', 'icloud.com',
        'mega.nz', 'pcloud.com', 'sync.com', 'tresorit.com', 'spideroak.com',
        'backblaze.com', 'carbonite.com', 'crashplan.com', 'mozy.com', 'sugarsync.com',

        // Communication & Messaging
        'slack.com', 'discord.com', 'teams.microsoft.com', 'zoom.us', 'skype.com',
        'telegram.org', 'whatsapp.com', 'signal.org', 'wickr.com', 'threema.ch',
        'wire.com', 'element.io', 'riot.im', 'keybase.io', 'session.com'
    ];

    const isSPASite = spaSites.some(site => window.location.hostname.includes(site));

    if (!isSPASite) {
        console.log("Stickara: Not a SPA site, skipping URL context reload");
        return;
    }

    // Clear the current notes cache to force reload from storage
    const wasVisible = noteContainer && noteContainer.classList.contains('visible');
    const currentIndex = currentNoteIndex;

    console.log(`Stickara: Saving current note ${currentIndex} before URL context switch`);

    // Save current note before switching context
    if (typeof saveNote === 'function') {
        saveNote();
    }

    // Clear notes cache to force reload
    console.log("Stickara: Clearing notes cache for URL context reload");
    notes = {};

    // Reload state and notes for the new URL context
    // This will automatically load the correct notes for the new URL
    if (typeof loadState === 'function') {
        console.log("Stickara: Loading state for new URL context");
        loadState();

        // Restore visibility if the note was visible before
        if (wasVisible) {
            setTimeout(() => {
                if (typeof showNote === 'function') {
                    console.log("Stickara: Restoring note visibility after URL context reload");
                    showNote();
                }
            }, 200);
        }
    } else {
        console.error("Stickara: loadState function not available for URL context reload");
    }

    console.log("Stickara: URL context reload completed");
}

console.log("Stickara: Storage Logic Loaded (v7 - Calendar Event ID)"); // Updated log
// --- END OF FILE content-storage.js ---