/**
 * Stickara Inline Worker Scripts
 * This file contains inline worker scripts that can be used as a fallback
 * when external worker scripts cannot be loaded.
 */

// Define the inline worker scripts
window.StickaraInlineWorkers = {
    // Storage worker script
    'storage-worker': `
        /**
         * Stickara Storage Worker
         * Handles background storage operations to prevent UI blocking
         */
        
        // Set up worker context
        self.isWorkerContext = true;
        
        // Send ready status to main thread
        self.postMessage({ status: 'ready' });
        
        // Set up message handler
        self.onmessage = function(event) {
            const { taskId, action, data } = event.data;
            
            try {
                // Process the task based on the action
                switch (action) {
                    case 'compressData':
                        handleCompressData(taskId, data);
                        break;
                        
                    case 'decompressData':
                        handleDecompressData(taskId, data);
                        break;
                        
                    case 'encryptData':
                        handleEncryptData(taskId, data);
                        break;
                        
                    case 'decryptData':
                        handleDecryptData(taskId, data);
                        break;
                        
                    case 'processNoteData':
                        handleProcessNoteData(taskId, data);
                        break;
                        
                    case 'processHighlightData':
                        handleProcessHighlightData(taskId, data);
                        break;
                        
                    case 'batchProcess':
                        handleBatchProcess(taskId, data);
                        break;
                        
                    default:
                        throw new Error(\`Unknown action: \${action}\`);
                }
            } catch (error) {
                // Send error back to main thread
                self.postMessage({
                    taskId,
                    error: error.message || 'Unknown error in storage worker'
                });
            }
        };
        
        /**
         * Processes note data (validation, normalization, etc.)
         * @param {string} taskId - The ID of the task
         * @param {Object} data - The note data to process
         */
        function handleProcessNoteData(taskId, data) {
            try {
                const { noteData, options } = data;
                
                // Clone the note data to avoid modifying the original
                const processedData = JSON.parse(JSON.stringify(noteData));
                
                // Add timestamp if not present
                if (!processedData.timestamp) {
                    processedData.timestamp = Date.now();
                }
                
                // Update last modified timestamp
                processedData.lastModified = Date.now();
                
                // Generate a word count if text is present
                if (processedData.text) {
                    processedData.wordCount = processedData.text.split(/\\s+/).filter(Boolean).length;
                }
                
                // Simulate processing work
                for (let i = 0; i < 10000; i++) {
                    // Minimal busy work to simulate CPU-intensive task
                    Math.sqrt(i);
                }
                
                // Send result back to main thread
                self.postMessage({
                    taskId,
                    result: {
                        processedData,
                        success: true
                    }
                });
            } catch (error) {
                self.postMessage({
                    taskId,
                    error: \`Note processing error: \${error.message}\`
                });
            }
        }
        
        /**
         * Processes highlight data
         * @param {string} taskId - The ID of the task
         * @param {Object} data - The highlight data to process
         */
        function handleProcessHighlightData(taskId, data) {
            try {
                const { highlightData, options } = data;
                
                // Clone the highlight data to avoid modifying the original
                const processedData = JSON.parse(JSON.stringify(highlightData));
                
                // Add timestamp if not present
                if (!processedData.timestamp) {
                    processedData.timestamp = Date.now();
                }
                
                // Update last modified timestamp
                processedData.lastModified = Date.now();
                
                // Send result back to main thread
                self.postMessage({
                    taskId,
                    result: {
                        processedData,
                        success: true
                    }
                });
            } catch (error) {
                self.postMessage({
                    taskId,
                    error: \`Highlight processing error: \${error.message}\`
                });
            }
        }
        
        // Stub implementations for other methods
        function handleCompressData(taskId, data) {
            self.postMessage({
                taskId,
                result: { compressed: data.input, success: true }
            });
        }
        
        function handleDecompressData(taskId, data) {
            self.postMessage({
                taskId,
                result: { decompressed: data.input, success: true }
            });
        }
        
        function handleEncryptData(taskId, data) {
            self.postMessage({
                taskId,
                result: { encrypted: data.input, success: true }
            });
        }
        
        function handleDecryptData(taskId, data) {
            self.postMessage({
                taskId,
                result: { decrypted: data.input, success: true }
            });
        }
        
        function handleBatchProcess(taskId, data) {
            self.postMessage({
                taskId,
                result: { results: data.items, count: data.items.length, success: true }
            });
        }
    `,
    
    // Image worker script
    'image-worker': `
        /**
         * Stickara Image Processing Worker
         * Handles background image processing operations to prevent UI blocking
         */
        
        // Set up worker context
        self.isWorkerContext = true;
        
        // Send ready status to main thread
        self.postMessage({ status: 'ready' });
        
        // Set up message handler
        self.onmessage = function(event) {
            const { taskId, action, data } = event.data;
            
            try {
                // Process the task based on the action
                switch (action) {
                    case 'processImage':
                        // Just return the original image data for the inline version
                        self.postMessage({
                            taskId,
                            result: {
                                processedDataUrl: data.imageDataUrl,
                                success: true
                            }
                        });
                        break;
                        
                    case 'resizeImage':
                        // Just return the original image data for the inline version
                        self.postMessage({
                            taskId,
                            result: {
                                resizedDataUrl: data.imageDataUrl,
                                width: data.width || 100,
                                height: data.height || 100,
                                success: true
                            }
                        });
                        break;
                        
                    case 'convertFormat':
                        // Just return the original image data for the inline version
                        self.postMessage({
                            taskId,
                            result: {
                                convertedDataUrl: data.imageDataUrl,
                                format: data.format || 'image/png',
                                success: true
                            }
                        });
                        break;
                        
                    default:
                        throw new Error(\`Unknown action: \${action}\`);
                }
            } catch (error) {
                // Send error back to main thread
                self.postMessage({
                    taskId,
                    error: error.message || 'Unknown error in image worker'
                });
            }
        };
    `,
    
    // Data worker script
    'data-worker': `
        /**
         * Stickara Data Processing Worker
         * Handles background data processing operations to prevent UI blocking
         */
        
        // Set up worker context
        self.isWorkerContext = true;
        
        // Send ready status to main thread
        self.postMessage({ status: 'ready' });
        
        // Set up message handler
        self.onmessage = function(event) {
            const { taskId, action, data } = event.data;
            
            try {
                // Process the task based on the action
                switch (action) {
                    case 'searchNotes':
                        // Simple implementation for inline worker
                        const results = data.notes.filter(note => 
                            note.text && note.text.includes(data.query)
                        );
                        
                        self.postMessage({
                            taskId,
                            result: {
                                results,
                                count: results.length,
                                query: data.query
                            }
                        });
                        break;
                        
                    default:
                        throw new Error(\`Unknown action: \${action}\`);
                }
            } catch (error) {
                // Send error back to main thread
                self.postMessage({
                    taskId,
                    error: error.message || 'Unknown error in data worker'
                });
            }
        };
    `
};

console.log("Stickara: Inline Workers Loaded");
