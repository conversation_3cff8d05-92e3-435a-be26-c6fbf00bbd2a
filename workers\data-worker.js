/**
 * Stickara Data Processing Worker
 * Handles background data processing operations to prevent UI blocking
 */

// Set up worker context
self.isWorkerContext = true;

// Send ready status to main thread
self.postMessage({ status: 'ready' });

// Set up message handler
self.onmessage = function(event) {
    const { taskId, action, data } = event.data;

    try {
        // Process the task based on the action
        switch (action) {
            case 'searchNotes':
                handleSearchNotes(taskId, data);
                break;

            case 'filterHighlights':
                handleFilterHighlights(taskId, data);
                break;

            case 'generateStatistics':
                handleGenerateStatistics(taskId, data);
                break;

            case 'extractKeywords':
                handleExtractKeywords(taskId, data);
                break;





            default:
                throw new Error(`Unknown action: ${action}`);
        }
    } catch (error) {
        // Send error back to main thread
        self.postMessage({
            taskId,
            error: error.message || 'Unknown error in data worker'
        });
    }
};

/**
 * Searches notes based on query
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The data for the task
 */
function handleSearchNotes(taskId, data) {
    try {
        const { notes, query, options } = data;

        // Default options
        const searchOptions = {
            caseSensitive: false,
            wholeWord: false,
            includeTitle: true,
            includeText: true,
            includeTags: true,
            ...options
        };

        // Prepare query
        const searchQuery = searchOptions.caseSensitive ? query : query.toLowerCase();

        // Search notes
        const results = notes.filter(note => {
            // Check title
            if (searchOptions.includeTitle && note.title) {
                const title = searchOptions.caseSensitive ? note.title : note.title.toLowerCase();
                if (title.includes(searchQuery)) {
                    return true;
                }
            }

            // Check text
            if (searchOptions.includeText && note.text) {
                const text = searchOptions.caseSensitive ? note.text : note.text.toLowerCase();
                if (text.includes(searchQuery)) {
                    return true;
                }
            }

            // Check tags
            if (searchOptions.includeTags && note.tags) {
                const tags = searchOptions.caseSensitive ? note.tags : note.tags.toLowerCase();
                if (tags.includes(searchQuery)) {
                    return true;
                }
            }

            return false;
        });

        // Simulate intensive processing
        for (let i = 0; i < 500000; i++) {
            // Busy work to simulate CPU-intensive task
            Math.sqrt(i);
        }

        // Send results back to main thread
        self.postMessage({
            taskId,
            result: {
                results,
                count: results.length,
                query: query
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Search error: ${error.message}`
        });
    }
}

/**
 * Filters highlights based on criteria
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The data for the task
 */
function handleFilterHighlights(taskId, data) {
    try {
        const { highlights, filters, options } = data;

        // Apply filters
        let filteredHighlights = [...highlights];

        // Filter by color
        if (filters.color) {
            filteredHighlights = filteredHighlights.filter(highlight =>
                highlight.color === filters.color
            );
        }

        // Filter by date range
        if (filters.dateRange) {
            const { start, end } = filters.dateRange;
            filteredHighlights = filteredHighlights.filter(highlight => {
                const timestamp = highlight.timestamp || 0;
                return timestamp >= start && timestamp <= end;
            });
        }

        // Filter by URL
        if (filters.url) {
            filteredHighlights = filteredHighlights.filter(highlight =>
                highlight.url && highlight.url.includes(filters.url)
            );
        }

        // Filter by text content
        if (filters.text) {
            const searchText = options?.caseSensitive ? filters.text : filters.text.toLowerCase();
            filteredHighlights = filteredHighlights.filter(highlight => {
                const text = options?.caseSensitive ? highlight.text : highlight.text.toLowerCase();
                return text.includes(searchText);
            });
        }

        // Filter by has note
        if (filters.hasNote !== undefined) {
            filteredHighlights = filteredHighlights.filter(highlight =>
                Boolean(highlight.hasLinkedNote) === filters.hasNote
            );
        }

        // Simulate intensive processing
        for (let i = 0; i < 500000; i++) {
            // Busy work to simulate CPU-intensive task
            Math.sqrt(i);
        }

        // Send results back to main thread
        self.postMessage({
            taskId,
            result: {
                filteredHighlights,
                count: filteredHighlights.length,
                filters: filters
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Filter error: ${error.message}`
        });
    }
}

/**
 * Generates statistics from notes and highlights
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The data for the task
 */
function handleGenerateStatistics(taskId, data) {
    try {
        const { notes, highlights, options } = data;

        // Initialize statistics object
        const statistics = {
            notes: {
                total: notes.length,
                byNotebook: {},
                byTag: {},
                wordCount: 0,
                averageLength: 0,
                withReminders: 0
            },
            highlights: {
                total: highlights.length,
                byColor: {},
                withNotes: 0,
                byDomain: {}
            },
            timeStats: {
                oldestNote: null,
                newestNote: null,
                mostActiveDay: null,
                mostActiveHour: null
            }
        };

        // Process notes
        let totalWords = 0;
        const noteDates = {};
        const noteHours = {};

        notes.forEach(note => {
            // Count by notebook
            const notebook = note.notebook || 'Uncategorized';
            statistics.notes.byNotebook[notebook] = (statistics.notes.byNotebook[notebook] || 0) + 1;

            // Count by tag
            if (note.tags) {
                const tags = note.tags.split(',').map(tag => tag.trim()).filter(Boolean);
                tags.forEach(tag => {
                    statistics.notes.byTag[tag] = (statistics.notes.byTag[tag] || 0) + 1;
                });
            }

            // Count words
            if (note.text) {
                const words = note.text.split(/\s+/).filter(Boolean).length;
                totalWords += words;
            }

            // Count notes with reminders
            if (note.reminder) {
                statistics.notes.withReminders++;
            }

            // Track dates for time statistics
            if (note.timestamp) {
                const date = new Date(note.timestamp);
                const dateString = date.toISOString().split('T')[0];
                const hour = date.getHours();

                noteDates[dateString] = (noteDates[dateString] || 0) + 1;
                noteHours[hour] = (noteHours[hour] || 0) + 1;

                // Track oldest and newest
                if (!statistics.timeStats.oldestNote || note.timestamp < statistics.timeStats.oldestNote) {
                    statistics.timeStats.oldestNote = note.timestamp;
                }
                if (!statistics.timeStats.newestNote || note.timestamp > statistics.timeStats.newestNote) {
                    statistics.timeStats.newestNote = note.timestamp;
                }
            }
        });

        // Calculate average note length
        statistics.notes.wordCount = totalWords;
        statistics.notes.averageLength = notes.length > 0 ? totalWords / notes.length : 0;

        // Find most active day and hour
        let maxDayCount = 0;
        let maxHourCount = 0;

        for (const date in noteDates) {
            if (noteDates[date] > maxDayCount) {
                maxDayCount = noteDates[date];
                statistics.timeStats.mostActiveDay = date;
            }
        }

        for (const hour in noteHours) {
            if (noteHours[hour] > maxHourCount) {
                maxHourCount = noteHours[hour];
                statistics.timeStats.mostActiveHour = parseInt(hour);
            }
        }

        // Process highlights
        const domains = {};

        highlights.forEach(highlight => {
            // Count by color
            const color = highlight.color || 'default';
            statistics.highlights.byColor[color] = (statistics.highlights.byColor[color] || 0) + 1;

            // Count highlights with notes
            if (highlight.hasLinkedNote) {
                statistics.highlights.withNotes++;
            }

            // Count by domain
            if (highlight.url) {
                try {
                    const url = new URL(highlight.url);
                    const domain = url.hostname;
                    domains[domain] = (domains[domain] || 0) + 1;
                } catch (e) {
                    // Invalid URL, ignore
                }
            }
        });

        // Sort domains by count and get top 10
        statistics.highlights.byDomain = Object.entries(domains)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10)
            .reduce((obj, [domain, count]) => {
                obj[domain] = count;
                return obj;
            }, {});

        // Simulate intensive processing
        for (let i = 0; i < 1000000; i++) {
            // Busy work to simulate CPU-intensive task
            Math.sqrt(i);
        }

        // Send results back to main thread
        self.postMessage({
            taskId,
            result: {
                statistics
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Statistics generation error: ${error.message}`
        });
    }
}

/**
 * Extracts keywords from text
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The data for the task
 */
function handleExtractKeywords(taskId, data) {
    try {
        const { text, options } = data;

        // Default options
        const extractOptions = {
            maxKeywords: 10,
            minLength: 3,
            stopWords: ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'like', 'through', 'over', 'before', 'between', 'after', 'since', 'without', 'under', 'within', 'along', 'following', 'across', 'behind', 'beyond', 'plus', 'except', 'but', 'up', 'out', 'around', 'down', 'off', 'above', 'near', 'a', 'an', 'the', 'this', 'that', 'these', 'those', 'my', 'your', 'his', 'her', 'its', 'our', 'their', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'shall', 'should', 'can', 'could', 'may', 'might', 'must', 'of'],
            ...options
        };

        // Tokenize the text
        const words = text.toLowerCase()
            .replace(/[^\w\s]/g, '') // Remove punctuation
            .split(/\s+/) // Split by whitespace
            .filter(word =>
                word.length >= extractOptions.minLength &&
                !extractOptions.stopWords.includes(word)
            );

        // Count word frequencies
        const wordCounts = {};
        words.forEach(word => {
            wordCounts[word] = (wordCounts[word] || 0) + 1;
        });

        // Sort by frequency and get top keywords
        const keywords = Object.entries(wordCounts)
            .sort((a, b) => b[1] - a[1])
            .slice(0, extractOptions.maxKeywords)
            .map(([word, count]) => ({
                word,
                count,
                score: count / words.length
            }));

        // Simulate intensive processing
        for (let i = 0; i < 1000000; i++) {
            // Busy work to simulate CPU-intensive task
            Math.sqrt(i);
        }

        // Send results back to main thread
        self.postMessage({
            taskId,
            result: {
                keywords,
                totalWords: words.length,
                uniqueWords: Object.keys(wordCounts).length
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Keyword extraction error: ${error.message}`
        });
    }
}





